﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="commentStatus" Type="OutArgument(x:String)" />
    <x:Property Name="Status" Type="OutArgument(x:String)" />
    <x:Property Name="logfile" Type="InArgument(x:String)" />
    <x:Property Name="M3TotalTableRows" Type="InArgument(scg:List(s:String[]))" />
    <x:Property Name="inbnValue" Type="InArgument(x:String)" />
    <x:Property Name="division" Type="InArgument(x:String)" />
    <x:Property Name="charge" Type="InArgument(x:String)" />
    <x:Property Name="chargeCode" Type="InArgument(x:String)" />
    <x:Property Name="chargeDiff" Type="OutArgument(x:String)" />
    <x:Property Name="DictOcrValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>System</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_1">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="req2" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj2" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode2" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj8" />
      <Variable x:TypeArguments="x:String" Name="puno" />
      <Variable x:TypeArguments="x:String" Name="pnli" />
      <Variable x:TypeArguments="x:Decimal" Name="tot" />
      <Variable x:TypeArguments="x:Decimal" Name="totqty" />
      <Variable x:TypeArguments="x:String" Name="ceid" />
      <Variable x:TypeArguments="x:String" Name="cdse" />
      <Variable x:TypeArguments="x:String" Name="grpr" />
      <Variable x:TypeArguments="x:Decimal" Name="totqty1" />
      <Variable x:TypeArguments="x:String" Name="ivqa" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode8" />
    </Sequence.Variables>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_21">
      <Assign.To>
        <OutArgument x:TypeArguments="x:Decimal">[tot]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:Decimal">[0]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_37">
      <Assign.To>
        <OutArgument x:TypeArguments="x:Decimal">[totqty]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:Decimal">[0]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_68">
      <Assign.To>
        <OutArgument x:TypeArguments="x:Decimal">[totqty1]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:Decimal">[0]</InArgument>
      </Assign.Value>
    </Assign>
    <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_3" Values="[M3TotalTableRows]">
      <ActivityAction x:TypeArguments="s:String[]">
        <ActivityAction.Argument>
          <DelegateInArgument x:TypeArguments="s:String[]" Name="rows" />
        </ActivityAction.Argument>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_4">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_17">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[puno]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[rows(6)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_18">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[pnli]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[rows(0)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_19">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["F3RCAC,F3CDSE,F3CEID from FGRPCL where F3PUNO = " + puno +" and F3SCOC != 0.000000 and F3PNLI = " + pnli + " and F3DIVI = " + division]</InArgument>
            </Assign.Value>
          </Assign>
          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Export MI lines IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_5" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/EXPORTMI/Select?HDRS=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
            <iai:IONAPIRequestWizard.Headers>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>Accept</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>application/json</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.Headers>
            <iai:IONAPIRequestWizard.QueryParameters>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>QERY</x:String>
                  <x:String>SEPC</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>req2</x:String>
                  <x:String>~</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.QueryParameters>
          </iai:IONAPIRequestWizard>
          <If Condition="[StatusCode2 = 200]" sap2010:WorkflowViewState.IdRef="If_1">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_10">
                <Sequence.Variables>
                  <Variable x:TypeArguments="njl:JToken" Name="out2" />
                </Sequence.Variables>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_10">
                  <Assign.To>
                    <OutArgument x:TypeArguments="njl:JToken">[out2]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[out2(&quot;results&quot;)(0)(&quot;records&quot;).ToString &lt;&gt; &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_7">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_11">
                      <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_6" Values="[out2(&quot;results&quot;)(0)(&quot;records&quot;)]">
                        <ActivityAction x:TypeArguments="njl:JToken">
                          <ActivityAction.Argument>
                            <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
                          </ActivityAction.Argument>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_20">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:Decimal">[tot]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:Decimal">[tot + Convert.toDecimal(item("REPL").ToString.split("~"C)(0))]</InArgument>
                            </Assign.Value>
                          </Assign>
                        </ActivityAction>
                      </ForEach>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_36">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Decimal">[totqty]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:Decimal">[totqty +Convert.ToDecimal(rows(3))]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                  </If.Then>
                  <If.Else>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_67">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:Decimal">[totqty1]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:Decimal">[totqty1 +Convert.ToDecimal(rows(3))]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </If.Else>
                </If>
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_2">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_5">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching lines charges for the PO " + puno +"."]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_6">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="[commentStatus]" Source="[logfile]" />
              </Sequence>
            </If.Else>
          </If>
        </Sequence>
      </ActivityAction>
    </ForEach>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_80">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[chargeDiff]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[(Convert.ToDecimal(charge) - Convert.ToDecimal(tot)).ToString]</InArgument>
      </Assign.Value>
    </Assign>
    <If Condition="[tot = Convert.toDecimal(charge) OR tot &gt; Convert.toDecimal(charge) OR tot &lt; Convert.toDecimal(charge)]" sap2010:WorkflowViewState.IdRef="If_2">
      <If.Then>
        <If Condition="[tot = 0 and False]" sap2010:WorkflowViewState.IdRef="If_30">
          <If.Then>
            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_41">
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_40">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_106">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[ceid]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[chargeCode]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_107">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[cdse]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">60</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_108">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[grpr]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[Math.Round(((Convert.toDecimal(charge))/totqty),4).ToString]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_109">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[ivqa]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[totqty.ToString]</InArgument>
                  </Assign.Value>
                </Assign>
              </Sequence>
              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_18" Response="[respObj8]" StatusCode="[StatusCode8]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                <iai:IONAPIRequestWizard.Headers>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>Accept</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>application/json</x:String>
                    </scg:List>
                  </scg:List>
                </iai:IONAPIRequestWizard.Headers>
                <iai:IONAPIRequestWizard.QueryParameters>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="8">
                      <x:String>INBN</x:String>
                      <x:String>RDTP</x:String>
                      <x:String>DIVI</x:String>
                      <x:String>CDSE</x:String>
                      <x:String>CEID</x:String>
                      <x:String>NLAM</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="8">
                      <x:String>inbnValue</x:String>
                      <x:String>2</x:String>
                      <x:String>division</x:String>
                      <x:String>cdse</x:String>
                      <x:String>ceid</x:String>
                      <x:String>charge</x:String>
                    </scg:List>
                  </scg:List>
                </iai:IONAPIRequestWizard.QueryParameters>
              </iai:IONAPIRequestWizard>
            </Sequence>
          </If.Then>
          <If.Else>
            <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_13" Values="[M3TotalTableRows]">
              <ActivityAction x:TypeArguments="s:String[]">
                <ActivityAction.Argument>
                  <DelegateInArgument x:TypeArguments="s:String[]" Name="rows" />
                </ActivityAction.Argument>
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_54">
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_110">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[puno]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[rows(6)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_111">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[pnli]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[rows(0)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_112">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">["F3SCOC,F3CDSE,F3CEID from FGRPCL where F3PUNO = " + puno +" and F3SCOC != 0.000000 and F3PNLI = " + pnli + " and F3DIVI = " + division]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Export MI lines IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_19" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/EXPORTMI/Select?HDRS=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
                    <iai:IONAPIRequestWizard.Headers>
                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                        <scg:List x:TypeArguments="x:String" Capacity="4">
                          <x:String>Accept</x:String>
                        </scg:List>
                        <scg:List x:TypeArguments="x:String" Capacity="4">
                          <x:String>application/json</x:String>
                        </scg:List>
                      </scg:List>
                    </iai:IONAPIRequestWizard.Headers>
                    <iai:IONAPIRequestWizard.QueryParameters>
                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                        <scg:List x:TypeArguments="x:String" Capacity="4">
                          <x:String>QERY</x:String>
                          <x:String>SEPC</x:String>
                        </scg:List>
                        <scg:List x:TypeArguments="x:String" Capacity="4">
                          <x:String>req2</x:String>
                          <x:String>~</x:String>
                        </scg:List>
                      </scg:List>
                    </iai:IONAPIRequestWizard.QueryParameters>
                  </iai:IONAPIRequestWizard>
                  <If Condition="[StatusCode2 = 200]" sap2010:WorkflowViewState.IdRef="If_29">
                    <If.Then>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_52">
                        <Sequence.Variables>
                          <Variable x:TypeArguments="x:String" Name="itno" />
                          <Variable x:TypeArguments="x:String" Name="repn" />
                        </Sequence.Variables>
                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_51">
                          <Sequence.Variables>
                            <Variable x:TypeArguments="njl:JToken" Name="out2" />
                          </Sequence.Variables>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_113">
                            <Assign.To>
                              <OutArgument x:TypeArguments="njl:JToken">[out2]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign DisplayName="Assign itno" sap2010:WorkflowViewState.IdRef="Assign_114">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[itno]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[rows(5)]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_115">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[repn]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[rows(1)]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_116">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[ivqa]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[rows(3)]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <If Condition="[out2(&quot;results&quot;)(0)(&quot;records&quot;).ToString &lt;&gt; &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_28">
                            <If.Then>
                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_44">
                                <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_12" Values="[out2(&quot;results&quot;)(0)(&quot;records&quot;)]">
                                  <ActivityAction x:TypeArguments="njl:JToken">
                                    <ActivityAction.Argument>
                                      <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
                                    </ActivityAction.Argument>
                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_43">
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_117">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[cdse]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[item("REPL").ToString.split("~"C)(1).ToString()]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_118">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[ceid]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[item("REPL").ToString.split("~"C)(2).ToString()]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <If Condition="[tot = Convert.toDecimal(charge)]" sap2010:WorkflowViewState.IdRef="If_23">
                                        <If.Then>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_119">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[grpr]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[Math.Round((Convert.toDecimal(item("REPL").ToString.split("~"C)(0))),4).ToString]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                        </If.Then>
                                        <If.Else>
                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_42">
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_120">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[grpr]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">[Math.Round(((Convert.toDecimal(item("REPL").ToString.split("~"C)(0))) - (tot - Convert.toDecimal(charge))/totqty),4).ToString()]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                          </Sequence>
                                        </If.Else>
                                      </If>
                                    </Sequence>
                                  </ActivityAction>
                                </ForEach>
                                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_20" Response="[respObj8]" StatusCode="[StatusCode8]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                  <iai:IONAPIRequestWizard.Headers>
                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                        <x:String>Accept</x:String>
                                      </scg:List>
                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                        <x:String>application/json</x:String>
                                      </scg:List>
                                    </scg:List>
                                  </iai:IONAPIRequestWizard.Headers>
                                  <iai:IONAPIRequestWizard.QueryParameters>
                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                      <scg:List x:TypeArguments="x:String" Capacity="16">
                                        <x:String>INBN</x:String>
                                        <x:String>RDTP</x:String>
                                        <x:String>DIVI</x:String>
                                        <x:String>CDSE</x:String>
                                        <x:String>CEID</x:String>
                                        <x:String>ITNO</x:String>
                                        <x:String>PNLI</x:String>
                                        <x:String>PUNO</x:String>
                                        <x:String>REPN</x:String>
                                        <x:String>IVQA</x:String>
                                        <x:String>GRPR</x:String>
                                      </scg:List>
                                      <scg:List x:TypeArguments="x:String" Capacity="16">
                                        <x:String>inbnValue</x:String>
                                        <x:String>5</x:String>
                                        <x:String>division</x:String>
                                        <x:String>cdse</x:String>
                                        <x:String>ceid</x:String>
                                        <x:String>itno</x:String>
                                        <x:String>pnli</x:String>
                                        <x:String>puno</x:String>
                                        <x:String>repn</x:String>
                                        <x:String>ivqa</x:String>
                                        <x:String>grpr</x:String>
                                      </scg:List>
                                    </scg:List>
                                  </iai:IONAPIRequestWizard.QueryParameters>
                                </iai:IONAPIRequestWizard>
                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_16" Line="Line charge added" Source="[logfile]" />
                              </Sequence>
                            </If.Then>
                            <If.Else>
                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_50">
                                <If Condition="[tot = 0]" sap2010:WorkflowViewState.IdRef="If_27">
                                  <If.Then>
                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_49">
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_121">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[ceid]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[chargeCode]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_122">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">["F3CDSE from FGRPCL where F3CEID = '" + ceid+"'"]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Export MI lines IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_21" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/EXPORTMI/Select?HDRS=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
                                        <iai:IONAPIRequestWizard.Headers>
                                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                            <scg:List x:TypeArguments="x:String" Capacity="4">
                                              <x:String>Accept</x:String>
                                            </scg:List>
                                            <scg:List x:TypeArguments="x:String" Capacity="4">
                                              <x:String>application/json</x:String>
                                            </scg:List>
                                          </scg:List>
                                        </iai:IONAPIRequestWizard.Headers>
                                        <iai:IONAPIRequestWizard.QueryParameters>
                                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                            <scg:List x:TypeArguments="x:String" Capacity="4">
                                              <x:String>QERY</x:String>
                                              <x:String>SEPC</x:String>
                                            </scg:List>
                                            <scg:List x:TypeArguments="x:String" Capacity="4">
                                              <x:String>req2</x:String>
                                              <x:String>~</x:String>
                                            </scg:List>
                                          </scg:List>
                                        </iai:IONAPIRequestWizard.QueryParameters>
                                      </iai:IONAPIRequestWizard>
                                      <If Condition="[StatusCode2 = 200]" sap2010:WorkflowViewState.IdRef="If_26">
                                        <If.Then>
                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_47">
                                            <Sequence.Variables>
                                              <Variable x:TypeArguments="njl:JToken" Name="out2" />
                                            </Sequence.Variables>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_123">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="njl:JToken">[out2]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <If Condition="[out2(&quot;results&quot;)(0)(&quot;records&quot;).ToString &lt;&gt; &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_24">
                                              <If.Then>
                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_45">
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_124">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[cdse]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">[out2("results")(0)("records")(0)("REPL").ToString.split("~"C)(0)]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                </Sequence>
                                              </If.Then>
                                              <If.Else>
                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_17" Line="[&quot;No record available for the charge code: &quot; + ceid]" Source="[logfile]" />
                                              </If.Else>
                                            </If>
                                            <If Condition="[cdse &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_25">
                                              <If.Then>
                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_46">
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_125">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[cdse]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">[out2("results")(0)("records")(0)("REPL").ToString.split("~"C)(0)]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_126">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[grpr]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">[Math.Round((Convert.toDecimal(charge))/totqty1,4).ToString()]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_22" Response="[respObj8]" StatusCode="[StatusCode8]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                    <iai:IONAPIRequestWizard.Headers>
                                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                                          <x:String>Accept</x:String>
                                                        </scg:List>
                                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                                          <x:String>application/json</x:String>
                                                        </scg:List>
                                                      </scg:List>
                                                    </iai:IONAPIRequestWizard.Headers>
                                                    <iai:IONAPIRequestWizard.QueryParameters>
                                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                        <scg:List x:TypeArguments="x:String" Capacity="16">
                                                          <x:String>INBN</x:String>
                                                          <x:String>RDTP</x:String>
                                                          <x:String>DIVI</x:String>
                                                          <x:String>CDSE</x:String>
                                                          <x:String>CEID</x:String>
                                                          <x:String>ITNO</x:String>
                                                          <x:String>PNLI</x:String>
                                                          <x:String>PUNO</x:String>
                                                          <x:String>REPN</x:String>
                                                          <x:String>IVQA</x:String>
                                                          <x:String>GRPR</x:String>
                                                        </scg:List>
                                                        <scg:List x:TypeArguments="x:String" Capacity="16">
                                                          <x:String>inbnValue</x:String>
                                                          <x:String>5</x:String>
                                                          <x:String>division</x:String>
                                                          <x:String>cdse</x:String>
                                                          <x:String>ceid</x:String>
                                                          <x:String>itno</x:String>
                                                          <x:String>pnli</x:String>
                                                          <x:String>puno</x:String>
                                                          <x:String>repn</x:String>
                                                          <x:String>ivqa</x:String>
                                                          <x:String>grpr</x:String>
                                                        </scg:List>
                                                      </scg:List>
                                                    </iai:IONAPIRequestWizard.QueryParameters>
                                                  </iai:IONAPIRequestWizard>
                                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_18" Line="Line charge added" Source="[logfile]" />
                                                </Sequence>
                                              </If.Then>
                                              <If.Else>
                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_19" Line="[&quot;No record available for the charge code: &quot; + ceid]" Source="[logfile]" />
                                              </If.Else>
                                            </If>
                                          </Sequence>
                                        </If.Then>
                                        <If.Else>
                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_48">
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_127">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching lines charges for the PO " + puno +"."]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_128">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_20" Line="[commentStatus]" Source="[logfile]" />
                                          </Sequence>
                                        </If.Else>
                                      </If>
                                    </Sequence>
                                  </If.Then>
                                </If>
                              </Sequence>
                            </If.Else>
                          </If>
                        </Sequence>
                      </Sequence>
                    </If.Then>
                    <If.Else>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_53">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_129">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching lines charges for the PO " + puno +"."]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_130">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_21" Line="[commentStatus]" Source="[logfile]" />
                      </Sequence>
                    </If.Else>
                  </If>
                </Sequence>
              </ActivityAction>
            </ForEach>
          </If.Else>
        </If>
      </If.Then>
    </If>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_21" sap:VirtualizedContainerService.HintSize="294.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_37" sap:VirtualizedContainerService.HintSize="294.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_68" sap:VirtualizedContainerService.HintSize="294.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_17" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_18" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_19" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_5" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_20" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="ForEach`1_6" sap:VirtualizedContainerService.HintSize="284.666666666667,214.666666666667" />
      <sap2010:ViewStateData Id="Assign_36" sap:VirtualizedContainerService.HintSize="284.666666666667,62" />
      <sap2010:ViewStateData Id="Sequence_11" sap:VirtualizedContainerService.HintSize="306.666666666667,440.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_67" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_7" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_10" sap:VirtualizedContainerService.HintSize="264,278.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_4" sap:VirtualizedContainerService.HintSize="264,544.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_3" sap:VirtualizedContainerService.HintSize="294.666666666667,697.333333333333" />
      <sap2010:ViewStateData Id="Assign_80" sap:VirtualizedContainerService.HintSize="294.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_106" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_107" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_108" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_109" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_40" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_18" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_41" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_110" sap:VirtualizedContainerService.HintSize="576,62" />
      <sap2010:ViewStateData Id="Assign_111" sap:VirtualizedContainerService.HintSize="576,62" />
      <sap2010:ViewStateData Id="Assign_112" sap:VirtualizedContainerService.HintSize="576,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_19" sap:VirtualizedContainerService.HintSize="576,22" />
      <sap2010:ViewStateData Id="Assign_113" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_114" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_115" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_116" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_117" sap:VirtualizedContainerService.HintSize="532,62" />
      <sap2010:ViewStateData Id="Assign_118" sap:VirtualizedContainerService.HintSize="532,62" />
      <sap2010:ViewStateData Id="Assign_119" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_120" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_42" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_23" sap:VirtualizedContainerService.HintSize="532,340" />
      <sap2010:ViewStateData Id="Sequence_43" sap:VirtualizedContainerService.HintSize="554,668">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_12" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_20" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Append_Line_16" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_44" sap:VirtualizedContainerService.HintSize="222,300.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_121" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_122" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_21" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_123" sap:VirtualizedContainerService.HintSize="490,62" />
      <sap2010:ViewStateData Id="Assign_124" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_45" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_17" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_24" sap:VirtualizedContainerService.HintSize="490,340" />
      <sap2010:ViewStateData Id="Assign_125" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_126" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_22" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Append_Line_18" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_46" sap:VirtualizedContainerService.HintSize="264,412">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_19" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_25" sap:VirtualizedContainerService.HintSize="490,566" />
      <sap2010:ViewStateData Id="Sequence_47" sap:VirtualizedContainerService.HintSize="512,1172">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_127" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_128" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_20" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_48" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_26" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_49" sap:VirtualizedContainerService.HintSize="264,442.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_27" sap:VirtualizedContainerService.HintSize="464,596.666666666667" />
      <sap2010:ViewStateData Id="Sequence_50" sap:VirtualizedContainerService.HintSize="486,720.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_28" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_51" sap:VirtualizedContainerService.HintSize="264,584.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_52" sap:VirtualizedContainerService.HintSize="286,708.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_129" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_130" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_21" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_53" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_29" sap:VirtualizedContainerService.HintSize="576,862.666666666667" />
      <sap2010:ViewStateData Id="Sequence_54" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_13" sap:VirtualizedContainerService.HintSize="284.666666666667,212.666666666667" />
      <sap2010:ViewStateData Id="If_30" sap:VirtualizedContainerService.HintSize="510.666666666667,366.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="294.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="316.666666666667,1322">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="356.666666666667,1562" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>