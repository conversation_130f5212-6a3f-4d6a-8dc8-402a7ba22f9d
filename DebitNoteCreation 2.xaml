﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="vendorId" Type="InArgument(x:String)" />
    <x:Property Name="ivdate" Type="InArgument(x:String)" />
    <x:Property Name="division" Type="InArgument(x:String)" />
    <x:Property Name="sino" Type="InArgument(x:String)" />
    <x:Property Name="cucd" Type="InArgument(x:String)" />
    <x:Property Name="tepy" Type="InArgument(x:String)" />
    <x:Property Name="pyme" Type="InArgument(x:String)" />
    <x:Property Name="cuam" Type="InArgument(x:String)" />
    <x:Property Name="authUser" Type="InArgument(x:String)" />
    <x:Property Name="bkid" Type="InArgument(x:String)" />
    <x:Property Name="correlationID" Type="InArgument(x:String)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="M3TotalTableRows" Type="InArgument(scg:List(s:String[]))" />
    <x:Property Name="logfile" Type="InArgument(x:String)" />
    <x:Property Name="vatCodeConfig" Type="InArgument(x:String)" />
    <x:Property Name="inbnValue" Type="InArgument(x:String)" />
    <x:Property Name="ListocrLineValues" Type="InArgument(scg:List(scg:Dictionary(x:String, x:Object)))" />
    <x:Property Name="commentStatus" Type="OutArgument(x:String)" />
    <x:Property Name="Status" Type="OutArgument(x:String)" />
    <x:Property Name="DictOcrValues" Type="InArgument(scg:IDictionary(x:String, x:Object))" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="Reprocess" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>Newtonsoft.Json</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_75">
    <Sequence.Variables>
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj5" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode5" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj0" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode0" />
      <Variable x:TypeArguments="x:String" Name="poInvoiceResponseCode" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="poInvoiceResponseDictionary" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="AddHeadOcrWorkflowOutput" />
      <Variable x:TypeArguments="x:Int32" Name="AddHeadOcrWorkflowStatus" />
      <Variable x:TypeArguments="x:String" Name="amt" />
      <Variable x:TypeArguments="x:Int32" Name="TotalAmount" />
    </Sequence.Variables>
    <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box &quot;debit in xaml&quot;" sap2010:WorkflowViewState.IdRef="MessageBox_1" Selection="OK" Text="debit in xaml" />
    <Assign sap2010:WorkflowViewState.IdRef="Assign_97">
      <Assign.To>
        <OutArgument x:TypeArguments="x:Int32">[TotalAmount]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:Int32">0</InArgument>
      </Assign.Value>
    </Assign>
    <Sequence DisplayName="Get inbn details" sap2010:WorkflowViewState.IdRef="Sequence_81">
      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_123">
        <Sequence.Variables>
          <Variable x:TypeArguments="x:String" Name="vser" />
          <Variable x:TypeArguments="iru:ResponseObject" Name="GetHeadRespObj" />
          <Variable x:TypeArguments="x:Int32" Name="getVoucherStatus" />
          <Variable x:TypeArguments="x:String" Name="inyr" />
        </Sequence.Variables>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_329">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[sino]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[DictOcrValues("INVOICE_RECEIPT_ID").tostring]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_330">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[inyr]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[DictOcrValues("INVOICE_RECEIPT_DATE").tostring.Substring(0,4)]</InArgument>
          </Assign.Value>
        </Assign>
        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Get the voucher satus IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_16" Response="[GetHeadRespObj]" StatusCode="[getVoucherStatus]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS200MI/GetInvTotInfo&quot;]">
          <iai:IONAPIRequestWizard.Headers>
            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
              <scg:List x:TypeArguments="x:String" Capacity="4">
                <x:String>Accept</x:String>
              </scg:List>
              <scg:List x:TypeArguments="x:String" Capacity="4">
                <x:String>application/json</x:String>
              </scg:List>
            </scg:List>
          </iai:IONAPIRequestWizard.Headers>
          <iai:IONAPIRequestWizard.QueryParameters>
            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
              <scg:List x:TypeArguments="x:String" Capacity="8">
                <x:String>DIVI</x:String>
                <x:String>SPYN</x:String>
                <x:String>SUNO</x:String>
                <x:String>SINO</x:String>
                <x:String>INYR</x:String>
              </scg:List>
              <scg:List x:TypeArguments="x:String" Capacity="8">
                <x:String>division</x:String>
                <x:String>vendorId</x:String>
                <x:String>vendorId</x:String>
                <x:String>sino</x:String>
                <x:String>inyr</x:String>
              </scg:List>
            </scg:List>
          </iai:IONAPIRequestWizard.QueryParameters>
        </iai:IONAPIRequestWizard>
        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_13" Line="Get voucher details with first Inbn value" Source="[logfile]" />
        <If Condition="[getVoucherStatus = 200]" sap2010:WorkflowViewState.IdRef="If_90">
          <If.Then>
            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_122">
              <Sequence.Variables>
                <Variable x:TypeArguments="x:String" Name="vono" />
                <Variable x:TypeArguments="x:String" Name="yea4" />
                <Variable x:TypeArguments="x:String" Name="acdt" />
                <Variable x:TypeArguments="njl:JToken" Name="out1" />
              </Sequence.Variables>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_117">
                <Assign.To>
                  <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(GetHeadRespObj.ReadAsText)]</InArgument>
                </Assign.Value>
              </Assign>
              <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_89">
                <If.Then>
                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_82">
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_118">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[(out1("results")(0)("errorMessage")).ToString]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_14" Line="[commentStatus]" Source="[logfile]" />
                  </Sequence>
                </If.Then>
                <If.Else>
                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_121">
                    <Sequence.Variables>
                      <Variable x:TypeArguments="iru:ResponseObject" Name="AccountingvoucherResp" />
                      <Variable x:TypeArguments="njl:JToken" Name="out3" />
                      <Variable x:TypeArguments="x:String" Name="jrno" />
                    </Sequence.Variables>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_119">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[vono]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[(out1("results")(0)("records")(0)("VONO")).ToString]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_120">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[yea4]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[(out1("results")(0)("records")(0)("YEA4")).ToString]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_121">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[acdt]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[(out1("results")(0)("records")(0)("ACDT")).ToString]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_122">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[vser]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[(out1("results")(0)("records")(0)("VSER")).ToString]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_123">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[jrno]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[(out1("results")(0)("records")(0)("JRNO")).ToString]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_124">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[vser]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[(out1("results")(0)("records")(0)("VSER")).ToString]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="GLS200get voucher IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_17" Response="[AccountingvoucherResp]" StatusCode="[getVoucherStatus]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/GLS200MI/GetVoucherLine&quot;]">
                      <iai:IONAPIRequestWizard.Headers>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>Accept</x:String>
                          </scg:List>
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>application/json</x:String>
                          </scg:List>
                        </scg:List>
                      </iai:IONAPIRequestWizard.Headers>
                      <iai:IONAPIRequestWizard.QueryParameters>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="8">
                            <x:String>DIVI</x:String>
                            <x:String>VONO</x:String>
                            <x:String>JSNO</x:String>
                            <x:String>JRNO</x:String>
                            <x:String>YEA4</x:String>
                            <x:String>VSER</x:String>
                          </scg:List>
                          <scg:List x:TypeArguments="x:String" Capacity="8">
                            <x:String>division</x:String>
                            <x:String>vono</x:String>
                            <x:String>2</x:String>
                            <x:String>jrno</x:String>
                            <x:String>yea4</x:String>
                            <x:String>vser</x:String>
                          </scg:List>
                        </scg:List>
                      </iai:IONAPIRequestWizard.QueryParameters>
                    </iai:IONAPIRequestWizard>
                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_15" Line="Get accounting lines for the validated line" Source="[logfile]" />
                    <If Condition="[getVoucherStatus = 200]" sap2010:WorkflowViewState.IdRef="If_88">
                      <If.Then>
                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_120">
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_125">
                            <Assign.To>
                              <OutArgument x:TypeArguments="njl:JToken">[out3]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(AccountingvoucherResp.ReadAsText)]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <If Condition="[out3(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_87">
                            <If.Then>
                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_83">
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_126">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[(out3("results")(0)("errorMessage")).ToString]</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_16" Line="[commentStatus]" Source="[logfile]" />
                              </Sequence>
                            </If.Then>
                            <If.Else>
                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_160">
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_158">
                                  <Sequence.Variables>
                                    <Variable x:TypeArguments="x:Int32" Name="addHeadRecodeStatus" />
                                    <Variable x:TypeArguments="njl:JToken" Name="out2" />
                                    <Variable x:TypeArguments="iru:ResponseObject" Name="AddHeadRespObj" />
                                  </Sequence.Variables>
                                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddHeadRecode IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_25" Response="[AddHeadRespObj]" StatusCode="[addHeadRecodeStatus]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHeadRecode&quot;]">
                                    <iai:IONAPIRequestWizard.Headers>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>Accept</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>application/json</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </iai:IONAPIRequestWizard.Headers>
                                    <iai:IONAPIRequestWizard.QueryParameters>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="8">
                                          <x:String>DIVI</x:String>
                                          <x:String>VONO</x:String>
                                          <x:String>ACDT</x:String>
                                          <x:String>IMCD</x:String>
                                          <x:String>YEA4</x:String>
                                          <x:String>VSER</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="8">
                                          <x:String>division</x:String>
                                          <x:String>vono</x:String>
                                          <x:String>acdt</x:String>
                                          <x:String>0</x:String>
                                          <x:String>yea4</x:String>
                                          <x:String>vser</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </iai:IONAPIRequestWizard.QueryParameters>
                                  </iai:IONAPIRequestWizard>
                                  <If Condition="[addHeadRecodeStatus = 200]" sap2010:WorkflowViewState.IdRef="If_122">
                                    <If.Then>
                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_157">
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_231">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="njl:JToken">[out2]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(AddHeadRespObj.ReadAsText)]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <If Condition="[out2(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_121">
                                          <If.Then>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_124">
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_232">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[(out2("results")(0)("errorMessage")).ToString]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_28" Line="[commentStatus]" Source="[logfile]" />
                                            </Sequence>
                                          </If.Then>
                                          <If.Else>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_156">
                                              <Sequence.Variables>
                                                <Variable x:TypeArguments="x:String" Name="inbnValue2" />
                                              </Sequence.Variables>
                                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_29" Line="Recoded Header Added" Source="[logfile]" />
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_233">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[inbnValue2]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[(out2("results")(0)("records")(0)("INBN")).ToString]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_328">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[inbnValue]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[inbnValue2]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_234">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[amt]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">["-"+DictOcrValues("TOTAL").Tostring]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_2">
                                                <iad:CommentOut.Activities>
                                                  <If Condition="[StatusCode2 = 200]" sap2010:WorkflowViewState.IdRef="If_120">
                                                    <If.Then>
                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_155">
                                                        <Sequence.Variables>
                                                          <Variable x:TypeArguments="x:String" Name="acqt" />
                                                        </Sequence.Variables>
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_235">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="njl:JToken">[out4]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <If Condition="[out4(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_119">
                                                          <If.Then>
                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_125">
                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_236">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="x:String">[(out4("results")(0)("errorMessage")).ToString]</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_31" Line="[commentStatus]" Source="[logfile]" />
                                                            </Sequence>
                                                          </If.Then>
                                                          <If.Else>
                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_154">
                                                              <If Condition="[GLCode = &quot;DISTRIBUTED&quot; AND ListocrLineValues.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_116">
                                                                <If.Then>
                                                                  <ForEach x:TypeArguments="scg:Dictionary(x:String, x:Object)" DisplayName="ForEach&lt;Dictionary&lt;String,Object&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_8" Values="[ListocrLineValues]">
                                                                    <ActivityAction x:TypeArguments="scg:Dictionary(x:String, x:Object)">
                                                                      <ActivityAction.Argument>
                                                                        <DelegateInArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="item" />
                                                                      </ActivityAction.Argument>
                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_128">
                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_237">
                                                                          <Assign.To>
                                                                            <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                                          </Assign.To>
                                                                          <Assign.Value>
                                                                            <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary (Of String, Object)]</InArgument>
                                                                          </Assign.Value>
                                                                        </Assign>
                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_238">
                                                                          <Assign.To>
                                                                            <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                                          </Assign.To>
                                                                          <Assign.Value>
                                                                            <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[item]</InArgument>
                                                                          </Assign.Value>
                                                                        </Assign>
                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_239">
                                                                          <Assign.To>
                                                                            <OutArgument x:TypeArguments="x:String">[AIT1]</OutArgument>
                                                                          </Assign.To>
                                                                          <Assign.Value>
                                                                            <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(0)]</InArgument>
                                                                          </Assign.Value>
                                                                        </Assign>
                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_240">
                                                                          <Assign.To>
                                                                            <OutArgument x:TypeArguments="x:String">[AIT2]</OutArgument>
                                                                          </Assign.To>
                                                                          <Assign.Value>
                                                                            <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(1)]</InArgument>
                                                                          </Assign.Value>
                                                                        </Assign>
                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_241">
                                                                          <Assign.To>
                                                                            <OutArgument x:TypeArguments="x:String">[AIT3]</OutArgument>
                                                                          </Assign.To>
                                                                          <Assign.Value>
                                                                            <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(2)]</InArgument>
                                                                          </Assign.Value>
                                                                        </Assign>
                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_242">
                                                                          <Assign.To>
                                                                            <OutArgument x:TypeArguments="x:String">[AIT4]</OutArgument>
                                                                          </Assign.To>
                                                                          <Assign.Value>
                                                                            <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(3)]</InArgument>
                                                                          </Assign.Value>
                                                                        </Assign>
                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_243">
                                                                          <Assign.To>
                                                                            <OutArgument x:TypeArguments="x:String">[AIT5]</OutArgument>
                                                                          </Assign.To>
                                                                          <Assign.Value>
                                                                            <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(4)]</InArgument>
                                                                          </Assign.Value>
                                                                        </Assign>
                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_244">
                                                                          <Assign.To>
                                                                            <OutArgument x:TypeArguments="x:String">[AIT6]</OutArgument>
                                                                          </Assign.To>
                                                                          <Assign.Value>
                                                                            <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(5)]</InArgument>
                                                                          </Assign.Value>
                                                                        </Assign>
                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_245">
                                                                          <Assign.To>
                                                                            <OutArgument x:TypeArguments="x:String">[AIT7]</OutArgument>
                                                                          </Assign.To>
                                                                          <Assign.Value>
                                                                            <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(6)]</InArgument>
                                                                          </Assign.Value>
                                                                        </Assign>
                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_246">
                                                                          <Assign.To>
                                                                            <OutArgument x:TypeArguments="x:String">[acqt]</OutArgument>
                                                                          </Assign.To>
                                                                          <Assign.Value>
                                                                            <InArgument x:TypeArguments="x:String">[LinesDict("QUANTITY").ToString]</InArgument>
                                                                          </Assign.Value>
                                                                        </Assign>
                                                                        <If Condition="[acqt.trim = &quot;&quot; OR acqt.trim = &quot;NA&quot;]" sap2010:WorkflowViewState.IdRef="If_91">
                                                                          <If.Then>
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_247">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="x:String">[acqt]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="x:String">
                                                                                  <Literal x:TypeArguments="x:String" Value="" />
                                                                                </InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                          </If.Then>
                                                                        </If>
                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_248">
                                                                          <Assign.To>
                                                                            <OutArgument x:TypeArguments="x:String">[amt]</OutArgument>
                                                                          </Assign.To>
                                                                          <Assign.Value>
                                                                            <InArgument x:TypeArguments="x:String">[LinesDict("LINE_AMOUNT").ToString]</InArgument>
                                                                          </Assign.Value>
                                                                        </Assign>
                                                                        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_27" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLineRecode&quot;]">
                                                                          <iai:IONAPIRequestWizard.Headers>
                                                                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                <x:String>Accept</x:String>
                                                                              </scg:List>
                                                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                <x:String>application/json</x:String>
                                                                              </scg:List>
                                                                            </scg:List>
                                                                          </iai:IONAPIRequestWizard.Headers>
                                                                          <iai:IONAPIRequestWizard.QueryParameters>
                                                                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                              <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                                <x:String>INBN</x:String>
                                                                                <x:String>RDTP</x:String>
                                                                                <x:String>DIVI</x:String>
                                                                                <x:String>NLAM</x:String>
                                                                                <x:String>AIT1</x:String>
                                                                                <x:String>AIT2</x:String>
                                                                                <x:String>AIT3</x:String>
                                                                                <x:String>AIT4</x:String>
                                                                                <x:String>AIT5</x:String>
                                                                                <x:String>AIT6</x:String>
                                                                                <x:String>AIT7</x:String>
                                                                                <x:String>ACQT</x:String>
                                                                              </scg:List>
                                                                              <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                                <x:String>inbnValue2</x:String>
                                                                                <x:String>8</x:String>
                                                                                <x:String>division</x:String>
                                                                                <x:String>amt</x:String>
                                                                                <x:String>AIT1</x:String>
                                                                                <x:String>AIT2</x:String>
                                                                                <x:String>AIT3</x:String>
                                                                                <x:String>AIT4</x:String>
                                                                                <x:String>AIT5</x:String>
                                                                                <x:String>AIT6</x:String>
                                                                                <x:String>AIT7</x:String>
                                                                                <x:String>acqt</x:String>
                                                                              </scg:List>
                                                                            </scg:List>
                                                                          </iai:IONAPIRequestWizard.QueryParameters>
                                                                        </iai:IONAPIRequestWizard>
                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_249">
                                                                          <Assign.To>
                                                                            <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                                                                          </Assign.To>
                                                                          <Assign.Value>
                                                                            <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                                                                          </Assign.Value>
                                                                        </Assign>
                                                                        <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_92">
                                                                          <If.Then>
                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_126">
                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_250">
                                                                                <Assign.To>
                                                                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                </Assign.To>
                                                                                <Assign.Value>
                                                                                  <InArgument x:TypeArguments="x:String" xml:space="preserve">["Error occured while adding the line.  "  +  (out1("results")(0)("errorMessage")).ToString]</InArgument>
                                                                                </Assign.Value>
                                                                              </Assign>
                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_251">
                                                                                <Assign.To>
                                                                                  <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                </Assign.To>
                                                                                <Assign.Value>
                                                                                  <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                                </Assign.Value>
                                                                              </Assign>
                                                                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_32" Line="[commentStatus]" Source="[logfile]" />
                                                                            </Sequence>
                                                                          </If.Then>
                                                                          <If.Else>
                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_127">
                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_252">
                                                                                <Assign.To>
                                                                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                </Assign.To>
                                                                                <Assign.Value>
                                                                                  <InArgument x:TypeArguments="x:String">["Invoice Line created"]</InArgument>
                                                                                </Assign.Value>
                                                                              </Assign>
                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_253">
                                                                                <Assign.To>
                                                                                  <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                </Assign.To>
                                                                                <Assign.Value>
                                                                                  <InArgument x:TypeArguments="x:String">["SUCCESS"]</InArgument>
                                                                                </Assign.Value>
                                                                              </Assign>
                                                                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_33" Line="[commentStatus]" Source="[logfile]" />
                                                                            </Sequence>
                                                                          </If.Else>
                                                                        </If>
                                                                      </Sequence>
                                                                    </ActivityAction>
                                                                  </ForEach>
                                                                </If.Then>
                                                                <If.Else>
                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_152">
                                                                    <If Condition="[GLCode = &quot;DISTRIBUTED&quot;]" sap2010:WorkflowViewState.IdRef="If_115">
                                                                      <If.Then>
                                                                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_34" Line="[commentStatus]" Source="[logfile]" />
                                                                      </If.Then>
                                                                      <If.Else>
                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_151">
                                                                          <If Condition="[ListocrLineValues.Count&gt;0]" DisplayName="If GLCode did not obtain from excep" sap2010:WorkflowViewState.IdRef="If_110">
                                                                            <If.Then>
                                                                              <If Condition="[ListocrLineValues(0)(&quot;DESCRIPTION&quot;).Tostring.Contains(&quot;~&quot;)]" sap2010:WorkflowViewState.IdRef="If_101">
                                                                                <If.Else>
                                                                                  <If Condition="[vendorId &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_100">
                                                                                    <If.Then>
                                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_136">
                                                                                        <Sequence.Variables>
                                                                                          <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="vendorResponseDictionary" />
                                                                                          <Variable x:TypeArguments="x:Int32" Name="vendorResponseCode" />
                                                                                          <Variable x:TypeArguments="x:String" Name="invoiceType" />
                                                                                        </Sequence.Variables>
                                                                                        <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;vendorID&quot;,vendorID},{&quot;company&quot;,company},{&quot;division&quot;,division}}]" ContinueOnError="True" DisplayName="SupplierInfo Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_5" OutputArguments="[vendorResponseDictionary]" ResponseCode="[vendorResponseCode]" WorkflowFile="[projectPath+&quot;\SupplierInfo.xaml&quot;]" />
                                                                                        <If Condition="[vendorResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_95">
                                                                                          <If.Then>
                                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_131">
                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_254">
                                                                                                <Assign.To>
                                                                                                  <OutArgument x:TypeArguments="x:String">[invoiceType]</OutArgument>
                                                                                                </Assign.To>
                                                                                                <Assign.Value>
                                                                                                  <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("invoiceType"), String)]</InArgument>
                                                                                                </Assign.Value>
                                                                                              </Assign>
                                                                                              <If Condition="[invoiceType.ToLower.contains(&quot;non po&quot;) OR invoiceType.ToLower.contains(&quot;expense&quot;)]" sap2010:WorkflowViewState.IdRef="If_94">
                                                                                                <If.Then>
                                                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_130">
                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_255">
                                                                                                      <Assign.To>
                                                                                                        <OutArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</OutArgument>
                                                                                                      </Assign.To>
                                                                                                      <Assign.Value>
                                                                                                        <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[New List(Of Dictionary(Of String, Object))]</InArgument>
                                                                                                      </Assign.Value>
                                                                                                    </Assign>
                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_256">
                                                                                                      <Assign.To>
                                                                                                        <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                                                                      </Assign.To>
                                                                                                      <Assign.Value>
                                                                                                        <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary(Of String, Object)]</InArgument>
                                                                                                      </Assign.Value>
                                                                                                    </Assign>
                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_257">
                                                                                                      <Assign.To>
                                                                                                        <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                                      </Assign.To>
                                                                                                      <Assign.Value>
                                                                                                        <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("GLCode"), String)]</InArgument>
                                                                                                      </Assign.Value>
                                                                                                    </Assign>
                                                                                                    <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_5">
                                                                                                      <TryCatch.Try>
                                                                                                        <If Condition="[invoiceType.ToLower.contains(&quot;non po&quot;) OR invoiceType.ToLower.contains(&quot;expense&quot;)]" sap2010:WorkflowViewState.IdRef="If_93">
                                                                                                          <If.Then>
                                                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_129">
                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_258">
                                                                                                                <Assign.To>
                                                                                                                  <OutArgument x:TypeArguments="x:Object">[LinesDict("SUPPLIER_ITEM_CODE")]</OutArgument>
                                                                                                                </Assign.To>
                                                                                                                <Assign.Value>
                                                                                                                  <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                                </Assign.Value>
                                                                                                              </Assign>
                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_259">
                                                                                                                <Assign.To>
                                                                                                                  <OutArgument x:TypeArguments="x:Object">[LinesDict("Dimensions")]</OutArgument>
                                                                                                                </Assign.To>
                                                                                                                <Assign.Value>
                                                                                                                  <InArgument x:TypeArguments="x:Object">[GLCode]</InArgument>
                                                                                                                </Assign.Value>
                                                                                                              </Assign>
                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_260">
                                                                                                                <Assign.To>
                                                                                                                  <OutArgument x:TypeArguments="x:Object">[LinesDict("QUANTITY")]</OutArgument>
                                                                                                                </Assign.To>
                                                                                                                <Assign.Value>
                                                                                                                  <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                                </Assign.Value>
                                                                                                              </Assign>
                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_261">
                                                                                                                <Assign.To>
                                                                                                                  <OutArgument x:TypeArguments="x:Object">[LinesDict("UNIT_PRICE")]</OutArgument>
                                                                                                                </Assign.To>
                                                                                                                <Assign.Value>
                                                                                                                  <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                                </Assign.Value>
                                                                                                              </Assign>
                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_262">
                                                                                                                <Assign.To>
                                                                                                                  <OutArgument x:TypeArguments="x:Object">[LinesDict("LINE_AMOUNT")]</OutArgument>
                                                                                                                </Assign.To>
                                                                                                                <Assign.Value>
                                                                                                                  <InArgument x:TypeArguments="x:Object">[DictOcrValues("SUBTOTAL").Tostring]</InArgument>
                                                                                                                </Assign.Value>
                                                                                                              </Assign>
                                                                                                              <InvokeMethod DisplayName="Assign to ocrValueLines InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_5" MethodName="Add">
                                                                                                                <InvokeMethod.TargetObject>
                                                                                                                  <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</InArgument>
                                                                                                                </InvokeMethod.TargetObject>
                                                                                                                <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</InArgument>
                                                                                                              </InvokeMethod>
                                                                                                            </Sequence>
                                                                                                          </If.Then>
                                                                                                        </If>
                                                                                                      </TryCatch.Try>
                                                                                                      <TryCatch.Catches>
                                                                                                        <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_5">
                                                                                                          <ActivityAction x:TypeArguments="s:Exception">
                                                                                                            <ActivityAction.Argument>
                                                                                                              <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                                            </ActivityAction.Argument>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_263">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("GLCode"), String)]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                          </ActivityAction>
                                                                                                        </Catch>
                                                                                                      </TryCatch.Catches>
                                                                                                    </TryCatch>
                                                                                                  </Sequence>
                                                                                                </If.Then>
                                                                                              </If>
                                                                                            </Sequence>
                                                                                          </If.Then>
                                                                                        </If>
                                                                                        <If Condition="[vendorResponseCode &lt;&gt; 200 OR GLCode = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_99">
                                                                                          <If.Then>
                                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_135">
                                                                                              <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;vendorID&quot;,vendorID},{&quot;Division&quot;,division},{&quot;businessRule&quot;,miscValues(&quot;businessRuleForDivisionGLCode&quot;).ToString}}]" ContinueOnError="True" DisplayName="DivisiontoGLCode Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_6" OutputArguments="[vendorResponseDictionary]" ResponseCode="[vendorResponseCode]" WorkflowFile="[projectPath+&quot;\DivisiontoGLCode.xaml&quot;]" />
                                                                                              <If Condition="[vendorResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_98">
                                                                                                <If.Then>
                                                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_134">
                                                                                                    <Sequence.Variables>
                                                                                                      <Variable x:TypeArguments="x:String" Name="comb" />
                                                                                                    </Sequence.Variables>
                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_264">
                                                                                                      <Assign.To>
                                                                                                        <OutArgument x:TypeArguments="x:String">[comb]</OutArgument>
                                                                                                      </Assign.To>
                                                                                                      <Assign.Value>
                                                                                                        <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("comb"), String)]</InArgument>
                                                                                                      </Assign.Value>
                                                                                                    </Assign>
                                                                                                    <If Condition="[comb &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_97">
                                                                                                      <If.Then>
                                                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_133">
                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_265">
                                                                                                            <Assign.To>
                                                                                                              <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                                            </Assign.To>
                                                                                                            <Assign.Value>
                                                                                                              <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("AIT1"), String)]</InArgument>
                                                                                                            </Assign.Value>
                                                                                                          </Assign>
                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_266">
                                                                                                            <Assign.To>
                                                                                                              <OutArgument x:TypeArguments="x:String">[invoiceType]</OutArgument>
                                                                                                            </Assign.To>
                                                                                                            <Assign.Value>
                                                                                                              <InArgument x:TypeArguments="x:String">Expense Invoice</InArgument>
                                                                                                            </Assign.Value>
                                                                                                          </Assign>
                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_267">
                                                                                                            <Assign.To>
                                                                                                              <OutArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</OutArgument>
                                                                                                            </Assign.To>
                                                                                                            <Assign.Value>
                                                                                                              <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[New List(Of Dictionary(Of String, Object))]</InArgument>
                                                                                                            </Assign.Value>
                                                                                                          </Assign>
                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_268">
                                                                                                            <Assign.To>
                                                                                                              <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                                                                            </Assign.To>
                                                                                                            <Assign.Value>
                                                                                                              <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary(Of String, Object)]</InArgument>
                                                                                                            </Assign.Value>
                                                                                                          </Assign>
                                                                                                          <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_6">
                                                                                                            <TryCatch.Try>
                                                                                                              <If Condition="[invoiceType.ToLower.contains(&quot;non po&quot;) OR invoiceType.ToLower.contains(&quot;expense&quot;)]" sap2010:WorkflowViewState.IdRef="If_96">
                                                                                                                <If.Then>
                                                                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_132">
                                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_269">
                                                                                                                      <Assign.To>
                                                                                                                        <OutArgument x:TypeArguments="x:Object">[LinesDict("SUPPLIER_ITEM_CODE")]</OutArgument>
                                                                                                                      </Assign.To>
                                                                                                                      <Assign.Value>
                                                                                                                        <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                                      </Assign.Value>
                                                                                                                    </Assign>
                                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_270">
                                                                                                                      <Assign.To>
                                                                                                                        <OutArgument x:TypeArguments="x:Object">[LinesDict("Dimensions")]</OutArgument>
                                                                                                                      </Assign.To>
                                                                                                                      <Assign.Value>
                                                                                                                        <InArgument x:TypeArguments="x:Object">[comb]</InArgument>
                                                                                                                      </Assign.Value>
                                                                                                                    </Assign>
                                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_271">
                                                                                                                      <Assign.To>
                                                                                                                        <OutArgument x:TypeArguments="x:Object">[LinesDict("QUANTITY")]</OutArgument>
                                                                                                                      </Assign.To>
                                                                                                                      <Assign.Value>
                                                                                                                        <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                                      </Assign.Value>
                                                                                                                    </Assign>
                                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_272">
                                                                                                                      <Assign.To>
                                                                                                                        <OutArgument x:TypeArguments="x:Object">[LinesDict("UNIT_PRICE")]</OutArgument>
                                                                                                                      </Assign.To>
                                                                                                                      <Assign.Value>
                                                                                                                        <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                                      </Assign.Value>
                                                                                                                    </Assign>
                                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_273">
                                                                                                                      <Assign.To>
                                                                                                                        <OutArgument x:TypeArguments="x:Object">[LinesDict("LINE_AMOUNT")]</OutArgument>
                                                                                                                      </Assign.To>
                                                                                                                      <Assign.Value>
                                                                                                                        <InArgument x:TypeArguments="x:Object">[DictOcrValues("SUBTOTAL").Tostring]</InArgument>
                                                                                                                      </Assign.Value>
                                                                                                                    </Assign>
                                                                                                                    <InvokeMethod DisplayName="Assign to ocrValueLines InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_6" MethodName="Add">
                                                                                                                      <InvokeMethod.TargetObject>
                                                                                                                        <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</InArgument>
                                                                                                                      </InvokeMethod.TargetObject>
                                                                                                                      <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</InArgument>
                                                                                                                    </InvokeMethod>
                                                                                                                  </Sequence>
                                                                                                                </If.Then>
                                                                                                              </If>
                                                                                                            </TryCatch.Try>
                                                                                                            <TryCatch.Catches>
                                                                                                              <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_6">
                                                                                                                <ActivityAction x:TypeArguments="s:Exception">
                                                                                                                  <ActivityAction.Argument>
                                                                                                                    <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                                                  </ActivityAction.Argument>
                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_274">
                                                                                                                    <Assign.To>
                                                                                                                      <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                                                    </Assign.To>
                                                                                                                    <Assign.Value>
                                                                                                                      <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("AIT1"), String)]</InArgument>
                                                                                                                    </Assign.Value>
                                                                                                                  </Assign>
                                                                                                                </ActivityAction>
                                                                                                              </Catch>
                                                                                                            </TryCatch.Catches>
                                                                                                          </TryCatch>
                                                                                                        </Sequence>
                                                                                                      </If.Then>
                                                                                                    </If>
                                                                                                  </Sequence>
                                                                                                </If.Then>
                                                                                              </If>
                                                                                            </Sequence>
                                                                                          </If.Then>
                                                                                        </If>
                                                                                      </Sequence>
                                                                                    </If.Then>
                                                                                  </If>
                                                                                </If.Else>
                                                                              </If>
                                                                            </If.Then>
                                                                            <If.Else>
                                                                              <If Condition="[vendorId &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_109">
                                                                                <If.Then>
                                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_144">
                                                                                    <Sequence.Variables>
                                                                                      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="vendorResponseDictionary" />
                                                                                      <Variable x:TypeArguments="x:Int32" Name="vendorResponseCode" />
                                                                                      <Variable x:TypeArguments="x:String" Name="invoiceType" />
                                                                                    </Sequence.Variables>
                                                                                    <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;vendorID&quot;,vendorID},{&quot;company&quot;,company},{&quot;division&quot;,division}}]" ContinueOnError="True" DisplayName="SupplierInfo Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_7" OutputArguments="[vendorResponseDictionary]" ResponseCode="[vendorResponseCode]" WorkflowFile="[projectPath+&quot;\SupplierInfo.xaml&quot;]" />
                                                                                    <If Condition="[vendorResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_104">
                                                                                      <If.Then>
                                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_139">
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_275">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:String">[invoiceType]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("invoiceType"), String)]</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                          <If Condition="[invoiceType.ToLower.contains(&quot;non po&quot;) OR invoiceType.ToLower.contains(&quot;expense&quot;)]" sap2010:WorkflowViewState.IdRef="If_103">
                                                                                            <If.Then>
                                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_138">
                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_276">
                                                                                                  <Assign.To>
                                                                                                    <OutArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</OutArgument>
                                                                                                  </Assign.To>
                                                                                                  <Assign.Value>
                                                                                                    <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[New List(Of Dictionary(Of String, Object))]</InArgument>
                                                                                                  </Assign.Value>
                                                                                                </Assign>
                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_277">
                                                                                                  <Assign.To>
                                                                                                    <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                                                                  </Assign.To>
                                                                                                  <Assign.Value>
                                                                                                    <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary(Of String, Object)]</InArgument>
                                                                                                  </Assign.Value>
                                                                                                </Assign>
                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_278">
                                                                                                  <Assign.To>
                                                                                                    <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                                  </Assign.To>
                                                                                                  <Assign.Value>
                                                                                                    <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("GLCode"), String)]</InArgument>
                                                                                                  </Assign.Value>
                                                                                                </Assign>
                                                                                                <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_7">
                                                                                                  <TryCatch.Try>
                                                                                                    <If Condition="[invoiceType.ToLower.contains(&quot;non po&quot;) OR invoiceType.ToLower.contains(&quot;expense&quot;)]" sap2010:WorkflowViewState.IdRef="If_102">
                                                                                                      <If.Then>
                                                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_137">
                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_279">
                                                                                                            <Assign.To>
                                                                                                              <OutArgument x:TypeArguments="x:Object">[LinesDict("SUPPLIER_ITEM_CODE")]</OutArgument>
                                                                                                            </Assign.To>
                                                                                                            <Assign.Value>
                                                                                                              <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                            </Assign.Value>
                                                                                                          </Assign>
                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_280">
                                                                                                            <Assign.To>
                                                                                                              <OutArgument x:TypeArguments="x:Object">[LinesDict("Dimensions")]</OutArgument>
                                                                                                            </Assign.To>
                                                                                                            <Assign.Value>
                                                                                                              <InArgument x:TypeArguments="x:Object">[GLCode]</InArgument>
                                                                                                            </Assign.Value>
                                                                                                          </Assign>
                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_281">
                                                                                                            <Assign.To>
                                                                                                              <OutArgument x:TypeArguments="x:Object">[LinesDict("QUANTITY")]</OutArgument>
                                                                                                            </Assign.To>
                                                                                                            <Assign.Value>
                                                                                                              <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                            </Assign.Value>
                                                                                                          </Assign>
                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_282">
                                                                                                            <Assign.To>
                                                                                                              <OutArgument x:TypeArguments="x:Object">[LinesDict("UNIT_PRICE")]</OutArgument>
                                                                                                            </Assign.To>
                                                                                                            <Assign.Value>
                                                                                                              <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                            </Assign.Value>
                                                                                                          </Assign>
                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_283">
                                                                                                            <Assign.To>
                                                                                                              <OutArgument x:TypeArguments="x:Object">[LinesDict("LINE_AMOUNT")]</OutArgument>
                                                                                                            </Assign.To>
                                                                                                            <Assign.Value>
                                                                                                              <InArgument x:TypeArguments="x:Object">[DictOcrValues("SUBTOTAL").Tostring]</InArgument>
                                                                                                            </Assign.Value>
                                                                                                          </Assign>
                                                                                                          <InvokeMethod DisplayName="Assign to ocrValueLines InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_7" MethodName="Add">
                                                                                                            <InvokeMethod.TargetObject>
                                                                                                              <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</InArgument>
                                                                                                            </InvokeMethod.TargetObject>
                                                                                                            <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</InArgument>
                                                                                                          </InvokeMethod>
                                                                                                        </Sequence>
                                                                                                      </If.Then>
                                                                                                    </If>
                                                                                                  </TryCatch.Try>
                                                                                                  <TryCatch.Catches>
                                                                                                    <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_7">
                                                                                                      <ActivityAction x:TypeArguments="s:Exception">
                                                                                                        <ActivityAction.Argument>
                                                                                                          <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                                        </ActivityAction.Argument>
                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_284">
                                                                                                          <Assign.To>
                                                                                                            <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                                          </Assign.To>
                                                                                                          <Assign.Value>
                                                                                                            <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("GLCode"), String)]</InArgument>
                                                                                                          </Assign.Value>
                                                                                                        </Assign>
                                                                                                      </ActivityAction>
                                                                                                    </Catch>
                                                                                                  </TryCatch.Catches>
                                                                                                </TryCatch>
                                                                                              </Sequence>
                                                                                            </If.Then>
                                                                                          </If>
                                                                                        </Sequence>
                                                                                      </If.Then>
                                                                                    </If>
                                                                                    <If Condition="[vendorResponseCode &lt;&gt; 200 OR NOT ListOcrLineValues.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_108">
                                                                                      <If.Then>
                                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_143">
                                                                                          <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;vendorID&quot;,vendorID},{&quot;Division&quot;,division},{&quot;businessRule&quot;,miscValues(&quot;businessRuleForDivisionGLCode&quot;).ToString}}]" ContinueOnError="True" DisplayName="DivisiontoGLCode Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_8" OutputArguments="[vendorResponseDictionary]" ResponseCode="[vendorResponseCode]" WorkflowFile="[projectPath+&quot;\DivisiontoGLCode.xaml&quot;]" />
                                                                                          <If Condition="[vendorResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_107">
                                                                                            <If.Then>
                                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_142">
                                                                                                <Sequence.Variables>
                                                                                                  <Variable x:TypeArguments="x:String" Name="comb" />
                                                                                                </Sequence.Variables>
                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_285">
                                                                                                  <Assign.To>
                                                                                                    <OutArgument x:TypeArguments="x:String">[comb]</OutArgument>
                                                                                                  </Assign.To>
                                                                                                  <Assign.Value>
                                                                                                    <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("comb"), String)]</InArgument>
                                                                                                  </Assign.Value>
                                                                                                </Assign>
                                                                                                <If Condition="[comb &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_106">
                                                                                                  <If.Then>
                                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_141">
                                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_286">
                                                                                                        <Assign.To>
                                                                                                          <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                                        </Assign.To>
                                                                                                        <Assign.Value>
                                                                                                          <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("AIT1"), String)]</InArgument>
                                                                                                        </Assign.Value>
                                                                                                      </Assign>
                                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_287">
                                                                                                        <Assign.To>
                                                                                                          <OutArgument x:TypeArguments="x:String">[invoiceType]</OutArgument>
                                                                                                        </Assign.To>
                                                                                                        <Assign.Value>
                                                                                                          <InArgument x:TypeArguments="x:String">Expense Invoice</InArgument>
                                                                                                        </Assign.Value>
                                                                                                      </Assign>
                                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_288">
                                                                                                        <Assign.To>
                                                                                                          <OutArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</OutArgument>
                                                                                                        </Assign.To>
                                                                                                        <Assign.Value>
                                                                                                          <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[New List(Of Dictionary(Of String, Object))]</InArgument>
                                                                                                        </Assign.Value>
                                                                                                      </Assign>
                                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_289">
                                                                                                        <Assign.To>
                                                                                                          <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                                                                        </Assign.To>
                                                                                                        <Assign.Value>
                                                                                                          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary(Of String, Object)]</InArgument>
                                                                                                        </Assign.Value>
                                                                                                      </Assign>
                                                                                                      <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_8">
                                                                                                        <TryCatch.Try>
                                                                                                          <If Condition="[invoiceType.ToLower.contains(&quot;non po&quot;) OR invoiceType.ToLower.contains(&quot;expense&quot;)]" sap2010:WorkflowViewState.IdRef="If_105">
                                                                                                            <If.Then>
                                                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_140">
                                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_290">
                                                                                                                  <Assign.To>
                                                                                                                    <OutArgument x:TypeArguments="x:Object">[LinesDict("SUPPLIER_ITEM_CODE")]</OutArgument>
                                                                                                                  </Assign.To>
                                                                                                                  <Assign.Value>
                                                                                                                    <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                                  </Assign.Value>
                                                                                                                </Assign>
                                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_291">
                                                                                                                  <Assign.To>
                                                                                                                    <OutArgument x:TypeArguments="x:Object">[LinesDict("Dimensions")]</OutArgument>
                                                                                                                  </Assign.To>
                                                                                                                  <Assign.Value>
                                                                                                                    <InArgument x:TypeArguments="x:Object">[comb]</InArgument>
                                                                                                                  </Assign.Value>
                                                                                                                </Assign>
                                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_292">
                                                                                                                  <Assign.To>
                                                                                                                    <OutArgument x:TypeArguments="x:Object">[LinesDict("QUANTITY")]</OutArgument>
                                                                                                                  </Assign.To>
                                                                                                                  <Assign.Value>
                                                                                                                    <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                                  </Assign.Value>
                                                                                                                </Assign>
                                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_293">
                                                                                                                  <Assign.To>
                                                                                                                    <OutArgument x:TypeArguments="x:Object">[LinesDict("UNIT_PRICE")]</OutArgument>
                                                                                                                  </Assign.To>
                                                                                                                  <Assign.Value>
                                                                                                                    <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                                  </Assign.Value>
                                                                                                                </Assign>
                                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_294">
                                                                                                                  <Assign.To>
                                                                                                                    <OutArgument x:TypeArguments="x:Object">[LinesDict("LINE_AMOUNT")]</OutArgument>
                                                                                                                  </Assign.To>
                                                                                                                  <Assign.Value>
                                                                                                                    <InArgument x:TypeArguments="x:Object">[DictOcrValues("SUBTOTAL").Tostring]</InArgument>
                                                                                                                  </Assign.Value>
                                                                                                                </Assign>
                                                                                                                <InvokeMethod DisplayName="Assign to ocrValueLines InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_8" MethodName="Add">
                                                                                                                  <InvokeMethod.TargetObject>
                                                                                                                    <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</InArgument>
                                                                                                                  </InvokeMethod.TargetObject>
                                                                                                                  <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</InArgument>
                                                                                                                </InvokeMethod>
                                                                                                              </Sequence>
                                                                                                            </If.Then>
                                                                                                          </If>
                                                                                                        </TryCatch.Try>
                                                                                                        <TryCatch.Catches>
                                                                                                          <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_8">
                                                                                                            <ActivityAction x:TypeArguments="s:Exception">
                                                                                                              <ActivityAction.Argument>
                                                                                                                <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                                              </ActivityAction.Argument>
                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_295">
                                                                                                                <Assign.To>
                                                                                                                  <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                                                </Assign.To>
                                                                                                                <Assign.Value>
                                                                                                                  <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("AIT1"), String)]</InArgument>
                                                                                                                </Assign.Value>
                                                                                                              </Assign>
                                                                                                            </ActivityAction>
                                                                                                          </Catch>
                                                                                                        </TryCatch.Catches>
                                                                                                      </TryCatch>
                                                                                                    </Sequence>
                                                                                                  </If.Then>
                                                                                                </If>
                                                                                              </Sequence>
                                                                                            </If.Then>
                                                                                          </If>
                                                                                        </Sequence>
                                                                                      </If.Then>
                                                                                    </If>
                                                                                  </Sequence>
                                                                                </If.Then>
                                                                              </If>
                                                                            </If.Else>
                                                                          </If>
                                                                          <If Condition="[ListocrLineValues.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_114">
                                                                            <If.Then>
                                                                              <ForEach x:TypeArguments="scg:Dictionary(x:String, x:Object)" DisplayName="ForEach&lt;Dictionary&lt;String,Object&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_9" Values="[ListocrLineValues]">
                                                                                <ActivityAction x:TypeArguments="scg:Dictionary(x:String, x:Object)">
                                                                                  <ActivityAction.Argument>
                                                                                    <DelegateInArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="item" />
                                                                                  </ActivityAction.Argument>
                                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_147">
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_296">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary (Of String, Object)]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_297">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[item]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_298">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:String">[AIT1]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(0)]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_299">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:String">[AIT2]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(1)]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_300">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:String">[AIT3]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(2)]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_301">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:String">[AIT4]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(3)]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_302">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:String">[AIT5]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(4)]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_303">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:String">[AIT6]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(5)]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_304">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:String">[AIT7]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(6)]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_305">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:String">[acqt]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:String">[LinesDict("QUANTITY").ToString]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <If Condition="[acqt.trim = &quot;&quot; OR acqt.trim = &quot;NA&quot;]" sap2010:WorkflowViewState.IdRef="If_111">
                                                                                      <If.Then>
                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_306">
                                                                                          <Assign.To>
                                                                                            <OutArgument x:TypeArguments="x:String">[acqt]</OutArgument>
                                                                                          </Assign.To>
                                                                                          <Assign.Value>
                                                                                            <InArgument x:TypeArguments="x:String">
                                                                                              <Literal x:TypeArguments="x:String" Value="" />
                                                                                            </InArgument>
                                                                                          </Assign.Value>
                                                                                        </Assign>
                                                                                      </If.Then>
                                                                                    </If>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_307">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:String">[amt]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:String">[LinesDict("LINE_AMOUNT").ToString]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_28" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLineRecode&quot;]">
                                                                                      <iai:IONAPIRequestWizard.Headers>
                                                                                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                            <x:String>Accept</x:String>
                                                                                          </scg:List>
                                                                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                            <x:String>application/json</x:String>
                                                                                          </scg:List>
                                                                                        </scg:List>
                                                                                      </iai:IONAPIRequestWizard.Headers>
                                                                                      <iai:IONAPIRequestWizard.QueryParameters>
                                                                                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                          <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                                            <x:String>INBN</x:String>
                                                                                            <x:String>RDTP</x:String>
                                                                                            <x:String>DIVI</x:String>
                                                                                            <x:String>NLAM</x:String>
                                                                                            <x:String>AIT1</x:String>
                                                                                            <x:String>AIT2</x:String>
                                                                                            <x:String>AIT3</x:String>
                                                                                            <x:String>AIT4</x:String>
                                                                                            <x:String>AIT5</x:String>
                                                                                            <x:String>AIT6</x:String>
                                                                                            <x:String>AIT7</x:String>
                                                                                            <x:String>ACQT</x:String>
                                                                                          </scg:List>
                                                                                          <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                                            <x:String>inbnValue2</x:String>
                                                                                            <x:String>8</x:String>
                                                                                            <x:String>division</x:String>
                                                                                            <x:String>amt</x:String>
                                                                                            <x:String>AIT1</x:String>
                                                                                            <x:String>AIT2</x:String>
                                                                                            <x:String>AIT3</x:String>
                                                                                            <x:String>AIT4</x:String>
                                                                                            <x:String>AIT5</x:String>
                                                                                            <x:String>AIT6</x:String>
                                                                                            <x:String>AIT7</x:String>
                                                                                            <x:String>acqt</x:String>
                                                                                          </scg:List>
                                                                                        </scg:List>
                                                                                      </iai:IONAPIRequestWizard.QueryParameters>
                                                                                    </iai:IONAPIRequestWizard>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_308">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_112">
                                                                                      <If.Then>
                                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_145">
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_309">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:String" xml:space="preserve">["Error occured while adding the line.  "  +  (out1("results")(0)("errorMessage")).ToString]</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_310">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_35" Line="[commentStatus]" Source="[logfile]" />
                                                                                        </Sequence>
                                                                                      </If.Then>
                                                                                      <If.Else>
                                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_146">
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_311">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:String">["Invoice Line created"]</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_312">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:String">["SUCCESS"]</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_36" Line="[commentStatus]" Source="[logfile]" />
                                                                                        </Sequence>
                                                                                      </If.Else>
                                                                                    </If>
                                                                                  </Sequence>
                                                                                </ActivityAction>
                                                                              </ForEach>
                                                                            </If.Then>
                                                                            <If.Else>
                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_150">
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_313">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[AIT1]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">[GLCode]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_314">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[AIT2]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">
                                                                                      <Literal x:TypeArguments="x:String" Value="" />
                                                                                    </InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_315">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[AIT3]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">
                                                                                      <Literal x:TypeArguments="x:String" Value="" />
                                                                                    </InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_316">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[AIT4]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">
                                                                                      <Literal x:TypeArguments="x:String" Value="" />
                                                                                    </InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_317">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[AIT5]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">
                                                                                      <Literal x:TypeArguments="x:String" Value="" />
                                                                                    </InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_318">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[AIT6]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">
                                                                                      <Literal x:TypeArguments="x:String" Value="" />
                                                                                    </InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_319">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[AIT7]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">
                                                                                      <Literal x:TypeArguments="x:String" Value="" />
                                                                                    </InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_320">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[amt]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">[LinesDict("UNIT_PRICE").ToString]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_29" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                                                  <iai:IONAPIRequestWizard.Headers>
                                                                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                        <x:String>Accept</x:String>
                                                                                      </scg:List>
                                                                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                        <x:String>application/json</x:String>
                                                                                      </scg:List>
                                                                                    </scg:List>
                                                                                  </iai:IONAPIRequestWizard.Headers>
                                                                                  <iai:IONAPIRequestWizard.QueryParameters>
                                                                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                      <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                                        <x:String>INBN</x:String>
                                                                                        <x:String>RDTP</x:String>
                                                                                        <x:String>DIVI</x:String>
                                                                                        <x:String>NLAM</x:String>
                                                                                        <x:String>AIT1</x:String>
                                                                                        <x:String>AIT2</x:String>
                                                                                        <x:String>AIT3</x:String>
                                                                                        <x:String>AIT4</x:String>
                                                                                        <x:String>AIT5</x:String>
                                                                                        <x:String>AIT6</x:String>
                                                                                        <x:String>AIT7</x:String>
                                                                                      </scg:List>
                                                                                      <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                                        <x:String>inbnValue</x:String>
                                                                                        <x:String>8</x:String>
                                                                                        <x:String>division</x:String>
                                                                                        <x:String>amt</x:String>
                                                                                        <x:String>AIT1</x:String>
                                                                                        <x:String>AIT2</x:String>
                                                                                        <x:String>AIT3</x:String>
                                                                                        <x:String>AIT4</x:String>
                                                                                        <x:String>AIT5</x:String>
                                                                                        <x:String>AIT6</x:String>
                                                                                        <x:String>AIT7</x:String>
                                                                                      </scg:List>
                                                                                    </scg:List>
                                                                                  </iai:IONAPIRequestWizard.QueryParameters>
                                                                                </iai:IONAPIRequestWizard>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_321">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_113">
                                                                                  <If.Then>
                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_148">
                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_322">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:String" xml:space="preserve">["Error occured while adding the line.  "  +  (out1("results")(0)("errorMessage")).ToString]</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_323">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_37" Line="[commentStatus]" Source="[logfile]" />
                                                                                    </Sequence>
                                                                                  </If.Then>
                                                                                  <If.Else>
                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_149">
                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_324">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:String">["Invoice Line created"]</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_325">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:String">["SUCCESS"]</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_38" Line="[commentStatus]" Source="[logfile]" />
                                                                                    </Sequence>
                                                                                  </If.Else>
                                                                                </If>
                                                                              </Sequence>
                                                                            </If.Else>
                                                                          </If>
                                                                        </Sequence>
                                                                      </If.Else>
                                                                    </If>
                                                                  </Sequence>
                                                                </If.Else>
                                                              </If>
                                                              <If Condition="[CInt(DictOcrValues(&quot;VAT_AMOUNT&quot;).Tostring) &lt;&gt; 0]" sap2010:WorkflowViewState.IdRef="If_118">
                                                                <If.Then>
                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_153">
                                                                    <Sequence.Variables>
                                                                      <Variable x:TypeArguments="x:String" Name="vat" />
                                                                      <Variable x:TypeArguments="iru:ResponseObject" Name="respout1" />
                                                                      <Variable x:TypeArguments="x:String" Name="vatCode" />
                                                                    </Sequence.Variables>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_326">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[vat]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">[DictOcrValues("VAT_AMOUNT").Tostring]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_327">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[vatCode]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">[miscValues("vatCodeConfig").ToString]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <If Condition="[miscValues(&quot;vatCodeConfig&quot;).ToString = &quot;NA&quot;]" sap2010:WorkflowViewState.IdRef="If_117">
                                                                      <If.Then>
                                                                        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_30" Response="[respout1]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                                          <iai:IONAPIRequestWizard.Headers>
                                                                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                <x:String>Accept</x:String>
                                                                              </scg:List>
                                                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                <x:String>application/json</x:String>
                                                                              </scg:List>
                                                                            </scg:List>
                                                                          </iai:IONAPIRequestWizard.Headers>
                                                                          <iai:IONAPIRequestWizard.QueryParameters>
                                                                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                <x:String>INBN</x:String>
                                                                                <x:String>RDTP</x:String>
                                                                                <x:String>DIVI</x:String>
                                                                                <x:String>GLAM</x:String>
                                                                              </scg:List>
                                                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                <x:String>inbnValue2</x:String>
                                                                                <x:String>3</x:String>
                                                                                <x:String>division</x:String>
                                                                                <x:String>vat</x:String>
                                                                              </scg:List>
                                                                            </scg:List>
                                                                          </iai:IONAPIRequestWizard.QueryParameters>
                                                                        </iai:IONAPIRequestWizard>
                                                                      </If.Then>
                                                                      <If.Else>
                                                                        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_31" Response="[respout1]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                                          <iai:IONAPIRequestWizard.Headers>
                                                                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                <x:String>Accept</x:String>
                                                                              </scg:List>
                                                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                <x:String>application/json</x:String>
                                                                              </scg:List>
                                                                            </scg:List>
                                                                          </iai:IONAPIRequestWizard.Headers>
                                                                          <iai:IONAPIRequestWizard.QueryParameters>
                                                                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                              <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                <x:String>INBN</x:String>
                                                                                <x:String>RDTP</x:String>
                                                                                <x:String>DIVI</x:String>
                                                                                <x:String>VTA1</x:String>
                                                                                <x:String>VTCD</x:String>
                                                                              </scg:List>
                                                                              <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                <x:String>inbnValue2</x:String>
                                                                                <x:String>3</x:String>
                                                                                <x:String>division</x:String>
                                                                                <x:String>vat</x:String>
                                                                                <x:String>vatCode</x:String>
                                                                              </scg:List>
                                                                            </scg:List>
                                                                          </iai:IONAPIRequestWizard.QueryParameters>
                                                                        </iai:IONAPIRequestWizard>
                                                                      </If.Else>
                                                                    </If>
                                                                  </Sequence>
                                                                </If.Then>
                                                              </If>
                                                            </Sequence>
                                                          </If.Else>
                                                        </If>
                                                      </Sequence>
                                                    </If.Then>
                                                  </If>
                                                </iad:CommentOut.Activities>
                                              </iad:CommentOut>
                                            </Sequence>
                                          </If.Else>
                                        </If>
                                      </Sequence>
                                    </If.Then>
                                  </If>
                                </Sequence>
                                <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_1">
                                  <iad:CommentOut.Activities>
                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_119">
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_127">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[AIT1]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[(out3("results")(0)("records")(0)("AIT1")).ToString]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_128">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[AIT2]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[(out3("results")(0)("records")(0)("AIT2")).ToString]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_129">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[AIT3]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[(out3("results")(0)("records")(0)("AIT3")).ToString]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_130">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[AIT4]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[(out3("results")(0)("records")(0)("AIT4")).ToString]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_131">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[AIT5]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[(out3("results")(0)("records")(0)("AIT5")).ToString]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_132">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[AIT6]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[(out3("results")(0)("records")(0)("AIT6")).ToString]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_133">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[AIT7]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[(out3("results")(0)("records")(0)("AIT7")).ToString]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                    </Sequence>
                                  </iad:CommentOut.Activities>
                                </iad:CommentOut>
                              </Sequence>
                            </If.Else>
                          </If>
                        </Sequence>
                      </If.Then>
                    </If>
                  </Sequence>
                </If.Else>
              </If>
            </Sequence>
          </If.Then>
        </If>
      </Sequence>
    </Sequence>
    <Sequence DisplayName="Adding Lines" sap2010:WorkflowViewState.IdRef="Sequence_74">
      <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="ListocrLineValues" sap2010:WorkflowViewState.IdRef="MessageBox_5" Selection="OK" Text="[ListocrLineValues.Count.tostring]" />
      <ForEach x:TypeArguments="scg:Dictionary(x:String, x:Object)" DisplayName="ForEach&lt;Dictionary&lt;String,Object&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_4" Values="[ListocrLineValues]">
        <ActivityAction x:TypeArguments="scg:Dictionary(x:String, x:Object)">
          <ActivityAction.Argument>
            <DelegateInArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="item" />
          </ActivityAction.Argument>
          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_73">
            <Sequence.Variables>
              <Variable x:TypeArguments="iru:ResponseObject" Name="respObj7" />
              <Variable x:TypeArguments="x:Int32" Name="StatusCode7" />
              <Variable x:TypeArguments="njl:JToken" Name="out7" />
            </Sequence.Variables>
            <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_9" Selection="OK" Text="[item(&quot;DESCRIPTION&quot;).ToString]" Title="DESCRIPTION" />
            <If Condition="[item(&quot;DESCRIPTION&quot;).ToString.Contains(&quot;~&quot;)]" sap2010:WorkflowViewState.IdRef="If_52">
              <If.Then>
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_62">
                  <Sequence.Variables>
                    <Variable x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="LinesDict" />
                    <Variable x:TypeArguments="x:String" Name="AIT1" />
                    <Variable x:TypeArguments="x:String" Name="AIT2" />
                    <Variable x:TypeArguments="x:String" Name="AIT3" />
                    <Variable x:TypeArguments="x:String" Name="AIT4" />
                    <Variable x:TypeArguments="x:String" Name="AIT5" />
                    <Variable x:TypeArguments="x:String" Name="AIT6" />
                    <Variable x:TypeArguments="x:String" Name="AIT7" />
                    <Variable x:TypeArguments="iru:ResponseObject" Name="respObj2" />
                    <Variable x:TypeArguments="x:Int32" Name="StatusCode2" />
                    <Variable x:TypeArguments="njl:JToken" Name="out1" />
                  </Sequence.Variables>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_52">
                    <Assign.To>
                      <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary (Of String, Object)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_53">
                    <Assign.To>
                      <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[item]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_54">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT1]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(0)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_55">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT2]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(1)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_56">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT3]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(2)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_57">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT4]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(3)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_58">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT5]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(4)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_59">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT6]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(5)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_60">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT7]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(6)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_98">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[amt]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[LinesDict("LINE_AMOUNT").ToString]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_3" Selection="OK" Text="[amt]" />
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_61">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Int32">[TotalAmount]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Int32">[CInt(amt)+CInt(LinesDict("LINE_AMOUNT").ToString)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_11" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                    <iai:IONAPIRequestWizard.Headers>
                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                        <scg:List x:TypeArguments="x:String" Capacity="4">
                          <x:String>Accept</x:String>
                        </scg:List>
                        <scg:List x:TypeArguments="x:String" Capacity="4">
                          <x:String>application/json</x:String>
                        </scg:List>
                      </scg:List>
                    </iai:IONAPIRequestWizard.Headers>
                    <iai:IONAPIRequestWizard.QueryParameters>
                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                        <scg:List x:TypeArguments="x:String" Capacity="16">
                          <x:String>INBN</x:String>
                          <x:String>RDTP</x:String>
                          <x:String>DIVI</x:String>
                          <x:String>NLAM</x:String>
                          <x:String>AIT1</x:String>
                          <x:String>AIT2</x:String>
                          <x:String>AIT3</x:String>
                          <x:String>AIT4</x:String>
                          <x:String>AIT5</x:String>
                          <x:String>AIT6</x:String>
                          <x:String>AIT7</x:String>
                        </scg:List>
                        <scg:List x:TypeArguments="x:String" Capacity="16">
                          <x:String>inbnValue</x:String>
                          <x:String>8</x:String>
                          <x:String>division</x:String>
                          <x:String>amt</x:String>
                          <x:String>AIT1</x:String>
                          <x:String>AIT2</x:String>
                          <x:String>AIT3</x:String>
                          <x:String>AIT4</x:String>
                          <x:String>AIT5</x:String>
                          <x:String>AIT6</x:String>
                          <x:String>AIT7</x:String>
                        </scg:List>
                      </scg:List>
                    </iai:IONAPIRequestWizard.QueryParameters>
                  </iai:IONAPIRequestWizard>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_62">
                    <Assign.To>
                      <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_45">
                    <If.Then>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_60">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_63">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String" xml:space="preserve">["Error occured while adding the line.  "  +  (out1("results")(0)("errorMessage")).ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_64">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                          </Assign.Value>
                        </Assign>
                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_6" Line="[commentStatus]" Source="[logfile]" />
                      </Sequence>
                    </If.Then>
                    <If.Else>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_61">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_65">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">["Invoice Line created"]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_66">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">["SUCCESS"]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_7" Line="[commentStatus]" Source="[logfile]" />
                      </Sequence>
                    </If.Else>
                  </If>
                </Sequence>
              </If.Then>
            </If>
          </Sequence>
        </ActivityAction>
      </ForEach>
    </Sequence>
    <Sequence DisplayName="Adding Header" sap2010:WorkflowViewState.IdRef="Sequence_59">
      <Sequence.Variables>
        <Variable x:TypeArguments="x:String" Name="InvoiceNumber_DR" />
        <Variable x:TypeArguments="x:String" Name="Amount_DR" />
      </Sequence.Variables>
      <Assign sap2010:WorkflowViewState.IdRef="Assign_99">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[InvoiceNumber_DR]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">[sino+"_DR"]</InArgument>
        </Assign.Value>
      </Assign>
      <Assign sap2010:WorkflowViewState.IdRef="Assign_100">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[Amount_DR]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">["-"+TotalAmount.ToString]</InArgument>
        </Assign.Value>
      </Assign>
      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_9" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
        <iai:IONAPIRequestWizard.Headers>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>Accept</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>application/json</x:String>
            </scg:List>
          </scg:List>
        </iai:IONAPIRequestWizard.Headers>
        <iai:IONAPIRequestWizard.QueryParameters>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="32">
              <x:String>SUNO</x:String>
              <x:String>IVDT</x:String>
              <x:String>DIVI</x:String>
              <x:String>DINO</x:String>
              <x:String>CUCD</x:String>
              <x:String>TEPY</x:String>
              <x:String>PYME</x:String>
              <x:String>CUAM</x:String>
              <x:String>IMCD</x:String>
              <x:String>CRTP</x:String>
              <x:String>dateformat</x:String>
              <x:String>excludeempty</x:String>
              <x:String>righttrim</x:String>
              <x:String>format</x:String>
              <x:String>extendedresult</x:String>
              <x:String>APCD</x:String>
              <x:String>CORI</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="32">
              <x:String>vendorId</x:String>
              <x:String>ivdate</x:String>
              <x:String>division</x:String>
              <x:String>InvoiceNumber_DR</x:String>
              <x:String>cucd</x:String>
              <x:String>tepy</x:String>
              <x:String>pyme</x:String>
              <x:String>Amount_DR</x:String>
              <x:String>1</x:String>
              <x:String>1</x:String>
              <x:String>YMD8</x:String>
              <x:String>false</x:String>
              <x:String>true</x:String>
              <x:String>PRETTY</x:String>
              <x:String>false</x:String>
              <x:String>authUser</x:String>
              <x:String>correlationID</x:String>
            </scg:List>
          </scg:List>
        </iai:IONAPIRequestWizard.QueryParameters>
      </iai:IONAPIRequestWizard>
      <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_6" Selection="OK" Text="[StatusCode5.ToString]" Title="StatusCode5" />
      <If Condition="[StatusCode5 = 200  and respObj5.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" sap2010:WorkflowViewState.IdRef="If_44">
        <If.Then>
          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_10" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
            <iai:IONAPIRequestWizard.Headers>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>Accept</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>application/json</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.Headers>
            <iai:IONAPIRequestWizard.QueryParameters>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="32">
                  <x:String>SUNO</x:String>
                  <x:String>IVDT</x:String>
                  <x:String>DIVI</x:String>
                  <x:String>SINO</x:String>
                  <x:String>CUCD</x:String>
                  <x:String>TEPY</x:String>
                  <x:String>PYME</x:String>
                  <x:String>CUAM</x:String>
                  <x:String>IMCD</x:String>
                  <x:String>CRTP</x:String>
                  <x:String>dateformat</x:String>
                  <x:String>excludeempty</x:String>
                  <x:String>righttrim</x:String>
                  <x:String>format</x:String>
                  <x:String>extendedresult</x:String>
                  <x:String>APCD</x:String>
                  <x:String>BKID</x:String>
                  <x:String>CORI</x:String>
                  <x:String>DINO</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="32">
                  <x:String>vendorId</x:String>
                  <x:String>ivdate</x:String>
                  <x:String>division</x:String>
                  <x:String>sino</x:String>
                  <x:String>cucd</x:String>
                  <x:String>tepy</x:String>
                  <x:String>pyme</x:String>
                  <x:String>Amount_DR</x:String>
                  <x:String>1</x:String>
                  <x:String>1</x:String>
                  <x:String>YMD8</x:String>
                  <x:String>false</x:String>
                  <x:String>true</x:String>
                  <x:String>PRETTY</x:String>
                  <x:String>false</x:String>
                  <x:String>authUser</x:String>
                  <x:String>bkid</x:String>
                  <x:String>correlationID</x:String>
                  <x:String>InvoiceNumber_DR</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.QueryParameters>
          </iai:IONAPIRequestWizard>
        </If.Then>
      </If>
      <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_7" Selection="OK" Text="[StatusCode5.ToString]" Title="StatusCode5" />
    </Sequence>
    <Sequence DisplayName="Adding Lines wth -ve amount" sap2010:WorkflowViewState.IdRef="Sequence_80">
      <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="ListocrLineValues" sap2010:WorkflowViewState.IdRef="MessageBox_8" Selection="OK" Text="[ListocrLineValues.Count.tostring]" />
      <ForEach x:TypeArguments="scg:Dictionary(x:String, x:Object)" DisplayName="ForEach&lt;Dictionary&lt;String,Object&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_5" Values="[ListocrLineValues]">
        <ActivityAction x:TypeArguments="scg:Dictionary(x:String, x:Object)">
          <ActivityAction.Argument>
            <DelegateInArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="item" />
          </ActivityAction.Argument>
          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_79">
            <Sequence.Variables>
              <Variable x:TypeArguments="iru:ResponseObject" Name="respObj7" />
              <Variable x:TypeArguments="x:Int32" Name="StatusCode7" />
              <Variable x:TypeArguments="njl:JToken" Name="out7" />
            </Sequence.Variables>
            <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_10" Selection="OK" Text="[item(&quot;DESCRIPTION&quot;).ToString]" Title="DESCRIPTION" />
            <If Condition="[item(&quot;DESCRIPTION&quot;).ToString.Contains(&quot;~&quot;)]" sap2010:WorkflowViewState.IdRef="If_54">
              <If.Then>
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_78">
                  <Sequence.Variables>
                    <Variable x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="LinesDict" />
                    <Variable x:TypeArguments="x:String" Name="AIT1" />
                    <Variable x:TypeArguments="x:String" Name="AIT2" />
                    <Variable x:TypeArguments="x:String" Name="AIT3" />
                    <Variable x:TypeArguments="x:String" Name="AIT4" />
                    <Variable x:TypeArguments="x:String" Name="AIT5" />
                    <Variable x:TypeArguments="x:String" Name="AIT6" />
                    <Variable x:TypeArguments="x:String" Name="AIT7" />
                    <Variable x:TypeArguments="iru:ResponseObject" Name="respObj2" />
                    <Variable x:TypeArguments="x:Int32" Name="StatusCode2" />
                    <Variable x:TypeArguments="njl:JToken" Name="out1" />
                  </Sequence.Variables>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_101">
                    <Assign.To>
                      <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary (Of String, Object)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_102">
                    <Assign.To>
                      <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[item]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_103">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT1]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(0)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_104">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT2]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(1)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_105">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT3]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(2)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_106">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT4]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(3)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_107">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT5]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(4)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_108">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT6]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(5)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_109">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT7]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(6)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_110">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[amt]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">["-"+LinesDict("LINE_AMOUNT").ToString]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_4" Selection="OK" Text="[amt]" />
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_111">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Int32">[TotalAmount]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Int32">[CInt(amt)+CInt(LinesDict("LINE_AMOUNT").ToString)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_15" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                    <iai:IONAPIRequestWizard.Headers>
                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                        <scg:List x:TypeArguments="x:String" Capacity="4">
                          <x:String>Accept</x:String>
                        </scg:List>
                        <scg:List x:TypeArguments="x:String" Capacity="4">
                          <x:String>application/json</x:String>
                        </scg:List>
                      </scg:List>
                    </iai:IONAPIRequestWizard.Headers>
                    <iai:IONAPIRequestWizard.QueryParameters>
                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                        <scg:List x:TypeArguments="x:String" Capacity="16">
                          <x:String>INBN</x:String>
                          <x:String>RDTP</x:String>
                          <x:String>DIVI</x:String>
                          <x:String>NLAM</x:String>
                          <x:String>AIT1</x:String>
                          <x:String>AIT2</x:String>
                          <x:String>AIT3</x:String>
                          <x:String>AIT4</x:String>
                          <x:String>AIT5</x:String>
                          <x:String>AIT6</x:String>
                          <x:String>AIT7</x:String>
                        </scg:List>
                        <scg:List x:TypeArguments="x:String" Capacity="16">
                          <x:String>inbnValue</x:String>
                          <x:String>8</x:String>
                          <x:String>division</x:String>
                          <x:String>amt</x:String>
                          <x:String>AIT1</x:String>
                          <x:String>AIT2</x:String>
                          <x:String>AIT3</x:String>
                          <x:String>AIT4</x:String>
                          <x:String>AIT5</x:String>
                          <x:String>AIT6</x:String>
                          <x:String>AIT7</x:String>
                        </scg:List>
                      </scg:List>
                    </iai:IONAPIRequestWizard.QueryParameters>
                  </iai:IONAPIRequestWizard>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_112">
                    <Assign.To>
                      <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_53">
                    <If.Then>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_76">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_113">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String" xml:space="preserve">["Error occured while adding the line.  "  +  (out1("results")(0)("errorMessage")).ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_114">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                          </Assign.Value>
                        </Assign>
                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_11" Line="[commentStatus]" Source="[logfile]" />
                      </Sequence>
                    </If.Then>
                    <If.Else>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_77">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_115">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">["Invoice Line created"]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_116">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">["SUCCESS"]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_12" Line="[commentStatus]" Source="[logfile]" />
                      </Sequence>
                    </If.Else>
                  </If>
                </Sequence>
              </If.Then>
            </If>
          </Sequence>
        </ActivityAction>
      </ForEach>
    </Sequence>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="MessageBox_1" sap:VirtualizedContainerService.HintSize="776.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_97" sap:VirtualizedContainerService.HintSize="776.666666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_329" sap:VirtualizedContainerService.HintSize="1644,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_330" sap:VirtualizedContainerService.HintSize="1644,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_16" sap:VirtualizedContainerService.HintSize="1644,22" />
      <sap2010:ViewStateData Id="Append_Line_13" sap:VirtualizedContainerService.HintSize="1644,22" />
      <sap2010:ViewStateData Id="Assign_117" sap:VirtualizedContainerService.HintSize="1496,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_118" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_14" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_82" sap:VirtualizedContainerService.HintSize="264,247.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_119" sap:VirtualizedContainerService.HintSize="1184,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_120" sap:VirtualizedContainerService.HintSize="1184,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_121" sap:VirtualizedContainerService.HintSize="1184,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_122" sap:VirtualizedContainerService.HintSize="1184,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_123" sap:VirtualizedContainerService.HintSize="1184,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_124" sap:VirtualizedContainerService.HintSize="1184,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_17" sap:VirtualizedContainerService.HintSize="1184,22" />
      <sap2010:ViewStateData Id="Append_Line_15" sap:VirtualizedContainerService.HintSize="1184,22" />
      <sap2010:ViewStateData Id="Assign_125" sap:VirtualizedContainerService.HintSize="1036,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_126" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_16" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_83" sap:VirtualizedContainerService.HintSize="264,247.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_25" sap:VirtualizedContainerService.HintSize="702,22" />
      <sap2010:ViewStateData Id="Assign_231" sap:VirtualizedContainerService.HintSize="554,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_232" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_28" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_124" sap:VirtualizedContainerService.HintSize="264,247.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_29" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_233" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_328" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_234" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_235" sap:VirtualizedContainerService.HintSize="712,62" />
      <sap2010:ViewStateData Id="Assign_236" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_31" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_125" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_237" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_238" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_239" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_240" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_241" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_242" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_243" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_244" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_245" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_246" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_247" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_91" sap:VirtualizedContainerService.HintSize="554,216" />
      <sap2010:ViewStateData Id="Assign_248" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_27" sap:VirtualizedContainerService.HintSize="554,22" />
      <sap2010:ViewStateData Id="Assign_249" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_250" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_251" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_32" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_126" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_252" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_253" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_33" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_127" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_92" sap:VirtualizedContainerService.HintSize="554,504" />
      <sap2010:ViewStateData Id="Sequence_128" sap:VirtualizedContainerService.HintSize="576,2170">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_8" sap:VirtualizedContainerService.HintSize="200,52.6666666666667" />
      <sap2010:ViewStateData Id="Append_Line_34" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_5" sap:VirtualizedContainerService.HintSize="926.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_254" sap:VirtualizedContainerService.HintSize="630.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_255" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_256" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_257" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_258" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_259" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_260" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_261" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_262" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_5" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Sequence_129" sap:VirtualizedContainerService.HintSize="264,768">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_93" sap:VirtualizedContainerService.HintSize="464,922">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_263" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_5" sap:VirtualizedContainerService.HintSize="468.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_5" sap:VirtualizedContainerService.HintSize="482.666666666667,1160" />
      <sap2010:ViewStateData Id="Sequence_130" sap:VirtualizedContainerService.HintSize="504.666666666667,1590">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_94" sap:VirtualizedContainerService.HintSize="630.666666666667,1744">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_131" sap:VirtualizedContainerService.HintSize="652.666666666667,1970">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_95" sap:VirtualizedContainerService.HintSize="926.666666666667,2124" />
      <sap2010:ViewStateData Id="InvokeWorkflow_6" sap:VirtualizedContainerService.HintSize="778.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_264" sap:VirtualizedContainerService.HintSize="630.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_265" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_266" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_267" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_268" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_269" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_270" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_271" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_272" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_273" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_6" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Sequence_132" sap:VirtualizedContainerService.HintSize="264,768">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_96" sap:VirtualizedContainerService.HintSize="464,922">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_274" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_6" sap:VirtualizedContainerService.HintSize="468.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_6" sap:VirtualizedContainerService.HintSize="482.666666666667,1160">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_133" sap:VirtualizedContainerService.HintSize="504.666666666667,1692">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_97" sap:VirtualizedContainerService.HintSize="630.666666666667,1846" />
      <sap2010:ViewStateData Id="Sequence_134" sap:VirtualizedContainerService.HintSize="652.666666666667,2072">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_98" sap:VirtualizedContainerService.HintSize="778.666666666667,2226">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_135" sap:VirtualizedContainerService.HintSize="800.666666666667,2412">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_99" sap:VirtualizedContainerService.HintSize="926.666666666667,2566" />
      <sap2010:ViewStateData Id="Sequence_136" sap:VirtualizedContainerService.HintSize="948.666666666667,4916">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_100" sap:VirtualizedContainerService.HintSize="1074.66666666667,5070" />
      <sap2010:ViewStateData Id="If_101" sap:VirtualizedContainerService.HintSize="1200.66666666667,5224" />
      <sap2010:ViewStateData Id="InvokeWorkflow_7" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Assign_275" sap:VirtualizedContainerService.HintSize="630.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_276" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_277" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_278" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_279" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_280" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_281" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_282" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_283" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_7" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Sequence_137" sap:VirtualizedContainerService.HintSize="264,768">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_102" sap:VirtualizedContainerService.HintSize="464,922">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_284" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_7" sap:VirtualizedContainerService.HintSize="468.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_7" sap:VirtualizedContainerService.HintSize="482.666666666667,1160" />
      <sap2010:ViewStateData Id="Sequence_138" sap:VirtualizedContainerService.HintSize="504.666666666667,1590">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_103" sap:VirtualizedContainerService.HintSize="630.666666666667,1744">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_139" sap:VirtualizedContainerService.HintSize="652.666666666667,1970">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_104" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_8" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_285" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_286" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_287" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_288" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_289" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_290" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_291" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_292" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_293" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_294" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_8" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Sequence_140" sap:VirtualizedContainerService.HintSize="264,768">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_105" sap:VirtualizedContainerService.HintSize="464,922">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_295" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_8" sap:VirtualizedContainerService.HintSize="468.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_8" sap:VirtualizedContainerService.HintSize="482.666666666667,1160">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_141" sap:VirtualizedContainerService.HintSize="504.666666666667,1692">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_106" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_142" sap:VirtualizedContainerService.HintSize="264,278.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_107" sap:VirtualizedContainerService.HintSize="464,432.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_143" sap:VirtualizedContainerService.HintSize="486,618.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_108" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_144" sap:VirtualizedContainerService.HintSize="222,331.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_109" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_110" sap:VirtualizedContainerService.HintSize="2301.33333333333,5378">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_296" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_297" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_298" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_299" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_300" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_301" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_302" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_303" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_304" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_305" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_306" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_111" sap:VirtualizedContainerService.HintSize="554,216" />
      <sap2010:ViewStateData Id="Assign_307" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_28" sap:VirtualizedContainerService.HintSize="554,22" />
      <sap2010:ViewStateData Id="Assign_308" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_309" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_310" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_35" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_145" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_311" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_312" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_36" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_146" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_112" sap:VirtualizedContainerService.HintSize="554,504" />
      <sap2010:ViewStateData Id="Sequence_147" sap:VirtualizedContainerService.HintSize="576,2170">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_9" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_313" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_314" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_315" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_316" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_317" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_318" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_319" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_320" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_29" sap:VirtualizedContainerService.HintSize="554,22" />
      <sap2010:ViewStateData Id="Assign_321" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_322" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_323" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_37" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_148" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_324" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_325" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_38" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_149" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_113" sap:VirtualizedContainerService.HintSize="554,504" />
      <sap2010:ViewStateData Id="Sequence_150" sap:VirtualizedContainerService.HintSize="576,1608">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_114" sap:VirtualizedContainerService.HintSize="2301.33333333333,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_151" sap:VirtualizedContainerService.HintSize="2323.33333333333,5594.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_115" sap:VirtualizedContainerService.HintSize="2549.33333333333,5748.66666666667" />
      <sap2010:ViewStateData Id="Sequence_152" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_116" sap:VirtualizedContainerService.HintSize="464,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_326" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_327" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_30" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_31" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_117" sap:VirtualizedContainerService.HintSize="464,214" />
      <sap2010:ViewStateData Id="Sequence_153" sap:VirtualizedContainerService.HintSize="486,542">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_118" sap:VirtualizedContainerService.HintSize="464,52.6666666666667" />
      <sap2010:ViewStateData Id="Sequence_154" sap:VirtualizedContainerService.HintSize="486,430.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_119" sap:VirtualizedContainerService.HintSize="712,584.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_155" sap:VirtualizedContainerService.HintSize="734,810.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_120" sap:VirtualizedContainerService.HintSize="860,962.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_2" sap:VirtualizedContainerService.HintSize="242,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_156" sap:VirtualizedContainerService.HintSize="264,548">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_121" sap:VirtualizedContainerService.HintSize="554,700">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_157" sap:VirtualizedContainerService.HintSize="576,925.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_122" sap:VirtualizedContainerService.HintSize="702,1077.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_158" sap:VirtualizedContainerService.HintSize="724,1263.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_127" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_128" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_129" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_130" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_131" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_132" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_133" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_119" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_1" sap:VirtualizedContainerService.HintSize="724,148.666666666667" />
      <sap2010:ViewStateData Id="Sequence_160" sap:VirtualizedContainerService.HintSize="746,1576">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_87" sap:VirtualizedContainerService.HintSize="1036,1728">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_120" sap:VirtualizedContainerService.HintSize="1058,1953.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_88" sap:VirtualizedContainerService.HintSize="1184,2105.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_121" sap:VirtualizedContainerService.HintSize="1206,2961.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_89" sap:VirtualizedContainerService.HintSize="1496,3113.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_122" sap:VirtualizedContainerService.HintSize="1518,3338.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_90" sap:VirtualizedContainerService.HintSize="1644,3490.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_123" sap:VirtualizedContainerService.HintSize="1666,3941.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_81" sap:VirtualizedContainerService.HintSize="776.666666666667,52">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="MessageBox_5" sap:VirtualizedContainerService.HintSize="754.666666666667,22" />
      <sap2010:ViewStateData Id="MessageBox_9" sap:VirtualizedContainerService.HintSize="702,22" />
      <sap2010:ViewStateData Id="Assign_52" sap:VirtualizedContainerService.HintSize="554,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_53" sap:VirtualizedContainerService.HintSize="554,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_54" sap:VirtualizedContainerService.HintSize="554,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_55" sap:VirtualizedContainerService.HintSize="554,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_56" sap:VirtualizedContainerService.HintSize="554,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_57" sap:VirtualizedContainerService.HintSize="554,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_58" sap:VirtualizedContainerService.HintSize="554,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_59" sap:VirtualizedContainerService.HintSize="554,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_60" sap:VirtualizedContainerService.HintSize="554,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_98" sap:VirtualizedContainerService.HintSize="554,61.3333333333333" />
      <sap2010:ViewStateData Id="MessageBox_3" sap:VirtualizedContainerService.HintSize="554,22" />
      <sap2010:ViewStateData Id="Assign_61" sap:VirtualizedContainerService.HintSize="554,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_11" sap:VirtualizedContainerService.HintSize="554,22" />
      <sap2010:ViewStateData Id="Assign_62" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_63" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_64" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_6" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_60" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_65" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_66" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_7" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_61" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_45" sap:VirtualizedContainerService.HintSize="554,504">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_62" sap:VirtualizedContainerService.HintSize="576,1968.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_52" sap:VirtualizedContainerService.HintSize="702,2120.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_73" sap:VirtualizedContainerService.HintSize="724,2306.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_4" sap:VirtualizedContainerService.HintSize="754.666666666667,2458">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_74" sap:VirtualizedContainerService.HintSize="776.666666666667,52">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_99" sap:VirtualizedContainerService.HintSize="464,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_100" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_9" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="MessageBox_6" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_10" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_44" sap:VirtualizedContainerService.HintSize="464,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="MessageBox_7" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Sequence_59" sap:VirtualizedContainerService.HintSize="776.666666666667,727.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="MessageBox_8" sap:VirtualizedContainerService.HintSize="754,22" />
      <sap2010:ViewStateData Id="MessageBox_10" sap:VirtualizedContainerService.HintSize="701.333333333333,22" />
      <sap2010:ViewStateData Id="Assign_101" sap:VirtualizedContainerService.HintSize="553.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_102" sap:VirtualizedContainerService.HintSize="553.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_103" sap:VirtualizedContainerService.HintSize="553.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_104" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_105" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_106" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_107" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_108" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_109" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_110" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="MessageBox_4" sap:VirtualizedContainerService.HintSize="553,22" />
      <sap2010:ViewStateData Id="Assign_111" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_15" sap:VirtualizedContainerService.HintSize="553,22" />
      <sap2010:ViewStateData Id="Assign_112" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_113" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_114" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_11" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_76" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_115" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_116" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_12" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_77" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_53" sap:VirtualizedContainerService.HintSize="553,494">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_78" sap:VirtualizedContainerService.HintSize="575.333333333333,1948">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_54" sap:VirtualizedContainerService.HintSize="701.333333333333,2102">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_79" sap:VirtualizedContainerService.HintSize="723.333333333333,2288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_5" sap:VirtualizedContainerService.HintSize="754,2440.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_80" sap:VirtualizedContainerService.HintSize="776.666666666667,2626.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_75" sap:VirtualizedContainerService.HintSize="798.666666666667,3865.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="304,698.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="ShouldCollapseAll">False</x:Boolean>
            <x:Boolean x:Key="ShouldExpandAll">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>