﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Desktop;assembly=Infor.Activities.Desktop"
 xmlns:iad1="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Web;assembly=Infor.Activities.Web"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="documentPath" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="promptPath" Type="InArgument(x:String)" />
    <x:Property Name="customPrompt" Type="InArgument(x:String)" />
    <x:Property Name="values" Type="InArgument(x:String)" />
    <x:Property Name="DictOcrValues" Type="OutArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="ListOcrLineValues" Type="OutArgument(scg:List(scg:Dictionary(x:String, x:Object)))" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.OCR</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System.IO</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.Text.RegularExpressions</x:String>
      <x:String>System.Windows.Forms</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.OCR</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
      <AssemblyReference>System.Windows.Forms</AssemblyReference>
      <AssemblyReference>System.Collections.Immutable</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="GenAI Sequence" sap2010:WorkflowViewState.IdRef="Sequence_3">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:Int32" Name="responseCode" />
      <Variable x:TypeArguments="x:String" Name="promptText" />
      <Variable x:TypeArguments="x:String" Name="promptRequest" />
      <Variable x:TypeArguments="x:String" Name="screenshotPath" />
      <Variable x:TypeArguments="x:String" Name="base64string" />
      <Variable x:TypeArguments="x:String" Name="customText" />
      <Variable x:TypeArguments="x:Boolean" Name="customExists" />
      <Variable x:TypeArguments="x:String" Name="IonBody" />
      <Variable x:TypeArguments="x:Int32" Name="genAIRespCode" />
      <Variable x:TypeArguments="njl:JToken" Name="genAIRequestToken" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="genAIResponse" />
      <Variable x:TypeArguments="njl:JToken" Name="IonBodyJson" />
      <Variable x:TypeArguments="njl:JToken" Name="out1" />
      <Variable x:TypeArguments="x:String" Name="out2" />
      <Variable x:TypeArguments="njl:JToken" Name="jout" />
      <Variable x:TypeArguments="njl:JArray" Name="HeadersJArray" />
      <Variable x:TypeArguments="x:Int32" Name="i" />
      <Variable x:TypeArguments="x:String" Name="strKey" />
      <Variable x:TypeArguments="x:String" Name="strDeliveryNote" />
      <Variable x:TypeArguments="x:Int32" Name="intLineDictCOunter" />
      <Variable x:TypeArguments="x:String" Name="strPONumber" />
      <Variable x:TypeArguments="x:String" Name="GenAIModel" />
      <Variable x:TypeArguments="x:String" Name="GenAIModelVersion" />
      <Variable x:TypeArguments="x:String" Name="outputStructure" />
      <Variable x:TypeArguments="x:String" Name="value" />
    </Sequence.Variables>
    <Sequence DisplayName="Read Prompt and Initialize Values" sap2010:WorkflowViewState.IdRef="Sequence_46">
      <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Read Text File" sap2010:WorkflowViewState.IdRef="File_Read_1" Source="[promptPath]" Text="[promptText]" />
      <Assign DisplayName="Initialize DictOcrValues" sap2010:WorkflowViewState.IdRef="Assign_113">
        <Assign.To>
          <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[DictOcrValues]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)" xml:space="preserve">[New Dictionary(Of String, Object) From {
    {"INVOICE_RECEIPT_ID", ""},
    {"INVOICE_RECEIPT_DATE", ""},
    {"TOTAL", ""},
    {"SUBTOTAL", ""},
    {"PO_NUMBER", ""},
    {"VENDOR_NAME", ""},
    {"VENDOR_ADDRESS", ""},
    {"VENDOR_PHONE", ""},
    {"SHIP_TO_ADDRESS", ""},
    {"BILL_TO_ADDRESS", ""},
    {"ISO_CURRENCY_CODE", ""},
    {"REFERENCE", ""},
    {"UNIQUE_REGISTRATION_CODE", ""},
    {"DOCUMENT_CLASS", ""},
    {"REMIT_TO_NAME", ""},
    {"REMIT_TO_ADDRESS", ""},
    {"VAT_PERCENTAGE", ""},
    {"VAT_SUBTOTAL", ""},
    {"VAT_AMOUNT", ""},
    {"SHIPPING_AND_HANDLING_PERCENTAGE", ""},
    {"SHIPPING_AND_HANDLING_SUBTOTAL", ""},
    {"SHIPPING_AND_HANDLING_AMOUNT", ""},
    {"Invoice_Level_Discount_PERCENTAGE", ""},
    {"Invoice_Level_Discount_SUBTOTAL", ""},
    {"Invoice_Level_Discount_AMOUNT", ""},
    {"OtherCharges_PERCENTAGE", ""},
    {"OtherCharges_SUBTOTAL", ""},
    {"OtherCharges_AMOUNT", ""},
    {"DELIVERY_NOTE_DATA", ""},
    {"EXCEPTION_CATEGORY",""}
}]</InArgument>
        </Assign.Value>
      </Assign>
      <Assign DisplayName="Initialize DictOcrLineValues" sap2010:WorkflowViewState.IdRef="Assign_114">
        <Assign.To>
          <OutArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListOcrLineValues]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[New List(Of Dictionary(Of String,Object))]</InArgument>
        </Assign.Value>
      </Assign>
      <Sequence DisplayName="Open Browser and Capture ScreensotSequence" sap2010:WorkflowViewState.IdRef="Sequence_29">
        <iaw:OpenBrowser ErrorCode="{x:Null}" Browser="Chrome" ContinueOnError="True" DisplayName="Open Browser" sap2010:WorkflowViewState.IdRef="OpenBrowser_2" PrivateBrowsingEnabled="False" URL="https://www.google.com/" ZoomPercentage="60" />
        <iaw:NavigateTo ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Navigate To" sap2010:WorkflowViewState.IdRef="NavigateTo_2" URL="[Path.GetDirectoryName(documentPath) + &quot;/&quot; + Uri.EscapeDataString(Path.GetFileName(documentPath))]" WaitAfter="0" WaitBefore="0" ZoomPercentage="100" />
        <iaw:MaximizeWindow ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Maximize Window" sap2010:WorkflowViewState.IdRef="MaximizeWindow_2" WaitAfter="0" WaitBefore="0" />
        <iad:SendKeys ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Send Keys" sap2010:WorkflowViewState.IdRef="SendKeys_1" IsWeb="False" Text="^-" WaitAfter="0" WaitBefore="0" />
        <iad:SendKeys ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Send Keys" sap2010:WorkflowViewState.IdRef="SendKeys_3" IsWeb="False" Text="^-" WaitAfter="0" WaitBefore="1000" />
        <iad:SendKeys ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Send Keys" sap2010:WorkflowViewState.IdRef="SendKeys_2" IsWeb="False" Text="^-" WaitAfter="0" WaitBefore="1000" />
        <iaw:ScreenShot ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Take Screenshot" sap2010:WorkflowViewState.IdRef="ScreenShot_2" SaveLocation="[screenshotPath]" WaitAfter="0" WaitBefore="10000" />
        <ias:FileToBase64 ErrorCode="{x:Null}" Base64="[base64string]" ContinueOnError="True" DisplayName="File to Base64 String" FilePath="[screenshotPath]" sap2010:WorkflowViewState.IdRef="FileToBase64_2" />
        <iaw:CloseBrowser ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Close Browser" sap2010:WorkflowViewState.IdRef="CloseBrowser_2" />
      </Sequence>
      <Assign sap2010:WorkflowViewState.IdRef="Assign_93">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[customText]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">
            <Literal x:TypeArguments="x:String" Value="" />
          </InArgument>
        </Assign.Value>
      </Assign>
      <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_1" IsValid="[customExists]" Path="[customPrompt]" />
      <If Condition="[customExists]" DisplayName="If customExists" sap2010:WorkflowViewState.IdRef="If_26">
        <If.Then>
          <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Read Text File" sap2010:WorkflowViewState.IdRef="File_Read_5" Source="[customPrompt]" Text="[customText]" />
        </If.Then>
      </If>
      <Assign sap2010:WorkflowViewState.IdRef="Assign_322">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[GenAIModel]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">[miscValues("GenAIModel").ToString]</InArgument>
        </Assign.Value>
      </Assign>
      <Assign sap2010:WorkflowViewState.IdRef="Assign_323">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[GenAIModelVersion]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">[miscValues("GenAIModelVersion").ToString]</InArgument>
        </Assign.Value>
      </Assign>
      <iad1:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_41">
        <iad1:CommentOut.Activities>
          <Sequence DisplayName="Prompt API Sequence" sap2010:WorkflowViewState.IdRef="Sequence_124">
            <Assign sap2010:WorkflowViewState.IdRef="Assign_98">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[promptText.Replace("{text}",values.tostring)]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_99">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[promptRequest.Replace("{notes}",customText.tostring)]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_100">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[promptRequest.Replace("{text}",Regex.Replace(Values.ToString(), "[^A-Za-z0-9\s!@#$%^&amp;*()_+={}\[\]:;,'&lt;&gt;.?//|-]", String.Empty))]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_101">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[promptRequest.Replace("'","\'")]</InArgument>
              </Assign.Value>
            </Assign>
            <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_7" Template="{}{ &#xA;'config': { &#xA;'temperature': 0, &#xA;'max_response':8192,&#xA;'stop_sequence': [&#xA;                      'Explanations','Explanation'&#xA;                ]   &#xA;}, &#xA;'prompt':'{{%prompt%}}',&#xA;'model': '{{%model%}}',   'version': '{{%version%}}','encoded_image': 'data:image/png;base64,{{%base64string%}}'}&#xA;" Text="[IonBody]">
              <ias:Template_Apply.Values>
                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>prompt</x:String>
                    <x:String>base64string</x:String>
                    <x:String>model</x:String>
                    <x:String>version</x:String>
                  </scg:List>
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>promptRequest</x:String>
                    <x:String>base64string</x:String>
                    <x:String>GenAIModel</x:String>
                    <x:String>GenAIModelVersion</x:String>
                  </scg:List>
                </scg:List>
              </ias:Template_Apply.Values>
            </ias:Template_Apply>
          </Sequence>
        </iad1:CommentOut.Activities>
      </iad1:CommentOut>
      <Sequence DisplayName="Messages API Sequence - with Image" sap2010:WorkflowViewState.IdRef="Sequence_125">
        <Assign sap2010:WorkflowViewState.IdRef="Assign_326">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[promptText.Replace("{notes}",customText.tostring)]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_327">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[promptRequest.Replace("'","\'")]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_328">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[outputStructure]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[promptRequest.Split(New String() {"====="}, StringSplitOptions.None)(1)]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_329">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[promptRequest.Split(New String() {"====="}, StringSplitOptions.None)(0)]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_330">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[value]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[Regex.Replace(values.tostring, "[^A-Za-z0-9\s!@#$%^&amp;*()_+={}\[\]:;,'&lt;&gt;.?//|-]", String.Empty).Replace("'","\'")]</InArgument>
          </Assign.Value>
        </Assign>
        <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Latest Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_8" Template="{}{&#xA;  'model': '{{%model%}}',&#xA;  'version': '{{%version%}}',&#xA;  'temperature': 0,&#xA;  'max_response': 8192,&#xA;  'stop_sequence': [&#xA;    'Explanations',&#xA;    'Explanation'&#xA;  ],&#xA;  'system': 'You are an expert document based Question-Answering tool. Your task is to answer the 2 questions using the image of only the first page in the document as reference for layout and empty values, but the extraction of values must be from the raw text.',&#xA;  'messages': [&#xA;      {&#xA;        'role': 'user',&#xA;        'content': [&#xA;          {&#xA;            'type': 'image',&#xA;            'data': 'data:image/png;base64,{{%base64string%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Read the following instructions'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%prompt%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%ocrText%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Use the following output format for your response. Your response should only have the JSON without code blocks as the response. Do not include any of your analysis statements before or after the JSON in the response. Do not add any additional objects to the JSON that are not present in the following Output Format Structure.'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%outputStructure%}}'&#xA;          }&#xA;        ]&#xA;      },&#xA;      {&#xA;        'role': 'assistant',&#xA;        'content': [&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Based on the invoice document, here are the extracted values in the requested JSON format without any code blocks:'&#xA;          }&#xA;        ]&#xA;      }&#xA;  ]&#xA;}&#xA;" Text="[IonBody]">
          <ias:Template_Apply.Values>
            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
              <scg:List x:TypeArguments="x:String" Capacity="8">
                <x:String>prompt</x:String>
                <x:String>base64string</x:String>
                <x:String>model</x:String>
                <x:String>version</x:String>
                <x:String>ocrText</x:String>
                <x:String>outputStructure</x:String>
              </scg:List>
              <scg:List x:TypeArguments="x:String" Capacity="8">
                <x:String>promptRequest</x:String>
                <x:String>base64string</x:String>
                <x:String>GenAIModel</x:String>
                <x:String>GenAIModelVersion</x:String>
                <x:String>value</x:String>
                <x:String>outputStructure</x:String>
              </scg:List>
            </scg:List>
          </ias:Template_Apply.Values>
        </ias:Template_Apply>
      </Sequence>
      <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_6" JTokenObject="[genAIRequestToken]" JTokenString="[IonBody]" />
      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="False" DisplayName="IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_3" PostData="[genAIRequestToken.tostring]" Response="[genAIResponse]" StatusCode="[genAIRespCode]" Url="[tenantID + &quot;genai/chatsvc/api/v1/messages&quot;]">
        <iai:IONAPIRequestWizard.Headers>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>x-infor-logicalidprefix</x:String>
              <x:String>Accept</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>lid://infor.colemanddp</x:String>
              <x:String>application/json</x:String>
            </scg:List>
          </scg:List>
        </iai:IONAPIRequestWizard.Headers>
        <iai:IONAPIRequestWizard.QueryParameters>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="0" />
            <scg:List x:TypeArguments="x:String" Capacity="0" />
          </scg:List>
        </iai:IONAPIRequestWizard.QueryParameters>
      </iai:IONAPIRequestWizard>
      <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_41" Selection="OK" Text="[genAIResponse.ReadAsText]" />
      <Assign sap2010:WorkflowViewState.IdRef="Assign_314">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[strDeliveryNote]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">
            <Literal x:TypeArguments="x:String" Value="" />
          </InArgument>
        </Assign.Value>
      </Assign>
    </Sequence>
    <Switch x:TypeArguments="x:Boolean" DisplayName="Check genAIRespCode is Success?" Expression="[genAIRespCode = 200]" sap2010:WorkflowViewState.IdRef="Switch`1_28">
      <Sequence x:Key="True" DisplayName="If genAIRespCode success Sequence" sap2010:WorkflowViewState.IdRef="Sequence_55">
        <Sequence.Variables>
          <Variable x:TypeArguments="njl:JArray" Name="LinesJarray" />
          <Variable x:TypeArguments="scg:List(x:String)" Name="lines" />
          <Variable x:TypeArguments="njl:JObject" Name="jobj" />
        </Sequence.Variables>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_115">
          <Assign.To>
            <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(genAIResponse.ReadAsText)]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_116">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[out2]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[out1("content").ToString.Replace("\n","")]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_117">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[out2]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[(out2.ToString.Replace("None","''")).Replace("''''","''").Replace("\","\\").replace("\\""","")]</InArgument>
          </Assign.Value>
        </Assign>
        <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_7" JTokenObject="[jout]" JTokenString="[out2.ToString]" />
        <iad1:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_40">
          <iad1:CommentOut.Activities>
            <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_40" Selection="OK" Text="[jout.ToString]" Title="GenAI Output" />
          </iad1:CommentOut.Activities>
        </iad1:CommentOut>
        <Sequence DisplayName="Headers Dictionary Sequence" sap2010:WorkflowViewState.IdRef="Sequence_57">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_139">
            <Assign.To>
              <OutArgument x:TypeArguments="njl:JArray">[HeadersJArray]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="njl:JArray">[JArray.FromObject(Jout("Headers"))]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_119">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Int32">[i]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Int32">0</InArgument>
            </Assign.Value>
          </Assign>
          <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach Headers JToken" sap2010:WorkflowViewState.IdRef="ForEach`1_13" Values="[HeadersJArray]">
            <ActivityAction x:TypeArguments="njl:JToken">
              <ActivityAction.Argument>
                <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
              </ActivityAction.Argument>
              <Sequence DisplayName="Inside Headers JToken Sequence" sap2010:WorkflowViewState.IdRef="Sequence_68">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_151">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[strKey]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String" xml:space="preserve">[New String() {
    "INVOICE_RECEIPT_ID", "INVOICE_RECEIPT_DATE", "TOTAL", "SUBTOTAL",
    "PO_NUMBER", "VENDOR_NAME", "VENDOR_ADDRESS", "VENDOR_PHONE",
    "SHIP_TO_ADDRESS", "BILL_TO_ADDRESS", "ISO_CURRENCY_CODE", "REFERENCE",
    "UNIQUE_REGISTRATION_CODE", "DOCUMENT_CLASS", "REMIT_TO_NAME", "REMIT_TO_ADDRESS", "IBAN"
}(i)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_153">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:Object">[DictOcrValues(strKey)]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:Object">[item]</InArgument>
                  </Assign.Value>
                </Assign>
                <iad1:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_21">
                  <iad1:CommentOut.Activities>
                    <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_15" Selection="OK" Text="[&quot;key is - &quot;+strKey+Environment.NewLine+&quot;Value is - &quot;+DictOcrValues(strKey).tostring+Environment.NewLine+&quot;i value is - &quot;+i.tostring]" Title="key value pairs" />
                  </iad1:CommentOut.Activities>
                </iad1:CommentOut>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_152">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:Int32">[i]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:Int32">[i+1]</InArgument>
                  </Assign.Value>
                </Assign>
              </Sequence>
            </ActivityAction>
          </ForEach>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_313">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[DictOcrValues("PO_NUMBER")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[CType(String.Join(",", CType(DictOcrValues("PO_NUMBER"), JArray).Select(Function(x) x.ToString())), Object)]</InArgument>
            </Assign.Value>
          </Assign>
          <iad1:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_36">
            <iad1:CommentOut.Activities>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_315">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:Object">[DictOcrValues("PO_NUMBER")]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:Object">[String.Join(",", CType(DictOcrValues("PO_NUMBER"), List(Of String)))]</InArgument>
                </Assign.Value>
              </Assign>
            </iad1:CommentOut.Activities>
          </iad1:CommentOut>
          <Sequence DisplayName="Additional Charges Sequence" sap2010:WorkflowViewState.IdRef="Sequence_59">
            <iad1:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_30">
              <iad1:CommentOut.Activities>
                <Sequence DisplayName="Additional Charges Counters" sap2010:WorkflowViewState.IdRef="Sequence_79">
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_172">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Int32">[a]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Int32">1</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_198">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Int32">[b]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Int32">1</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_199">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Int32">[c]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Int32">1</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_200">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Int32">[d]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Int32">1</InArgument>
                    </Assign.Value>
                  </Assign>
                </Sequence>
                <Switch x:TypeArguments="x:Boolean" DisplayName="Additional Charges Key" Expression="[{&quot;VAT&quot;, &quot;SHIPPING_AND_HANDLING&quot;, &quot;Invoice_Level_Discount&quot;}.Contains(item(1)(0).ToString)]" sap2010:WorkflowViewState.IdRef="Switch`1_22">
                  <Switch x:TypeArguments="x:String" x:Key="True" DisplayName="Handle Known Charges" Expression="[item(1)(0).ToString]" sap2010:WorkflowViewState.IdRef="Switch`1_21">
                    <Sequence x:Key="VAT" DisplayName="VAT Key Sequence" sap2010:WorkflowViewState.IdRef="Sequence_105">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_261">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[strKey]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">["VAT"+a.tostring]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_262">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Int32">[a]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:Int32">[a+1]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                    <Sequence x:Key="SHIPPING_AND_HANDLING" DisplayName="SHIPPING_AND_HANDLING Sequence" sap2010:WorkflowViewState.IdRef="Sequence_106">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_263">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[strKey]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">["SHIPPING_AND_HANDLING"+b.tostring]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_264">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Int32">[b]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:Int32">[b+1]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                    <Sequence x:Key="Invoice_Level_Discount" DisplayName="Invoice_Level_Discount equence" sap2010:WorkflowViewState.IdRef="Sequence_107">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_265">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[strKey]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">["Invoice_Level_Discount"+c.tostring]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_266">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Int32">[c]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:Int32">[c+1]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                  </Switch>
                  <Sequence x:Key="False" DisplayName="AdditionalCharge Key Sequence" sap2010:WorkflowViewState.IdRef="Sequence_108">
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_267">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[strKey]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">["AdditionalCharge"+d.tostring]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_268">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:Int32">[d]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:Int32">[d+1]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </Sequence>
                </Switch>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_178">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:Object">[DictOcrValues(strKey)]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:Object">[item]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_34" Selection="OK" Text="[item.tostring]" Title="Additional Charges Item Value" />
                <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_18" Selection="OK" Text="[&quot;key is - &quot;+strKey+Environment.NewLine+&quot;Value is - &quot;+DictOcrValues(strKey).tostring+Environment.NewLine+&quot;i value from Additional charges is - &quot;+i.tostring+Environment.NewLine+&quot;a value from Additional charges is - &quot;+a.tostring+Environment.NewLine+&quot;b value from Additional charges is - &quot;+b.tostring+Environment.NewLine+&quot;c value from Additional charges is - &quot;+c.tostring+Environment.NewLine+&quot;d value from Additional charges is - &quot;+d.tostring]" Title="key value pairs" />
              </iad1:CommentOut.Activities>
            </iad1:CommentOut>
            <Assign DisplayName="Assign Additional_Charges" sap2010:WorkflowViewState.IdRef="Assign_173">
              <Assign.To>
                <OutArgument x:TypeArguments="njl:JArray">[HeadersJArray]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="njl:JArray">[JArray.FromObject(Jout("Additional_Charges"))]</InArgument>
              </Assign.Value>
            </Assign>
            <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach Headers (Additional Charges) JToken" sap2010:WorkflowViewState.IdRef="ForEach`1_17" Values="[HeadersJArray]">
              <ActivityAction x:TypeArguments="njl:JToken">
                <ActivityAction.Argument>
                  <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
                </ActivityAction.Argument>
                <Switch x:TypeArguments="x:Boolean" DisplayName="Additional Charges" Expression="[item.Count &gt; 1 AndAlso&#xA;    TypeOf item(1) Is JArray AndAlso&#xA;    CType(item(1), JArray).Count &gt; 0 AndAlso&#xA;    {&quot;VAT&quot;, &quot;SHIPPING_AND_HANDLING&quot;, &quot;Invoice_Level_Discount&quot;}.Contains(CType(item(1), JArray)(0).ToString())]" sap2010:WorkflowViewState.IdRef="Switch`1_26">
                  <Switch x:TypeArguments="x:String" x:Key="True" DisplayName="Handle Existing Additional Charges" Expression="[item(1)(0).ToString]" sap2010:WorkflowViewState.IdRef="Switch`1_25">
                    <Sequence x:Key="VAT" DisplayName="VAT Sequence" sap2010:WorkflowViewState.IdRef="Sequence_114">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_284">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Object">[DictOcrValues("VAT_PERCENTAGE")]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("VAT_PERCENTAGE"),
    If(
        String.IsNullOrWhiteSpace(DictOcrValues("VAT_PERCENTAGE").ToString),
        item(1)(1).ToString,
        DictOcrValues("VAT_PERCENTAGE").ToString + "," + item(1)(1).ToString
    ),
    item(1)(1).ToString
)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_285">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Object">[DictOcrValues("VAT_SUBTOTAL")]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("VAT_SUBTOTAL"),
    If(
        String.IsNullOrWhiteSpace(DictOcrValues("VAT_SUBTOTAL").ToString),
        item(1)(2).ToString,
        DictOcrValues("VAT_SUBTOTAL").ToString + "," + item(1)(2).ToString
    ),
    item(1)(2).ToString
)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_286">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Object">[DictOcrValues("VAT_AMOUNT")]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("VAT_AMOUNT"),
    If(
        String.IsNullOrWhiteSpace(DictOcrValues("VAT_AMOUNT").ToString),
        item(1)(3).ToString,
        DictOcrValues("VAT_AMOUNT").ToString + "," + item(1)(3).ToString
    ),
    item(1)(3).ToString
)]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                    <Sequence x:Key="SHIPPING_AND_HANDLING" DisplayName="SHIPPING_AND_HANDLING Sequence" sap2010:WorkflowViewState.IdRef="Sequence_115">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_287">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Object">[DictOcrValues("SHIPPING_AND_HANDLING_PERCENTAGE")]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("SHIPPING_AND_HANDLING_PERCENTAGE"),
    If(
        String.IsNullOrWhiteSpace(DictOcrValues("SHIPPING_AND_HANDLING_PERCENTAGE").ToString),
        item(1)(1).ToString,
        DictOcrValues("SHIPPING_AND_HANDLING_PERCENTAGE").ToString + "," + item(1)(1).ToString
    ),
    item(1)(1).ToString
)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_288">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Object">[DictOcrValues("SHIPPING_AND_HANDLING_SUBTOTAL")]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("SHIPPING_AND_HANDLING_SUBTOTAL"),
    If(
        String.IsNullOrWhiteSpace(DictOcrValues("SHIPPING_AND_HANDLING_SUBTOTAL").ToString),
        item(1)(2).ToString,
        DictOcrValues("SHIPPING_AND_HANDLING_SUBTOTAL").ToString + "," + item(1)(2).ToString
    ),
    item(1)(2).ToString
)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_289">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Object">[DictOcrValues("SHIPPING_AND_HANDLING_AMOUNT")]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("SHIPPING_AND_HANDLING_AMOUNT"),
    If(
        String.IsNullOrWhiteSpace(DictOcrValues("SHIPPING_AND_HANDLING_AMOUNT").ToString),
        item(1)(3).ToString,
        DictOcrValues("SHIPPING_AND_HANDLING_AMOUNT").ToString + "," + item(1)(3).ToString
    ),
    item(1)(3).ToString
)]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                    <Sequence x:Key="Invoice_Level_Discount" DisplayName="Invoice_Level_Discount Sequence" sap2010:WorkflowViewState.IdRef="Sequence_116">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_290">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Object">[DictOcrValues("Invoice_Level_Discount_PERCENTAGE")]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("Invoice_Level_Discount_PERCENTAGE"),
    If(
        String.IsNullOrWhiteSpace(DictOcrValues("Invoice_Level_Discount_PERCENTAGE").ToString),
        item(1)(1).ToString,
        DictOcrValues("Invoice_Level_Discount_PERCENTAGE").ToString + "," + item(1)(1).ToString
    ),
    item(1)(1).ToString
)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_291">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Object">[DictOcrValues("Invoice_Level_Discount_SUBTOTAL")]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("Invoice_Level_Discount_SUBTOTAL"),
    If(
        String.IsNullOrWhiteSpace(DictOcrValues("Invoice_Level_Discount_SUBTOTAL").ToString),
        item(1)(2).ToString,
        DictOcrValues("Invoice_Level_Discount_SUBTOTAL").ToString + "," + item(1)(2).ToString
    ),
    item(1)(2).ToString
)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_292">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Object">[DictOcrValues("Invoice_Level_Discount_AMOUNT")]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("Invoice_Level_Discount_AMOUNT"),
    If(
        String.IsNullOrWhiteSpace(DictOcrValues("Invoice_Level_Discount_AMOUNT").ToString),
        item(1)(3).ToString,
        DictOcrValues("Invoice_Level_Discount_AMOUNT").ToString + "," + item(1)(3).ToString
    ),
    item(1)(3).ToString
)]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                  </Switch>
                  <Sequence x:Key="False" DisplayName="Other Additional Charges Sequence" sap2010:WorkflowViewState.IdRef="Sequence_117">
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_316">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:Object">[DictOcrValues("OtherCharges_PERCENTAGE")]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:Object" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("OtherCharges_PERCENTAGE"),
    If(
        item.Count &gt; 1 AndAlso TypeOf item(1) Is JArray AndAlso CType(item(1), JArray).Count &gt; 1,
        DictOcrValues("OtherCharges_PERCENTAGE").ToString() &amp; "," &amp; CType(item(1), JArray)(1).ToString(),
        DictOcrValues("OtherCharges_PERCENTAGE").ToString()
    ),
    If(
        item.Count &gt; 1 AndAlso TypeOf item(1) Is JArray AndAlso CType(item(1), JArray).Count &gt; 1,
        CType(item(1), JArray)(1).ToString(),
        ""
    )
)]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_317">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:Object">[DictOcrValues("OtherCharges_SUBTOTAL")]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:Object" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("OtherCharges_SUBTOTAL"),
    If(
        item.Count &gt; 1 AndAlso TypeOf item(1) Is JArray AndAlso CType(item(1), JArray).Count &gt; 2,
        DictOcrValues("OtherCharges_SUBTOTAL").ToString() &amp; "," &amp; CType(item(1), JArray)(2).ToString(),
        DictOcrValues("OtherCharges_SUBTOTAL").ToString()
    ),
    If(
        item.Count &gt; 1 AndAlso TypeOf item(1) Is JArray AndAlso CType(item(1), JArray).Count &gt; 2,
        CType(item(1), JArray)(2).ToString(),
        ""
    )
)]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_318">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:Object">[DictOcrValues("OtherCharges_AMOUNT")]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:Object" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("OtherCharges_AMOUNT"),
    If(
        item.Count &gt; 1 AndAlso TypeOf item(1) Is JArray AndAlso CType(item(1), JArray).Count &gt; 3,
        DictOcrValues("OtherCharges_AMOUNT").ToString() &amp; "," &amp; CType(item(1), JArray)(3).ToString(),
        DictOcrValues("OtherCharges_AMOUNT").ToString()
    ),
    If(
        item.Count &gt; 1 AndAlso TypeOf item(1) Is JArray AndAlso CType(item(1), JArray).Count &gt; 3,
        CType(item(1), JArray)(3).ToString(),
        ""
    )
)]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <iad1:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_37">
                      <iad1:CommentOut.Activities>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_293">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Object">[DictOcrValues("OtherCharges_PERCENTAGE")]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("OtherCharges_PERCENTAGE"),
    If(
        String.IsNullOrWhiteSpace(DictOcrValues("OtherCharges_PERCENTAGE").ToString),
        item(1)(1).ToString,
        DictOcrValues("OtherCharges_PERCENTAGE").ToString + "," + item(1)(1).ToString
    ),
    item(1)(1).ToString
)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_294">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Object">[DictOcrValues("OtherCharges_SUBTOTAL")]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("OtherCharges_SUBTOTAL"),
    If(
        String.IsNullOrWhiteSpace(DictOcrValues("OtherCharges_SUBTOTAL").ToString),
        item(1)(2).ToString,
        DictOcrValues("OtherCharges_SUBTOTAL").ToString + "," + item(1)(2).ToString
    ),
    item(1)(2).ToString
)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_295">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Object">[DictOcrValues("OtherCharges_AMOUNT")]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("OtherCharges_AMOUNT"),
    If(
        String.IsNullOrWhiteSpace(DictOcrValues("OtherCharges_AMOUNT").ToString),
        item(1)(3).ToString,
        DictOcrValues("OtherCharges_AMOUNT").ToString + "," + item(1)(3).ToString
    ),
    item(1)(3).ToString
)]</InArgument>
                          </Assign.Value>
                        </Assign>
                      </iad1:CommentOut.Activities>
                    </iad1:CommentOut>
                  </Sequence>
                </Switch>
              </ActivityAction>
            </ForEach>
          </Sequence>
        </Sequence>
        <Sequence DisplayName="Lines Sequence" sap2010:WorkflowViewState.IdRef="Sequence_58">
          <Sequence.Variables>
            <Variable x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="LinesDict" />
            <Variable x:TypeArguments="njl:JArray" Name="detailValuesJArray" />
          </Sequence.Variables>
          <Assign DisplayName="Extract Lines from Jout to LinesJarray" sap2010:WorkflowViewState.IdRef="Assign_129">
            <Assign.To>
              <OutArgument x:TypeArguments="njl:JArray">[LinesJarray]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="njl:JArray">[JArray.FromObject(Jout("Lines"))]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Initialize intLineDictCounter" sap2010:WorkflowViewState.IdRef="Assign_224">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Int32">[intLineDictCounter]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Int32">0</InArgument>
            </Assign.Value>
          </Assign>
          <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach PO in LinesJarray" sap2010:WorkflowViewState.IdRef="ForEach`1_18" Values="[LinesJarray]">
            <ActivityAction x:TypeArguments="njl:JToken">
              <ActivityAction.Argument>
                <DelegateInArgument x:TypeArguments="njl:JToken" Name="PoNO" />
              </ActivityAction.Argument>
              <ForEach x:TypeArguments="njl:JProperty" DisplayName="Each Line in LinesJArray" sap2010:WorkflowViewState.IdRef="ForEach`1_23" Values="[CType(PoNO, JObject).Properties]">
                <ActivityAction x:TypeArguments="njl:JProperty">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="njl:JProperty" Name="Line" />
                  </ActivityAction.Argument>
                  <Switch x:TypeArguments="x:String" DisplayName="Switch Between PO_Number|Detail|PO_Total_Amount" Expression="[Line.Name]" sap2010:WorkflowViewState.IdRef="Switch`1_27">
                    <Assign x:Key="PO_Number" sap2010:WorkflowViewState.IdRef="Assign_297">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[strPONumber]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[Line.Value.Tostring]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Sequence x:Key="Details" sap2010:Annotation.AnnotationText="Details contains all lines and 11 fields data from each line" DisplayName="Details JArray Sequence" sap2010:WorkflowViewState.IdRef="Sequence_120">
                      <Sequence.Variables>
                        <Variable x:TypeArguments="njl:JArray" Name="JADetail" />
                      </Sequence.Variables>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_298">
                        <Assign.To>
                          <OutArgument x:TypeArguments="njl:JArray">[detailValuesJArray]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="njl:JArray">[JArray.FromObject(PoNO("Details"))]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <iad1:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_33">
                        <iad1:CommentOut.Activities>
                          <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_36" Selection="OK" Text="[detailValuesJArray.count.tostring]" Title="Total lines in Current PO - detailValuesJArray count." />
                        </iad1:CommentOut.Activities>
                      </iad1:CommentOut>
                      <ForEach x:TypeArguments="njl:JToken" DisplayName="Loop each Line in a PO" sap2010:WorkflowViewState.IdRef="ForEach`1_34" Values="[detailValuesJArray]">
                        <ActivityAction x:TypeArguments="njl:JToken">
                          <ActivityAction.Argument>
                            <DelegateInArgument x:TypeArguments="njl:JToken" Name="detail" />
                          </ActivityAction.Argument>
                          <Sequence DisplayName="Add All details to LinesDict" sap2010:WorkflowViewState.IdRef="Sequence_119">
                            <Sequence.Variables>
                              <Variable x:TypeArguments="x:String" Name="strTempDetail" />
                            </Sequence.Variables>
                            <iad1:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_34">
                              <iad1:CommentOut.Activities>
                                <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" sap2010:Annotation.AnnotationText="Contains all 11 parameters of current line" ContinueOnError="False" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_37" Selection="OK" Text="[detail.tostring]" Title="detail - All Data of a single line as List" />
                              </iad1:CommentOut.Activities>
                            </iad1:CommentOut>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_299">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[strTempDetail]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[detail.ToString().Replace("'", "")]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <iad1:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_35">
                              <iad1:CommentOut.Activities>
                                <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_38" Selection="OK" Text="[strTempDetail]" Title="Cleaned String - strTempDetail" />
                              </iad1:CommentOut.Activities>
                            </iad1:CommentOut>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_300">
                              <Assign.To>
                                <OutArgument x:TypeArguments="njl:JArray">[JADetail]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="njl:JArray">[JArray.Parse(strTempDetail)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_301">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Int32">[i]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Int32">0</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_302">
                              <Assign.To>
                                <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)" xml:space="preserve">[New Dictionary(Of String, Object) From {
    {"SUPPLIER_ITEM_CODE", ""},
    {"CUSTOMER_ITEM_CODE", ""},
    {"DESCRIPTION", ""},
    {"ITEM_STATUS", ""},
    {"QUANTITY", ""},
    {"UNIT_PRICE", ""},
    {"LINE_LEVEL_ITEM_DISCOUNT", ""},
    {"UNIT_OF_MEASURE", ""},
    {"LINE_AMOUNT", ""},
    {"WEIGHT_OR_CARTONQUANTITY", ""},
    {"DELIVERY_NOTE_DATA", ""},
    {"PO_Number", ""},
    {"PO_Total_Amount", ""},
    {"DELIVERY_NOTE_NUMBER", ""}
}]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <ForEach x:TypeArguments="njl:JToken" DisplayName="Extract Line Details and Create Dictionay" sap2010:WorkflowViewState.IdRef="ForEach`1_33" Values="[JADetail]">
                              <ActivityAction x:TypeArguments="njl:JToken">
                                <ActivityAction.Argument>
                                  <DelegateInArgument x:TypeArguments="njl:JToken" Name="element" />
                                </ActivityAction.Argument>
                                <Sequence DisplayName="Add Details data" sap2010:WorkflowViewState.IdRef="Sequence_118">
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_303">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[strKey]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[New String() { "SUPPLIER_ITEM_CODE", "CUSTOMER_ITEM_CODE", "DESCRIPTION", "ITEM_STATUS", "QUANTITY", "UNIT_PRICE", "LINE_LEVEL_ITEM_DISCOUNT", "UNIT_OF_MEASURE", "LINE_AMOUNT", "WEIGHT_OR_CARTONQUANTITY", "DELIVERY_NOTE_DATA" }(i)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_320">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Object">[LinesDict(strKey)]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Object">[element]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign DisplayName="Assign LinesDict(&quot;DELIVERY_NOTE_NUMBER&quot;)" sap2010:WorkflowViewState.IdRef="Assign_304">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Object">[LinesDict("DELIVERY_NOTE_NUMBER")]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(strKey = "DELIVERY_NOTE_DATA" AndAlso CType(LinesDict(strKey), JArray)(0).ToString &lt;&gt; "''",
     CType(LinesDict(strKey), JArray)(0).ToString,LinesDict("DELIVERY_NOTE_NUMBER").ToString)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign DisplayName="Replace DN with Null if ''" sap2010:WorkflowViewState.IdRef="Assign_324">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Object">[LinesDict("DELIVERY_NOTE_NUMBER")]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[If(strKey = "DELIVERY_NOTE_DATA" AndAlso LinesDict("DELIVERY_NOTE_NUMBER").ToString = "''", "",LinesDict("DELIVERY_NOTE_NUMBER").ToString)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign DisplayName="Replace PO with null if ''" sap2010:WorkflowViewState.IdRef="Assign_325">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Object">[LinesDict("PO_Number")]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[If(strKey = "PO_Number" AndAlso LinesDict("PO_Number").ToString = "''", "",LinesDict("PO_Number").ToString)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_310">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[strDeliveryNote]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(strKey = "DELIVERY_NOTE_DATA" AndAlso CType(LinesDict(strKey), JArray)(0).ToString &lt;&gt; "''",
    If(String.IsNullOrWhiteSpace(strDeliveryNote),
        CType(LinesDict(strKey), JArray)(0).ToString,
        strDeliveryNote + "," + CType(LinesDict(strKey), JArray)(0).ToString),
    strDeliveryNote)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <iad1:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_38">
                                    <iad1:CommentOut.Activities>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_319">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[strDeliveryNote]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(strKey = "DELIVERY_NOTE_DATA", 
	If(String.IsNullOrWhiteSpace(strDeliveryNote),
		CType(LinesDict(strKey), JArray)(0).ToString,
	strDeliveryNote + ","+ CType(LinesDict(strKey), JArray)(0).ToString), strDeliveryNote)]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                    </iad1:CommentOut.Activities>
                                  </iad1:CommentOut>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_305">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Int32">[i]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Int32">[i+1]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </Sequence>
                              </ActivityAction>
                            </ForEach>
                            <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_29" MethodName="Add">
                              <InvokeMethod.TargetObject>
                                <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListOcrLineValues]</InArgument>
                              </InvokeMethod.TargetObject>
                              <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</InArgument>
                            </InvokeMethod>
                          </Sequence>
                        </ActivityAction>
                      </ForEach>
                    </Sequence>
                    <Sequence x:Key="PO_Total_Amount" DisplayName="PO_Total_Amount Sequence" sap2010:WorkflowViewState.IdRef="Sequence_122">
                      <Sequence.Variables>
                        <Variable x:TypeArguments="x:Int32" Name="intCounter" />
                      </Sequence.Variables>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_306">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Int32">[intCounter]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:Int32">[(ListOcrLineValues.count-1)-(detailValuesJArray.count-1)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ForEach x:TypeArguments="x:Int32" DisplayName="Add PO Number &amp; PO Total Amount" sap2010:WorkflowViewState.IdRef="ForEach`1_35" Values="[Enumerable.Range(intCounter, detailValuesJArray.count)]">
                        <ActivityAction x:TypeArguments="x:Int32">
                          <ActivityAction.Argument>
                            <DelegateInArgument x:TypeArguments="x:Int32" Name="DictIndex" />
                          </ActivityAction.Argument>
                          <Sequence sap2010:Annotation.AnnotationText="This block will Add PO Number &amp; PO Total Amount to all lines within that PO Data" DisplayName="Add PO Number &amp; PO Total Amount Sequence" sap2010:WorkflowViewState.IdRef="Sequence_121">
                            <Assign DisplayName="Assign PO_Number" sap2010:WorkflowViewState.IdRef="Assign_307">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Object">[ListOcrLineValues(DictIndex)("PO_Number")]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Object">[strPONumber]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign DisplayName="Assign PO_Total_Amount" sap2010:WorkflowViewState.IdRef="Assign_308">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Object">[ListOcrLineValues(DictIndex)("PO_Total_Amount")]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Object">[Line.Value.Tostring]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign DisplayName="Increment intLineDictCounter" sap2010:WorkflowViewState.IdRef="Assign_309">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Int32">[intLineDictCounter]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Int32">[intLineDictCounter+1]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </Sequence>
                        </ActivityAction>
                      </ForEach>
                    </Sequence>
                  </Switch>
                </ActivityAction>
              </ForEach>
            </ActivityAction>
          </ForEach>
          <iad1:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_31">
            <iad1:CommentOut.Activities>
              <ForEach x:TypeArguments="x:Object" DisplayName="Debug Purpose" sap2010:WorkflowViewState.IdRef="ForEach`1_30" Values="[ListOcrLineValues]">
                <ActivityAction x:TypeArguments="x:Object">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="x:Object" Name="item" />
                  </ActivityAction.Argument>
                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_93">
                    <Sequence.Variables>
                      <Variable x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="tempdict" />
                    </Sequence.Variables>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_233">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[tempdict]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[CType(item, Dictionary(Of String, Object))]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <ForEach x:TypeArguments="scg:KeyValuePair(x:String, x:Object)" DisplayName="ForEach&lt;KeyValuePair&lt;String,Object&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_32" Values="[tempdict]">
                      <ActivityAction x:TypeArguments="scg:KeyValuePair(x:String, x:Object)">
                        <ActivityAction.Argument>
                          <DelegateInArgument x:TypeArguments="scg:KeyValuePair(x:String, x:Object)" Name="item" />
                        </ActivityAction.Argument>
                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="[&quot;Key is - &quot;+item.key+&quot;      -      Value is - &quot;+item.value.ToString]" Source="C:\Users\<USER>\Downloads\DictCountLog.txt" />
                      </ActivityAction>
                    </ForEach>
                  </Sequence>
                </ActivityAction>
              </ForEach>
            </iad1:CommentOut.Activities>
          </iad1:CommentOut>
        </Sequence>
        <Assign DisplayName="Assign DELIVERY_NOTE_DATA to Headers with strDeliveryNote" sap2010:WorkflowViewState.IdRef="Assign_311">
          <Assign.To>
            <OutArgument x:TypeArguments="x:Object">[DictOcrValues("DELIVERY_NOTE_DATA")]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:Object">[strDeliveryNote]</InArgument>
          </Assign.Value>
        </Assign>
      </Sequence>
      <Sequence x:Key="False" DisplayName="GenAI Falied Sequence" sap2010:WorkflowViewState.IdRef="Sequence_123">
        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Gen AI Failure Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_4" Line="[&quot;Exception occurred in Get AI XAML. genAIRespCode is: &quot;+genAIRespCode.ToString]" Source="[logFile]" />
        <Throw Exception="[New Exception(&quot;Exception occurred in Get AI XAML.&quot;)]" sap2010:WorkflowViewState.IdRef="Throw_1" />
      </Sequence>
    </Switch>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="File_Read_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_113" sap:VirtualizedContainerService.HintSize="242,64" />
      <sap2010:ViewStateData Id="Assign_114" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="OpenBrowser_2" sap:VirtualizedContainerService.HintSize="592.666666666667,58" />
      <sap2010:ViewStateData Id="NavigateTo_2" sap:VirtualizedContainerService.HintSize="592.666666666667,58" />
      <sap2010:ViewStateData Id="MaximizeWindow_2" sap:VirtualizedContainerService.HintSize="592.666666666667,22" />
      <sap2010:ViewStateData Id="SendKeys_1" sap:VirtualizedContainerService.HintSize="592.666666666667,22" />
      <sap2010:ViewStateData Id="SendKeys_3" sap:VirtualizedContainerService.HintSize="592.666666666667,22" />
      <sap2010:ViewStateData Id="SendKeys_2" sap:VirtualizedContainerService.HintSize="592.666666666667,22" />
      <sap2010:ViewStateData Id="ScreenShot_2" sap:VirtualizedContainerService.HintSize="592.666666666667,22" />
      <sap2010:ViewStateData Id="FileToBase64_2" sap:VirtualizedContainerService.HintSize="592.666666666667,22" />
      <sap2010:ViewStateData Id="CloseBrowser_2" sap:VirtualizedContainerService.HintSize="592.666666666667,22" />
      <sap2010:ViewStateData Id="Sequence_29" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_93" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Path_Validate_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="File_Read_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_26" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_322" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_323" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_98" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_99" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_100" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_101" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Template_Apply_7" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_124" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_41" sap:VirtualizedContainerService.HintSize="242,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_326" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_327" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_328" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_329" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_330" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Template_Apply_8" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_125" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="DeserializeJSON_6" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="MessageBox_41" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_314" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_46" sap:VirtualizedContainerService.HintSize="600,1369">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_115" sap:VirtualizedContainerService.HintSize="558,60" />
      <sap2010:ViewStateData Id="Assign_116" sap:VirtualizedContainerService.HintSize="558,60" />
      <sap2010:ViewStateData Id="Assign_117" sap:VirtualizedContainerService.HintSize="558,60" />
      <sap2010:ViewStateData Id="DeserializeJSON_7" sap:VirtualizedContainerService.HintSize="558,22" />
      <sap2010:ViewStateData Id="MessageBox_40" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_40" sap:VirtualizedContainerService.HintSize="558,118">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_139" sap:VirtualizedContainerService.HintSize="294,60" />
      <sap2010:ViewStateData Id="Assign_119" sap:VirtualizedContainerService.HintSize="294,60" />
      <sap2010:ViewStateData Id="Assign_151" sap:VirtualizedContainerService.HintSize="242,64" />
      <sap2010:ViewStateData Id="Assign_153" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="MessageBox_15" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_21" sap:VirtualizedContainerService.HintSize="242,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_152" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_68" sap:VirtualizedContainerService.HintSize="264,486">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_13" sap:VirtualizedContainerService.HintSize="294,634">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_313" sap:VirtualizedContainerService.HintSize="294,60" />
      <sap2010:ViewStateData Id="Assign_315" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="CommentOut_36" sap:VirtualizedContainerService.HintSize="294,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_172" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_198" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_199" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_200" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_79" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_261" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_262" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_105" sap:VirtualizedContainerService.HintSize="264,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_263" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_264" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_106" sap:VirtualizedContainerService.HintSize="264,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_265" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_266" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_107" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_21" sap:VirtualizedContainerService.HintSize="476.666666666667,192">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_267" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_268" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_108" sap:VirtualizedContainerService.HintSize="264,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_22" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_178" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="MessageBox_34" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="MessageBox_18" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="CommentOut_30" sap:VirtualizedContainerService.HintSize="713,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_173" sap:VirtualizedContainerService.HintSize="713,60" />
      <sap2010:ViewStateData Id="Assign_284" sap:VirtualizedContainerService.HintSize="242,64" />
      <sap2010:ViewStateData Id="Assign_285" sap:VirtualizedContainerService.HintSize="242,64" />
      <sap2010:ViewStateData Id="Assign_286" sap:VirtualizedContainerService.HintSize="242,64" />
      <sap2010:ViewStateData Id="Sequence_114" sap:VirtualizedContainerService.HintSize="264,375">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_287" sap:VirtualizedContainerService.HintSize="242,64" />
      <sap2010:ViewStateData Id="Assign_288" sap:VirtualizedContainerService.HintSize="242,64" />
      <sap2010:ViewStateData Id="Assign_289" sap:VirtualizedContainerService.HintSize="242,64" />
      <sap2010:ViewStateData Id="Sequence_115" sap:VirtualizedContainerService.HintSize="264,270">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_290" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_291" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_292" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_116" sap:VirtualizedContainerService.HintSize="264,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_25" sap:VirtualizedContainerService.HintSize="476,1078">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_316" sap:VirtualizedContainerService.HintSize="256,64" />
      <sap2010:ViewStateData Id="Assign_317" sap:VirtualizedContainerService.HintSize="256,64" />
      <sap2010:ViewStateData Id="Assign_318" sap:VirtualizedContainerService.HintSize="256,64" />
      <sap2010:ViewStateData Id="Assign_293" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_294" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_295" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="CommentOut_37" sap:VirtualizedContainerService.HintSize="256,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_117" sap:VirtualizedContainerService.HintSize="278,766">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_26" sap:VirtualizedContainerService.HintSize="683,164">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_17" sap:VirtualizedContainerService.HintSize="713,312">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_59" sap:VirtualizedContainerService.HintSize="294,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_57" sap:VirtualizedContainerService.HintSize="558,1247">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_129" sap:VirtualizedContainerService.HintSize="536,60" />
      <sap2010:ViewStateData Id="Assign_224" sap:VirtualizedContainerService.HintSize="536,60" />
      <sap2010:ViewStateData Id="Assign_297" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_298" sap:VirtualizedContainerService.HintSize="347,60" />
      <sap2010:ViewStateData Id="MessageBox_36" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_33" sap:VirtualizedContainerService.HintSize="347,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="MessageBox_37" sap:VirtualizedContainerService.HintSize="200,63.3333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsAnnotationDocked">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_34" sap:VirtualizedContainerService.HintSize="294.666666666667,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_299" sap:VirtualizedContainerService.HintSize="294.666666666667,62" />
      <sap2010:ViewStateData Id="MessageBox_38" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_35" sap:VirtualizedContainerService.HintSize="294.666666666667,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_300" sap:VirtualizedContainerService.HintSize="294.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_301" sap:VirtualizedContainerService.HintSize="294.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_302" sap:VirtualizedContainerService.HintSize="294.666666666667,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_303" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_320" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_304" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_324" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_325" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_310" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_319" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="CommentOut_38" sap:VirtualizedContainerService.HintSize="242,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_305" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_118" sap:VirtualizedContainerService.HintSize="264,900.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_33" sap:VirtualizedContainerService.HintSize="294.666666666667,1053.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeMethod_29" sap:VirtualizedContainerService.HintSize="294,128" />
      <sap2010:ViewStateData Id="Sequence_119" sap:VirtualizedContainerService.HintSize="316.666666666667,1952.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_34" sap:VirtualizedContainerService.HintSize="347,2101">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_120" sap:VirtualizedContainerService.HintSize="369,2452">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
            <x:Boolean x:Key="IsAnnotationDocked">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_306" sap:VirtualizedContainerService.HintSize="295,60" />
      <sap2010:ViewStateData Id="Assign_307" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_308" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_309" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_121" sap:VirtualizedContainerService.HintSize="264,437">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsAnnotationDocked">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_35" sap:VirtualizedContainerService.HintSize="295,585">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_122" sap:VirtualizedContainerService.HintSize="317,811">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_27" sap:VirtualizedContainerService.HintSize="476,182">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_23" sap:VirtualizedContainerService.HintSize="506,330">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_18" sap:VirtualizedContainerService.HintSize="536,478">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_233" sap:VirtualizedContainerService.HintSize="284.666666666667,62" />
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="ForEach`1_32" sap:VirtualizedContainerService.HintSize="284.666666666667,212.666666666667" />
      <sap2010:ViewStateData Id="Sequence_93" sap:VirtualizedContainerService.HintSize="306.666666666667,438.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_30" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_31" sap:VirtualizedContainerService.HintSize="536,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_58" sap:VirtualizedContainerService.HintSize="558,900">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_311" sap:VirtualizedContainerService.HintSize="558,60" />
      <sap2010:ViewStateData Id="Sequence_55" sap:VirtualizedContainerService.HintSize="580,2931">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_4" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Throw_1" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_123" sap:VirtualizedContainerService.HintSize="222,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_28" sap:VirtualizedContainerService.HintSize="600,3137" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="622,4670">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="662,4750" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>