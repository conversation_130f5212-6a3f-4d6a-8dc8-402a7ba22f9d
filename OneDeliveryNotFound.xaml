﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow" this:Workflow.division="595"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:this="clr-namespace:RehostedWorkflowDesigner"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="vendorId" Type="InArgument(x:String)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="logfile" Type="InArgument(x:String)" />
    <x:Property Name="M3TotalTableRows" Type="OutArgument(scg:List(s:String[]))" />
    <x:Property Name="Status" Type="OutArgument(x:String)" />
    <x:Property Name="commentStatus" Type="OutArgument(x:String)" />
    <x:Property Name="division" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="withPO" Type="OutArgument(x:String)" />
    <x:Property Name="withVendor" Type="OutArgument(x:String)" />
    <x:Property Name="DictOcrValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.Workflow</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.Workflow</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_1">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="req2" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="linesOcrWorkflowOutput" />
      <Variable x:TypeArguments="x:Int32" Name="linesOcrWorkflowStatus" />
      <Variable x:TypeArguments="x:Boolean" Name="dnExists" />
      <Variable x:TypeArguments="scg:List(s:String[])" Name="M3TotalTableRows1" />
    </Sequence.Variables>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_18">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String" xml:space="preserve">["F2PNLI,F2REPN,F2SCOC,F2RPQA,F2TRDT,F2ITNO,F2CONO,F2CUCD,F2DIVI,F2SUNO,F2IVQA,F2PUNO,F2SUDO,F2RCAC,F2RPQT,F2SCOP,F2SERA,F2IVQT,F2ICAC  from FGRECL where F2PUNO = " + DictOcrValues("PO_NUMBER").Tostring+ " and F2DIVI = "+division + " and F2IMST != 9 "]</InArgument>
      </Assign.Value>
    </Assign>
    <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;miscValues&quot;,miscValues},{&quot;req2&quot;,req2}}]" ContinueOnError="True" DisplayName="Lines extraction Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_3" OutputArguments="[linesOcrWorkflowOutput]" ResponseCode="[linesOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\LinesExtractionWithPO.xaml&quot;]" />
    <If Condition="[linesOcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_6">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_14">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_23">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(s:String[])">[new list(OF string())]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_24">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows1]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(s:String[])">[CType(linesOcrWorkflowOutput("M3TotalTableRows"), List(Of String()))]</InArgument>
            </Assign.Value>
          </Assign>
          <If Condition="[M3TotalTableRows1.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_8">
            <If.Then>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_26">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[withPO]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">True</InArgument>
                </Assign.Value>
              </Assign>
            </If.Then>
            <If.Else>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_15">
                <If Condition="[miscValues(&quot;autoAllocateOpenLines&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_9">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_16">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_25">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String" xml:space="preserve">["F2PNLI,F2REPN,F2SCOC,F2RPQA,F2TRDT,F2ITNO,F2CONO,F2CUCD,F2DIVI,F2SUNO,F2IVQA,F2PUNO,F2SUDO,F2RCAC,F2RPQT,F2SCOP,F2SERA,F2IVQT,F2ICAC  from FGRECL where F2SUNO = " + vendorId + " and F2DIVI = "+division + " and F2IMST != 9 "]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_27">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[withPO]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">False</InArgument>
                        </Assign.Value>
                      </Assign>
                      <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;miscValues&quot;,miscValues},{&quot;req2&quot;,req2}}]" ContinueOnError="True" DisplayName="Lines extraction Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_4" OutputArguments="[linesOcrWorkflowOutput]" ResponseCode="[linesOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\LinesExtractionWithPO.xaml&quot;]" />
                    </Sequence>
                  </If.Then>
                </If>
              </Sequence>
            </If.Else>
          </If>
        </Sequence>
      </If.Then>
    </If>
    <If Condition="[linesOcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_4">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_8">
          <Sequence.Variables>
            <Variable x:TypeArguments="x:Boolean" Name="DNinM3exists" />
          </Sequence.Variables>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_13">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Boolean">[dnExists]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Boolean">True</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_14">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(s:String[])">[new list(OF string())]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_2">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows1]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(s:String[])">[CType(linesOcrWorkflowOutput("M3TotalTableRows"), List(Of String()))]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_3">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Boolean">[DNinM3exists]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Boolean">[CType(linesOcrWorkflowOutput("DNinM3exists"), Boolean)]</InArgument>
            </Assign.Value>
          </Assign>
          <If Condition="[M3TotalTableRows1.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_3">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_4">
                <If Condition="[dnExists]" sap2010:WorkflowViewState.IdRef="If_1">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_3">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_28">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[withVendor]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">True</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="[&quot;Receipt lines for the delivery note number &quot;  + &quot; is extracted.&quot;]" Source="[logfile]" />
                      <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_1" Values="[M3TotalTableRows1]">
                        <ActivityAction x:TypeArguments="s:String[]">
                          <ActivityAction.Argument>
                            <DelegateInArgument x:TypeArguments="s:String[]" Name="item" />
                          </ActivityAction.Argument>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_2">
                            <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_1" MethodName="Add">
                              <InvokeMethod.TargetObject>
                                <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</InArgument>
                              </InvokeMethod.TargetObject>
                              <InArgument x:TypeArguments="s:String[]">[item]</InArgument>
                            </InvokeMethod>
                          </Sequence>
                        </ActivityAction>
                      </ForEach>
                    </Sequence>
                  </If.Then>
                </If>
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_10">
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_15">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">["Lines extracted."]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_16">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">SUCCESS</InArgument>
                    </Assign.Value>
                  </Assign>
                </Sequence>
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_7">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_30">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[withVendor]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">False</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[DNinM3exists]" sap2010:WorkflowViewState.IdRef="If_2">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_5">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_5">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">["Lines already invoiced for the Delivery note: " + DictOcrValues("DELIVERY_NOTE_DATA").Tostring]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_6">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_2" Line="[&quot;Lines already invoiced for delivery note: &quot; + DictOcrValues(&quot;DELIVERY_NOTE_DATA&quot;).Tostring]" Source="[logfile]" />
                    </Sequence>
                  </If.Then>
                  <If.Else>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_6">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_7">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">["No receipts available for the given delivery Note: " +DictOcrValues("DELIVERY_NOTE_DATA").Tostring]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_8">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">PONOTRECEIVED</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_3" Line="[CommentStatus]" Source="[logfile]" />
                    </Sequence>
                  </If.Else>
                </If>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_9">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:Boolean">[dnExists]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                  </Assign.Value>
                </Assign>
              </Sequence>
            </If.Else>
          </If>
        </Sequence>
      </If.Then>
      <If.Else>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_9">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_29">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[withVendor]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">False</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_10">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[CType(linesOcrWorkflowOutput("commentStatus"), String)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_11">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[CType(linesOcrWorkflowOutput("Status"), String)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_12">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Boolean">[dnExists]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Boolean">False</InArgument>
            </Assign.Value>
          </Assign>
        </Sequence>
      </If.Else>
    </If>
    <sads:DebugSymbol.Symbol>d19DOlxVc2Vyc1xza2F2aXJheWFuaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYyXE9uZURlbGl2ZXJ5Tm90Rm91bmQueGFtbHcBbgFyAQJGA9gCDgIBAU4FVQ4DAagBVgVW3QMDAaABVwWTAQoCAXaUAQXWAgoCAQJQMVA3AwGpAVb7AlaVAwMBpgFW0wJW7QIDAaQBViNWyQEDAaMBVqMDVtoDAwGhAVcTVzMCAXdZCZEBFAIBeZQBE5QBMwIBA5YBCbACFAIBGLMCCdQCFAIBBVoLYRQDAZwBYgtpFAMBmAFqC5ABEAIBepoBC6EBFAIBcqIBC6kBFAIBbqoBC7EBFAIBarIBC7kBFAIBZboBC68CEAIBGbQCC7sCFAIBFLwCC8MCFAIBD8QCC8sCFAIBCswCC9MCFAIBBl9CX1kDAZ8BXENcVQMBnQFnQmeIAQMBmwFkQ2RWAwGZAWoZajsCAXtsD3MYAwGUAXYPjgEaAgF9nwE3nwE7AgF1nAE4nAFCAgFzpwFCpwFZAgFxpAFDpAFVAgFvrwFCrwGIAQIBbawBQ6wBVgIBa7cBN7cBbwIBaLQBOLQBRgIBZroBGboBOwIBGrwBD+0BGgIBRvABD60CGgIBHLkCNrkCOwIBF7YCN7YCQwIBFcECNsECbgIBEr4CN74CRgIBEMkCNskCZwIBDcYCN8YCPwIBC9ECN9ECPAIBCc4COM4CQgIBB3E6cT4DAZcBbjtuQwMBlQF3EY0BFgIBfr0BEdoBFgIBUNsBEewBHAIBR/EBEfgBGgIBQvkBEaQCFgIBIaUCEawCGgIBHXcfd3QCAX95FYsBIAMBgQG9AR+9ASsCAVG/ARXYASACAVPcARPjARwCAUzkARPrARwCAUj2ATz2AUECAUXzAT3zAUkCAUP5AR/5AS8CASL7ARWNAiACATOQAhWiAiACASSqAj2qAkICASCnAj6nAkgCAR56F4EBIAMBjgGCAReJASADAYoBigEXigHvAwMBggHAARfHASACAWHIARfIAZICAgFdyQEX1wEhAgFU4QE+4QFSAgFP3gE/3gFOAgFN6QE+6QFFAgFL5gE/5gFHAgFJ/AEXgwIgAgE9hAIXiwIgAgE5jAIXjAKsAgIBNJECF5gCIAIBLpkCF6ACIAIBKqECF6ECygECASV8Q3xJAwGPAYcBQocBRwMBjQGEAUOEAUsDAYsBigGNA4oBpwMDAYgBigHlAooB/wIDAYYBigE1igHbAQMBhQGKAbUDigHsAwMBgwHFAULFAUYCAWTCAUPCAU8CAWLIAaMByAH8AQIBYMgBhALIAY8CAgFeyQGYAckBrQECAVvOARvVASYCAVWBAkKBAqMBAgFA/gFD/gFSAgE+iQJCiQJJAgE8hgJDhgJLAgE6jAKjAYwClgICATeMAp4CjAKpAgIBNZYCQpYCpwECATGTAkOTAlICAS+eAkKeAk8CAS2bAkObAksCASuhAqMBoQK0AQIBKKECvAGhAscBAgEmzwEd1AEsAgFW0QFU0QFmAgFZ0wFI0wFOAgFX</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_18" sap:VirtualizedContainerService.HintSize="1400,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_3" sap:VirtualizedContainerService.HintSize="1400,22" />
      <sap2010:ViewStateData Id="Assign_23" sap:VirtualizedContainerService.HintSize="754,62" />
      <sap2010:ViewStateData Id="Assign_24" sap:VirtualizedContainerService.HintSize="754,62" />
      <sap2010:ViewStateData Id="Assign_26" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_25" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_27" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_16" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_9" sap:VirtualizedContainerService.HintSize="464,504" />
      <sap2010:ViewStateData Id="Sequence_15" sap:VirtualizedContainerService.HintSize="486,628">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_8" sap:VirtualizedContainerService.HintSize="754,782" />
      <sap2010:ViewStateData Id="Sequence_14" sap:VirtualizedContainerService.HintSize="776,1110">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_6" sap:VirtualizedContainerService.HintSize="1400,1264" />
      <sap2010:ViewStateData Id="Assign_13" sap:VirtualizedContainerService.HintSize="1088,62" />
      <sap2010:ViewStateData Id="Assign_14" sap:VirtualizedContainerService.HintSize="1088,62" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="1088,62" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="1088,62" />
      <sap2010:ViewStateData Id="Assign_28" sap:VirtualizedContainerService.HintSize="284.666666666667,62" />
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="284.666666666667,22" />
      <sap2010:ViewStateData Id="InvokeMethod_1" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="239.333333333333,258">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_1" sap:VirtualizedContainerService.HintSize="284.666666666667,410.666666666667" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="306.666666666667,698.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="464,852.666666666667" />
      <sap2010:ViewStateData Id="Assign_15" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_16" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_10" sap:VirtualizedContainerService.HintSize="464,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_4" sap:VirtualizedContainerService.HintSize="486,1304.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_30" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_7" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_6" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="554,504" />
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="576,832">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="1088,1458.66666666667" />
      <sap2010:ViewStateData Id="Sequence_8" sap:VirtualizedContainerService.HintSize="1110,1990.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_29" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_11" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_12" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_9" sap:VirtualizedContainerService.HintSize="264,492">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="1400,2144.66666666667" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="1422,3736.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="1462,3816.66666666667" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>