﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:iae="clr-namespace:Infor.Activities.Email;assembly=Infor.Activities.Email"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="emailAccount" Type="InArgument(x:String)" />
    <x:Property Name="emailFolder" Type="InArgument(x:String)" />
    <x:Property Name="numberOfEmails" Type="InArgument(x:Int32)" />
    <x:Property Name="enableMessageBoxes" Type="InArgument(x:Boolean)" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
    <x:Property Name="invoiceSource" Type="InArgument(x:String)" />
    <x:Property Name="configurationFolder" Type="InArgument(x:String)" />
    <x:Property Name="logFolderName" Type="InArgument(x:String)" />
    <x:Property Name="directoriesNames" Type="InArgument(x:String)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="colemanAPI" Type="InArgument(x:String)" />
    <x:Property Name="userIdentifier" Type="InArgument(x:String)" />
    <x:Property Name="distributionType" Type="InArgument(x:String)" />
    <x:Property Name="poFilterValues" Type="InArgument(x:String)" />
    <x:Property Name="poFilterCondition" Type="InArgument(x:String)" />
    <x:Property Name="datalakeAPILogicalId" Type="InArgument(x:String)" />
    <x:Property Name="chargeCode" Type="InArgument(x:String)" />
    <x:Property Name="discountCode" Type="InArgument(x:String)" />
    <x:Property Name="authUser" Type="InArgument(x:String)" />
    <x:Property Name="imsAPIUrl" Type="InArgument(x:String)" />
    <x:Property Name="poDiscountsHandlingConfig" Type="InArgument(x:Boolean)" />
    <x:Property Name="vatCodeConfig" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="processExpenseInvoice" Type="InArgument(x:Boolean)" />
    <x:Property Name="approvalRequired" Type="InArgument(x:Boolean)" />
    <x:Property Name="matchVendorItemCode" Type="InArgument(x:Boolean)" />
    <x:Property Name="approvalWorkflow" Type="InArgument(x:String)" />
    <x:Property Name="extractNumericFromPO" Type="InArgument(x:Boolean)" />
    <x:Property Name="extractFromWidgetDatalake" Type="InArgument(x:Boolean)" />
    <x:Property Name="checkAmountBussinessRule" Type="InArgument(x:String)" />
    <x:Property Name="InProgressFolder" Type="InArgument(x:String)" />
    <x:Property Name="MasterDownloads" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>System</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.IO</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Microsoft.VisualBasic</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.Collections.Immutable</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <TryCatch DisplayName="TryCatch - Process Outlook Emails" sap2010:WorkflowViewState.IdRef="TryCatch_5">
    <TryCatch.Variables>
      <Variable x:TypeArguments="scg:List(x:String)" Name="inProgressFiles" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="processPoResp" />
      <Variable x:TypeArguments="x:Int32" Name="processIdCount" />
      <Variable x:TypeArguments="x:Int32" Name="statsCount" />
      <Variable x:TypeArguments="scg:List(scg:List(x:String))" Default="[New List(Of List(Of String))]" Name="mainListNew" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="emailsubjects" />
      <Variable x:TypeArguments="x:String" Name="strMoveFile" />
      <Variable x:TypeArguments="scg:List(scg:List(x:String))" Name="approvalLists" />
      <Variable x:TypeArguments="scg:List(scg:List(x:String))" Default="[New List(Of List(Of String))]" Name="finalizeStoreStat" />
    </TryCatch.Variables>
    <TryCatch.Try>
      <Sequence DisplayName="Process Outlook Emails Sequence" sap2010:WorkflowViewState.IdRef="Sequence_72">
        <Assign sap2010:WorkflowViewState.IdRef="Assign_82">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[InProgressFolder]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[configurationFolder+ "\InProgress"]</InArgument>
          </Assign.Value>
        </Assign>
        <Sequence DisplayName="Process Outlook Emails" sap2010:WorkflowViewState.IdRef="Sequence_67">
          <Sequence.Variables>
            <Variable x:TypeArguments="scg:List(iae:Mail)" Default="[new List( Of Mail)]" Name="ListAllemails" />
            <Variable x:TypeArguments="x:Int32" Name="ln" />
            <Variable x:TypeArguments="scg:List(iae:Mail)" Name="SM1" />
            <Variable x:TypeArguments="scg:List(iae:Mail)" Name="SM2" />
            <Variable x:TypeArguments="scg:List(iae:Mail)" Name="SM3" />
            <Variable x:TypeArguments="scg:List(iae:Mail)" Name="SM4" />
            <Variable x:TypeArguments="scg:List(iae:Mail)" Name="SM5" />
            <Variable x:TypeArguments="scg:List(x:String)" Default="[new list ( of string)]" Name="tsubjects2" />
            <Variable x:TypeArguments="scg:List(x:String)" Default="[new list ( of string)]" Name="tsubjects3" />
            <Variable x:TypeArguments="scg:List(x:String)" Default="[new list ( of string)]" Name="tsubjects4" />
            <Variable x:TypeArguments="scg:List(x:String)" Default="[new list ( of string)]" Name="tsubjects5" />
            <Variable x:TypeArguments="scg:List(x:String)" Default="[new list ( of string)]" Name="tsubjects1" />
            <Variable x:TypeArguments="x:String" Name="p1_time" />
            <Variable x:TypeArguments="x:String" Name="p2_time" />
            <Variable x:TypeArguments="x:String" Name="p3_time" />
            <Variable x:TypeArguments="x:String" Name="p4_time" />
            <Variable x:TypeArguments="x:String" Name="p5_time" />
            <Variable x:TypeArguments="x:Int32" Default="5" Name="numberOfParts" />
            <Variable x:TypeArguments="x:Int32" Name="emailResponseCode" />
          </Sequence.Variables>
          <Switch x:TypeArguments="x:String" DisplayName="Read Outlook Emails as Per Source" Expression="[invoiceSource]" sap2010:WorkflowViewState.IdRef="Switch`1_17">
            <iae:GetOutlookEmails Filter="{x:Null}" x:Key="OutlookClientEmail" Account="[emailAccount.Trim]" ContinueOnError="False" DisplayName="Get Outlook Emails" Emails="[ListAllemails]" ErrorCode="[emailResponseCode]" Folder="[emailFolder.Trim]" sap2010:WorkflowViewState.IdRef="GetOutlookEmails_9" MarkAsRead="True" OnlyUnread="True" TopEmails="[numberOfEmails]" />
            <iae:GetOutlookEmailsGraph EmailCount="{x:Null}" Filter="{x:Null}" x:Key="OutlookGraphEmail" AccountName="[emailAccount.Trim]" ContinueOnError="True" DisplayName="Get Outlook Emails Graph" Emails="[ListAllemails]" ErrorCode="[emailResponseCode]" FolderName="[emailFolder.Trim]" sap2010:WorkflowViewState.IdRef="GetOutlookEmailsGraph_1" IncludeAttachments="False" MarkAsRead="True" OnlyUnread="True" TopEmails="[numberOfEmails]" />
          </Switch>
          <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_17">
            <iad:CommentOut.Activities>
              <DoWhile DisplayName="Read all Outlook unread emails" sap2010:WorkflowViewState.IdRef="DoWhile_3">
                <DoWhile.Variables>
                  <Variable x:TypeArguments="iae:Mail" Name="mail" />
                  <Variable x:TypeArguments="x:Int32" Name="TotalNoOfEmails" />
                </DoWhile.Variables>
                <DoWhile.Condition>[emails.count&gt;0]</DoWhile.Condition>
                <Sequence DisplayName="Outlook Sequence" sap2010:WorkflowViewState.IdRef="Sequence_60">
                  <Switch x:TypeArguments="x:String" DisplayName="Read Outlook Emails as Per Source" Expression="[invoiceSource]" sap2010:WorkflowViewState.IdRef="Switch`1_4">
                    <iae:GetOutlookEmails Filter="{x:Null}" x:Key="OutlookClientEmail" Account="[emailAccount]" ContinueOnError="False" DisplayName="Get Outlook Emails" Emails="[emails]" ErrorCode="[emailResponseCode]" Folder="[emailFolder]" sap2010:WorkflowViewState.IdRef="GetOutlookEmails_5" MarkAsRead="True" OnlyUnread="True" TopEmails="[numberOfEmails]" />
                    <x:Null x:Key="OutlookGraphEmail" />
                  </Switch>
                  <Switch x:TypeArguments="x:Boolean" DisplayName="If Emails count is &gt;0" Expression="[emails.count&gt;0]" sap2010:WorkflowViewState.IdRef="Switch`1_5">
                    <ForEach x:TypeArguments="iae:Mail" x:Key="True" DisplayName="Build ListAllEmails" sap2010:WorkflowViewState.IdRef="ForEach`1_5" Values="[emails]">
                      <ActivityAction x:TypeArguments="iae:Mail">
                        <ActivityAction.Argument>
                          <DelegateInArgument x:TypeArguments="iae:Mail" Name="mail" />
                        </ActivityAction.Argument>
                        <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_4" MethodName="Add">
                          <InvokeMethod.TargetObject>
                            <InArgument x:TypeArguments="scg:List(iae:Mail)">[ListAllemails]</InArgument>
                          </InvokeMethod.TargetObject>
                          <InArgument x:TypeArguments="iae:Mail">[mail]</InArgument>
                        </InvokeMethod>
                      </ActivityAction>
                    </ForEach>
                  </Switch>
                </Sequence>
              </DoWhile>
            </iad:CommentOut.Activities>
          </iad:CommentOut>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_42" Line="[Environment.newLine+&quot;Outlook emails extraction is done and the total mail count is - &quot;+ListAllemails.count.tostring+Environment.newLine+&quot;Emails Response Status code : &quot;+emailResponseCode.tostring]" Source="[logfile]" />
          <Sequence DisplayName="Create Lists for 5 sub ListMails processes" sap2010:WorkflowViewState.IdRef="Sequence_61">
            <Assign sap2010:WorkflowViewState.IdRef="Assign_66">
              <Assign.To>
                <OutArgument x:TypeArguments="x:Int32">[ln]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:Int32">[cint(ListAllemails.Count/ numberOfParts)]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_67">
              <Assign.To>
                <OutArgument x:TypeArguments="scg:List(iae:Mail)">[SM1]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="scg:List(iae:Mail)">[ListAllemails.Skip(1 - 1).Take(ln).ToList()]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_68">
              <Assign.To>
                <OutArgument x:TypeArguments="scg:List(iae:Mail)">[SM2]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="scg:List(iae:Mail)">[ListAllemails.Skip(1*ln).Take(ln).ToList()]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_69">
              <Assign.To>
                <OutArgument x:TypeArguments="scg:List(iae:Mail)">[SM3]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="scg:List(iae:Mail)">[ListAllemails.Skip(2*ln).Take(ln).ToList()]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_70">
              <Assign.To>
                <OutArgument x:TypeArguments="scg:List(iae:Mail)">[SM4]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="scg:List(iae:Mail)">[ListAllemails.Skip(3*ln).Take(ln).ToList()]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_71">
              <Assign.To>
                <OutArgument x:TypeArguments="scg:List(iae:Mail)">[SM5]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="scg:List(iae:Mail)">[ListAllemails.Skip(4*ln).ToList()]</InArgument>
              </Assign.Value>
            </Assign>
          </Sequence>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_56" Line="[&quot;Count of Mails in 5 sub mail lists is &quot;+Environment.NewLine+&quot;SM1 - &quot;+SM1.Count.tostring+Environment.NewLine+&quot;SM2 - &quot;+SM2.Count.tostring+Environment.NewLine+&quot;SM3 - &quot;+SM3.Count.tostring+Environment.NewLine+&quot;SM4 - &quot;+SM4.Count.tostring+Environment.NewLine+&quot;SM5 - &quot;+SM5.Count.tostring+Environment.NewLine+&quot;Total Emails: &quot;  +ListAllemails.count.tostring]" Source="[logfile]" />
          <Switch x:TypeArguments="x:Boolean" DisplayName="Initiate Parallel Processing if Mails Available" Expression="[ListAllemails.count&gt;0]" sap2010:WorkflowViewState.IdRef="Switch`1_6">
            <Parallel x:Key="True" DisplayName="Parallel Sequence Updated" sap2010:WorkflowViewState.IdRef="Parallel_6">
              <If Condition="[SM1.Count &gt; 0]" DisplayName="Parallel Sequence--1" sap2010:WorkflowViewState.IdRef="If_35">
                <If.Then>
                  <Sequence DisplayName="Sequence 1" sap2010:WorkflowViewState.IdRef="Sequence_121">
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_202">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[p1_time]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">["start time: "+Environment.newLine+System.DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;submails&quot;,SM1},{&quot;configurationFolder&quot;,configurationFolder},{&quot;logfile&quot;,logfile},{&quot;tenantID&quot;,tenantID},{&quot;colemanAPI&quot;,colemanAPI},{&quot;userIdentifier&quot;,userIdentifier},{&quot;distributionType&quot;,distributionType},{&quot;projectPath&quot;,projectPath},{&quot;poFilterValues&quot;,poFilterValues},{&quot;poFilterCondition&quot;,poFilterCondition},{&quot;datalakeAPILogicalId&quot;,datalakeAPILogicalId},{&quot;chargeCode&quot;,chargeCode},{&quot;discountCode&quot;,discountCode},{&quot;authUser&quot;,authUser},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;extractNumericFromPO&quot;,extractNumericFromPO},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;poDiscountsHandlingConfig&quot;,poDiscountsHandlingConfig},{&quot;matchVendorItemCode&quot;,matchVendorItemCode},{&quot;extractFromWidgetDatalake&quot;,extractFromWidgetDatalake},{&quot;approvalRequired&quot;,approvalRequired},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;processExpenseInvoice&quot;,processExpenseInvoice},{&quot;miscValues&quot;,miscValues},{&quot;invoiceSource&quot;,invoiceSource},{&quot;enableMessageBoxes&quot;,enableMessageBoxes},{&quot;seqnumber&quot;,&quot;1&quot;},{&quot;DownloadTo&quot;,&quot;DownloadedFiles_&quot;},{&quot;FileRenameFormat&quot;,&quot;InforOS_A&quot;},{&quot;MasterDownloads&quot;,MasterDownloads},{&quot;emailAccount&quot;,emailAccount}}]" ContinueOnError="False" DisplayName="Invoke preprocess 1" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_52" OutputArguments="[emailsubjects]" WorkflowFile="[projectPath+&quot;\Outlook_preprocess.xaml&quot;]" />
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_203">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:List(scg:List(x:String))">[mainListNew]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[CType(emailsubjects("mainStoreStats"), List(Of List(Of String)))]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <ForEach x:TypeArguments="scg:List(x:String)" DisplayName="ForEach&lt;List&lt;String&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_10" Values="[mainListNew]">
                      <ActivityAction x:TypeArguments="scg:List(x:String)">
                        <ActivityAction.Argument>
                          <DelegateInArgument x:TypeArguments="scg:List(x:String)" Name="item" />
                        </ActivityAction.Argument>
                        <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_9" MethodName="Add">
                          <InvokeMethod.TargetObject>
                            <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[finalizeStoreStat]</InArgument>
                          </InvokeMethod.TargetObject>
                          <InArgument x:TypeArguments="scg:List(x:String)">[item]</InArgument>
                        </InvokeMethod>
                      </ActivityAction>
                    </ForEach>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_204">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:List(x:String)">[tsubjects1]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:List(x:String)">[TryCast(emailsubjects("emailsubjects"),List(Of String))]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </Sequence>
                </If.Then>
                <If.Else>
                  <Sequence DisplayName="No Mails in SM1 Mail list" sap2010:WorkflowViewState.IdRef="Sequence_122">
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_205">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[p1_time]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">["No Data to process in Parallel Sequence 1."]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_206">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:List(x:String)">[tsubjects1]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </Sequence>
                </If.Else>
              </If>
              <If Condition="[SM2.Count &gt; 0]" DisplayName="Parallel Sequence--2" sap2010:WorkflowViewState.IdRef="If_36">
                <If.Then>
                  <Sequence DisplayName="Sequence 2" sap2010:WorkflowViewState.IdRef="Sequence_123">
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_207">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[p2_time]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">["start time: "+Environment.newLine+System.DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;submails&quot;,SM2},{&quot;configurationFolder&quot;,configurationFolder},{&quot;logfile&quot;,logfile},{&quot;tenantID&quot;,tenantID},{&quot;colemanAPI&quot;,colemanAPI},{&quot;userIdentifier&quot;,userIdentifier},{&quot;distributionType&quot;,distributionType},{&quot;projectPath&quot;,projectPath},{&quot;poFilterValues&quot;,poFilterValues},{&quot;poFilterCondition&quot;,poFilterCondition},{&quot;datalakeAPILogicalId&quot;,datalakeAPILogicalId},{&quot;chargeCode&quot;,chargeCode},{&quot;discountCode&quot;,discountCode},{&quot;authUser&quot;,authUser},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;extractNumericFromPO&quot;,extractNumericFromPO},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;poDiscountsHandlingConfig&quot;,poDiscountsHandlingConfig},{&quot;matchVendorItemCode&quot;,matchVendorItemCode},{&quot;extractFromWidgetDatalake&quot;,extractFromWidgetDatalake},{&quot;approvalRequired&quot;,approvalRequired},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;processExpenseInvoice&quot;,processExpenseInvoice},{&quot;miscValues&quot;,miscValues},{&quot;invoiceSource&quot;,invoiceSource},{&quot;enableMessageBoxes&quot;,enableMessageBoxes},{&quot;seqnumber&quot;,&quot;2&quot;},{&quot;DownloadTo&quot;,&quot;DownloadedFiles_&quot;},{&quot;FileRenameFormat&quot;,&quot;InforOS_B&quot;},{&quot;MasterDownloads&quot;,MasterDownloads},{&quot;emailAccount&quot;,emailAccount}}]" ContinueOnError="False" DisplayName="Invoke Workflow 2" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_53" OutputArguments="[emailsubjects]" WorkflowFile="[projectPath+&quot;\Outlook_preprocess.xaml&quot;]" />
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_208">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:List(scg:List(x:String))">[mainListNew]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[CType(emailsubjects("mainStoreStats"), List(Of List(Of String)))]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <ForEach x:TypeArguments="scg:List(x:String)" DisplayName="ForEach&lt;List&lt;String&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_11" Values="[mainListNew]">
                      <ActivityAction x:TypeArguments="scg:List(x:String)">
                        <ActivityAction.Argument>
                          <DelegateInArgument x:TypeArguments="scg:List(x:String)" Name="item" />
                        </ActivityAction.Argument>
                        <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_10" MethodName="Add">
                          <InvokeMethod.TargetObject>
                            <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[finalizeStoreStat]</InArgument>
                          </InvokeMethod.TargetObject>
                          <InArgument x:TypeArguments="scg:List(x:String)">[item]</InArgument>
                        </InvokeMethod>
                      </ActivityAction>
                    </ForEach>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_209">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:List(x:String)">[tsubjects2]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:List(x:String)">[TryCast(emailsubjects("emailsubjects"),List(Of String))]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </Sequence>
                </If.Then>
                <If.Else>
                  <Sequence DisplayName="No Mails in SM2 Mail list" sap2010:WorkflowViewState.IdRef="Sequence_124">
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_210">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[p2_time]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">No Data to process in Parallel Sequence 2.</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_211">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:List(x:String)">[tsubjects2]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </Sequence>
                </If.Else>
              </If>
              <If Condition="[SM3.Count &gt; 0]" DisplayName="Parallel Sequence--3" sap2010:WorkflowViewState.IdRef="If_37">
                <If.Then>
                  <Sequence DisplayName="Sequence 3" sap2010:WorkflowViewState.IdRef="Sequence_125">
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_212">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[p3_time]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">["start time: "+Environment.newLine+System.DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;submails&quot;,SM3},{&quot;configurationFolder&quot;,configurationFolder},{&quot;logfile&quot;,logfile},{&quot;tenantID&quot;,tenantID},{&quot;colemanAPI&quot;,colemanAPI},{&quot;userIdentifier&quot;,userIdentifier},{&quot;distributionType&quot;,distributionType},{&quot;projectPath&quot;,projectPath},{&quot;poFilterValues&quot;,poFilterValues},{&quot;poFilterCondition&quot;,poFilterCondition},{&quot;datalakeAPILogicalId&quot;,datalakeAPILogicalId},{&quot;chargeCode&quot;,chargeCode},{&quot;discountCode&quot;,discountCode},{&quot;authUser&quot;,authUser},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;extractNumericFromPO&quot;,extractNumericFromPO},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;poDiscountsHandlingConfig&quot;,poDiscountsHandlingConfig},{&quot;matchVendorItemCode&quot;,matchVendorItemCode},{&quot;extractFromWidgetDatalake&quot;,extractFromWidgetDatalake},{&quot;approvalRequired&quot;,approvalRequired},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;processExpenseInvoice&quot;,processExpenseInvoice},{&quot;miscValues&quot;,miscValues},{&quot;invoiceSource&quot;,invoiceSource},{&quot;enableMessageBoxes&quot;,enableMessageBoxes},{&quot;seqnumber&quot;,&quot;3&quot;},{&quot;DownloadTo&quot;,&quot;DownloadedFiles_&quot;},{&quot;FileRenameFormat&quot;,&quot;InforOS_C&quot;},{&quot;MasterDownloads&quot;,MasterDownloads},{&quot;emailAccount&quot;,emailAccount}}]" ContinueOnError="False" DisplayName="Invoke Workflow 3" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_54" OutputArguments="[emailsubjects]" WorkflowFile="[projectPath+&quot;\Outlook_preprocess.xaml&quot;]" />
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_213">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:List(scg:List(x:String))">[mainListNew]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[CType(emailsubjects("mainStoreStats"), List(Of List(Of String)))]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <ForEach x:TypeArguments="scg:List(x:String)" DisplayName="ForEach&lt;List&lt;String&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_12" Values="[mainListNew]">
                      <ActivityAction x:TypeArguments="scg:List(x:String)">
                        <ActivityAction.Argument>
                          <DelegateInArgument x:TypeArguments="scg:List(x:String)" Name="item" />
                        </ActivityAction.Argument>
                        <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_11" MethodName="Add">
                          <InvokeMethod.TargetObject>
                            <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[finalizeStoreStat]</InArgument>
                          </InvokeMethod.TargetObject>
                          <InArgument x:TypeArguments="scg:List(x:String)">[item]</InArgument>
                        </InvokeMethod>
                      </ActivityAction>
                    </ForEach>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_214">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:List(x:String)">[tsubjects3]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:List(x:String)">[TryCast(emailsubjects("emailsubjects"),List(Of String))]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </Sequence>
                </If.Then>
                <If.Else>
                  <Sequence DisplayName="No Mails in SM3 Mail list" sap2010:WorkflowViewState.IdRef="Sequence_126">
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_215">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[p3_time]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">No Data to process in Parallel Sequence 3.</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_216">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:List(x:String)">[tsubjects3]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </Sequence>
                </If.Else>
              </If>
              <If Condition="[SM4.Count &gt; 0]" DisplayName="Parallel Sequence--4" sap2010:WorkflowViewState.IdRef="If_38">
                <If.Then>
                  <Sequence DisplayName="Sequence 4" sap2010:WorkflowViewState.IdRef="Sequence_127">
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_217">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[p4_time]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">["start time: "+Environment.newLine+System.DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;submails&quot;,SM4},{&quot;configurationFolder&quot;,configurationFolder},{&quot;logfile&quot;,logfile},{&quot;tenantID&quot;,tenantID},{&quot;colemanAPI&quot;,colemanAPI},{&quot;userIdentifier&quot;,userIdentifier},{&quot;distributionType&quot;,distributionType},{&quot;projectPath&quot;,projectPath},{&quot;poFilterValues&quot;,poFilterValues},{&quot;poFilterCondition&quot;,poFilterCondition},{&quot;datalakeAPILogicalId&quot;,datalakeAPILogicalId},{&quot;chargeCode&quot;,chargeCode},{&quot;discountCode&quot;,discountCode},{&quot;authUser&quot;,authUser},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;extractNumericFromPO&quot;,extractNumericFromPO},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;poDiscountsHandlingConfig&quot;,poDiscountsHandlingConfig},{&quot;matchVendorItemCode&quot;,matchVendorItemCode},{&quot;extractFromWidgetDatalake&quot;,extractFromWidgetDatalake},{&quot;approvalRequired&quot;,approvalRequired},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;processExpenseInvoice&quot;,processExpenseInvoice},{&quot;miscValues&quot;,miscValues},{&quot;invoiceSource&quot;,invoiceSource},{&quot;enableMessageBoxes&quot;,enableMessageBoxes},{&quot;seqnumber&quot;,&quot;4&quot;},{&quot;DownloadTo&quot;,&quot;DownloadedFiles_&quot;},{&quot;FileRenameFormat&quot;,&quot;InforOS_D&quot;},{&quot;MasterDownloads&quot;,MasterDownloads},{&quot;emailAccount&quot;,emailAccount}}]" ContinueOnError="False" DisplayName="Invoke Workflow 4" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_55" OutputArguments="[emailsubjects]" WorkflowFile="[projectPath+&quot;\Outlook_preprocess.xaml&quot;]" />
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_218">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:List(scg:List(x:String))">[mainListNew]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[CType(emailsubjects("mainStoreStats"), List(Of List(Of String)))]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <ForEach x:TypeArguments="scg:List(x:String)" DisplayName="ForEach&lt;List&lt;String&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_13" Values="[mainListNew]">
                      <ActivityAction x:TypeArguments="scg:List(x:String)">
                        <ActivityAction.Argument>
                          <DelegateInArgument x:TypeArguments="scg:List(x:String)" Name="item" />
                        </ActivityAction.Argument>
                        <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_12" MethodName="Add">
                          <InvokeMethod.TargetObject>
                            <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[finalizeStoreStat]</InArgument>
                          </InvokeMethod.TargetObject>
                          <InArgument x:TypeArguments="scg:List(x:String)">[item]</InArgument>
                        </InvokeMethod>
                      </ActivityAction>
                    </ForEach>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_219">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:List(x:String)">[tsubjects4]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:List(x:String)">[TryCast(emailsubjects("emailsubjects"),List(Of String))]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </Sequence>
                </If.Then>
                <If.Else>
                  <Sequence DisplayName="No Mails in SM4 Mail list" sap2010:WorkflowViewState.IdRef="Sequence_128">
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_220">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[p4_time]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">No Data to process in Parallel Sequence 4.</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_221">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:List(x:String)">[tsubjects4]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </Sequence>
                </If.Else>
              </If>
              <If Condition="[SM5.Count &gt; 0]" DisplayName="Parallel Sequence--5" sap2010:WorkflowViewState.IdRef="If_39">
                <If.Then>
                  <Sequence DisplayName="Sequence 5" sap2010:WorkflowViewState.IdRef="Sequence_129">
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_222">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[p5_time]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">["start time: "+Environment.newLine+System.DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;submails&quot;,SM5},{&quot;configurationFolder&quot;,configurationFolder},{&quot;logfile&quot;,logfile},{&quot;tenantID&quot;,tenantID},{&quot;colemanAPI&quot;,colemanAPI},{&quot;userIdentifier&quot;,userIdentifier},{&quot;distributionType&quot;,distributionType},{&quot;projectPath&quot;,projectPath},{&quot;poFilterValues&quot;,poFilterValues},{&quot;poFilterCondition&quot;,poFilterCondition},{&quot;datalakeAPILogicalId&quot;,datalakeAPILogicalId},{&quot;chargeCode&quot;,chargeCode},{&quot;discountCode&quot;,discountCode},{&quot;authUser&quot;,authUser},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;extractNumericFromPO&quot;,extractNumericFromPO},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;poDiscountsHandlingConfig&quot;,poDiscountsHandlingConfig},{&quot;matchVendorItemCode&quot;,matchVendorItemCode},{&quot;extractFromWidgetDatalake&quot;,extractFromWidgetDatalake},{&quot;approvalRequired&quot;,approvalRequired},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;processExpenseInvoice&quot;,processExpenseInvoice},{&quot;miscValues&quot;,miscValues},{&quot;invoiceSource&quot;,invoiceSource},{&quot;enableMessageBoxes&quot;,enableMessageBoxes},{&quot;seqnumber&quot;,&quot;5&quot;},{&quot;DownloadTo&quot;,&quot;DownloadedFiles_&quot;},{&quot;FileRenameFormat&quot;,&quot;InforOS_E&quot;},{&quot;MasterDownloads&quot;,MasterDownloads},{&quot;emailAccount&quot;,emailAccount}}]" ContinueOnError="False" DisplayName="Invoke Workflow 5" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_56" OutputArguments="[emailsubjects]" WorkflowFile="[projectPath+&quot;\Outlook_preprocess.xaml&quot;]" />
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_223">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:List(scg:List(x:String))">[mainListNew]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[CType(emailsubjects("mainStoreStats"), List(Of List(Of String)))]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <ForEach x:TypeArguments="scg:List(x:String)" DisplayName="ForEach&lt;List&lt;String&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_14" Values="[mainListNew]">
                      <ActivityAction x:TypeArguments="scg:List(x:String)">
                        <ActivityAction.Argument>
                          <DelegateInArgument x:TypeArguments="scg:List(x:String)" Name="item" />
                        </ActivityAction.Argument>
                        <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_13" MethodName="Add">
                          <InvokeMethod.TargetObject>
                            <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[finalizeStoreStat]</InArgument>
                          </InvokeMethod.TargetObject>
                          <InArgument x:TypeArguments="scg:List(x:String)">[item]</InArgument>
                        </InvokeMethod>
                      </ActivityAction>
                    </ForEach>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_224">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:List(x:String)">[tsubjects5]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:List(x:String)">[TryCast(emailsubjects("emailsubjects"),List(Of String))]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </Sequence>
                </If.Then>
                <If.Else>
                  <Sequence DisplayName="No Mails in SM5 Mail list" sap2010:WorkflowViewState.IdRef="Sequence_130">
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_225">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[p5_time]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">No Data to process in Parallel Sequence 5.</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_226">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:List(x:String)">[tsubjects5]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </Sequence>
                </If.Else>
              </If>
            </Parallel>
            <Throw x:Key="False" DisplayName="No Mail Data Throw" Exception="[New SystemException(&quot;No unread emails in the mailbox to process.TrimStart&quot;)]" sap2010:WorkflowViewState.IdRef="Throw_2" />
          </Switch>
          <Sequence DisplayName="Assign Parallel Log Data" sap2010:WorkflowViewState.IdRef="Sequence_90">
            <Sequence.Variables>
              <Variable x:TypeArguments="x:String" Name="log1" />
              <Variable x:TypeArguments="x:String" Name="log2" />
              <Variable x:TypeArguments="x:String" Name="log3" />
              <Variable x:TypeArguments="x:String" Name="log4" />
              <Variable x:TypeArguments="x:String" Name="log5" />
            </Sequence.Variables>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_105">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[log1]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(Not String.IsNullOrWhiteSpace(p1_time) OrElse tsubjects1.Count &gt; 0,
   "process-1" &amp; Environment.NewLine &amp;
   If(String.IsNullOrWhiteSpace(p1_time), "", p1_time) &amp; Environment.NewLine &amp;
   If(tsubjects1.Count = 0, "", String.Join(", ", tsubjects1)) &amp; Environment.NewLine,
   "")]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_106">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[log2]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(Not String.IsNullOrWhiteSpace(p2_time) OrElse tsubjects2.Count &gt; 0,
   "process-2" &amp; Environment.NewLine &amp;
   If(String.IsNullOrWhiteSpace(p2_time), "", p2_time) &amp; Environment.NewLine &amp;
   If(tsubjects2.Count = 0, "", String.Join(", ", tsubjects2)) &amp; Environment.NewLine,
   "")]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_107">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[log3]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(Not String.IsNullOrWhiteSpace(p3_time) OrElse tsubjects3.Count &gt; 0,
   "process-3" &amp; Environment.NewLine &amp;
   If(String.IsNullOrWhiteSpace(p3_time), "", p3_time) &amp; Environment.NewLine &amp;
   If(tsubjects3.Count = 0, "", String.Join(", ", tsubjects3)) &amp; Environment.NewLine,
   "")]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_108">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[log4]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(Not String.IsNullOrWhiteSpace(p4_time) OrElse tsubjects4.Count &gt; 0,
   "process-4" &amp; Environment.NewLine &amp;
   If(String.IsNullOrWhiteSpace(p4_time), "", p4_time) &amp; Environment.NewLine &amp;
   If(tsubjects4.Count = 0, "", String.Join(", ", tsubjects4)) &amp; Environment.NewLine,
   "")]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_109">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[log5]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(Not String.IsNullOrWhiteSpace(p5_time) OrElse tsubjects5.Count &gt; 0,
   "process-5" &amp; Environment.NewLine &amp;
   If(String.IsNullOrWhiteSpace(p5_time), "", p5_time) &amp; Environment.NewLine &amp;
   If(tsubjects5.Count = 0, "", String.Join(", ", tsubjects5)) &amp; Environment.NewLine,
   "")]</InArgument>
              </Assign.Value>
            </Assign>
            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Append Line post Parallel Process" sap2010:WorkflowViewState.IdRef="Append_Line_57" Line="[&quot;Log data From all 5 parallel sequences is as below.&quot; &amp; Environment.NewLine &amp;&#xA;log1 &amp; log2 &amp; log3 &amp; log4 &amp; log5]" Source="[logfile]" />
            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Process Outlook Emails Completed  Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_59" Line="Downloading attachments from Outlook Completed." Source="[logfile]" />
          </Sequence>
        </Sequence>
        <Sequence DisplayName="Split Sequence" sap2010:WorkflowViewState.IdRef="Sequence_89">
          <Sequence.Variables>
            <Variable x:TypeArguments="scg:List(x:String)" Name="MasterDownloadedFiles" />
          </Sequence.Variables>
          <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[MasterDownloads]" DisplayName="Get Files in OutlookDownloads" FileType="All" Files="[MasterDownloadedFiles]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_3" IncludeSubDir="True" />
          <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From{{&quot;logFile&quot;,logFile},{&quot;configurationFolder&quot;,configurationFolder},{&quot;tenantID&quot;,tenantID},{&quot;projectPath&quot;,projectPath},{&quot;miscValues&quot;,miscValues},{&quot;promptPath&quot;,ConfigurationFolder + &quot;\Classification.txt&quot;},{&quot;promptPath2&quot;,ConfigurationFolder + &quot;\GenAISplit2New.txt&quot;},{&quot;InProgressFolder&quot;,InProgressFolder},{&quot;strClassificationExtension&quot;,ConfigurationFolder + &quot;\GenAISplit1_Ext.txt&quot;},{&quot;MasterDownloads&quot;,MasterDownloads},{&quot;enableMessageBoxes&quot;,enableMessageBoxes},{&quot;strSplitExtension&quot;,ConfigurationFolder + &quot;\GenAISplit2_Ext.txt&quot;}}]" ContinueOnError="False" DisplayName="Invoke Classification_Split Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_48" WorkflowFile="[projectPath+&quot;\Classification_Split.xaml&quot;]" />
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification Split Sequence Completed  Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_61" Line="[&quot;Classification (Split - )+&quot;+miscValues(&quot;SplittingDoc&quot;).ToString+&quot; Sequence Completed.&quot;]" Source="[logfile]" />
        </Sequence>
        <Sequence DisplayName="OCR Sequence" sap2010:WorkflowViewState.IdRef="Sequence_69">
          <Sequence.Variables>
            <Variable x:TypeArguments="scg:List(x:String)" Name="listInProgressFiles" />
            <Variable x:TypeArguments="x:String" Name="strSpecificText" />
            <Variable x:TypeArguments="scg:List(x:String)" Default="[New List(of string)]" Name="processIdLst" />
            <Variable x:TypeArguments="scg:List(x:String)" Name="ListReProcess" />
            <Variable x:TypeArguments="scg:List(x:String)" Name="ListInProgressNames" />
            <Variable x:TypeArguments="x:String" Name="InvoiceFileName" />
            <Variable x:TypeArguments="x:Int32" Name="RenameCounter" />
            <Variable x:TypeArguments="x:Boolean" Name="blnReprocessExists" />
            <Variable x:TypeArguments="x:Int32" Name="processPoRespCode" />
          </Sequence.Variables>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="OCR Sequence Started  Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_62" Line="Get OCR Sequence Started." Source="[logfile]" />
          <Assign sap2010:WorkflowViewState.IdRef="Assign_198">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(scg:List(x:String))">[approvalLists]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[New List(Of List(Of String))]</InArgument>
            </Assign.Value>
          </Assign>
          <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_1" IsValid="[blnReprocessExists]" Path="[configurationFolder+ &quot;\ReProcess&quot;]" />
          <Switch x:TypeArguments="x:Boolean" DisplayName="Check if Reprocess folder exists" Expression="[blnReprocessExists]" sap2010:WorkflowViewState.IdRef="Switch`1_16">
            <Sequence x:Key="True" DisplayName="Move Reprocess Files Sequence" sap2010:WorkflowViewState.IdRef="Sequence_114">
              <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[inProgressFolder]" DisplayName="Get Files from Inprogress" FileType="PDF" Files="[listInProgressFiles]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_1" IncludeSubDir="True" />
              <Assign sap2010:WorkflowViewState.IdRef="Assign_196">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:Int32">[RenameCounter]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:Int32">1</InArgument>
                </Assign.Value>
              </Assign>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_194">
                <Assign.To>
                  <OutArgument x:TypeArguments="scg:List(x:String)">[ListInProgressNames]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="scg:List(x:String)">[new list( of string)]</InArgument>
                </Assign.Value>
              </Assign>
              <ForEach x:TypeArguments="x:String" DisplayName="Get FileNames from Inprogress Folder" sap2010:WorkflowViewState.IdRef="ForEach`1_9" Values="[listInProgressFiles]">
                <ActivityAction x:TypeArguments="x:String">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="x:String" Name="itemA" />
                  </ActivityAction.Argument>
                  <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_7" MethodName="Add">
                    <InvokeMethod.TargetObject>
                      <InArgument x:TypeArguments="scg:List(x:String)">[ListInProgressNames]</InArgument>
                    </InvokeMethod.TargetObject>
                    <InArgument x:TypeArguments="x:String">[Path.GetFileName(itemA).Tostring]</InArgument>
                  </InvokeMethod>
                </ActivityAction>
              </ForEach>
              <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="True" DirPath="[configurationFolder+ &quot;\ReProcess&quot;]" DisplayName="Get Reprocess Files in Directory" FileType="All" Files="[ListReProcess]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_10" IncludeSubDir="True" />
              <If Condition="[ListReProcess.Count&gt;0]" DisplayName="Check Reprocess files count and Move to Inprogress" sap2010:WorkflowViewState.IdRef="If_28">
                <If.Then>
                  <ForEach x:TypeArguments="x:String" DisplayName="Move all ReProcess Files to Inprogress" sap2010:WorkflowViewState.IdRef="ForEach`1_8" Values="[ListReProcess]">
                    <ActivityAction x:TypeArguments="x:String">
                      <ActivityAction.Argument>
                        <DelegateInArgument x:TypeArguments="x:String" Name="ReprocessFile" />
                      </ActivityAction.Argument>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_113">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_188">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[Path.GetFilename(ReprocessFile)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Switch x:TypeArguments="x:Boolean" DisplayName="Move Files to InProgress Folder" Expression="[ListInProgressNames.Contains(Path.GetFileName(ReprocessFile))]" sap2010:WorkflowViewState.IdRef="Switch`1_15">
                          <Sequence x:Key="True" DisplayName="File Name Already Present in Master Downloads" sap2010:WorkflowViewState.IdRef="Sequence_111">
                            <If Condition="[System.Text.RegularExpressions.Regex.IsMatch(InvoiceFileName, &quot;[:#&amp;@`'*/\?&lt;&gt;|&quot;&quot;]&quot;)]" DisplayName="Check if the FileName Contains the special Characters" sap2010:WorkflowViewState.IdRef="If_29">
                              <If.Then>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_189">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(InvoiceFileName, "[:#&amp;@`'*/\?&lt;&gt;|""]", "_")]</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </If.Then>
                            </If>
                            <ias:File_Move ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Move File" sap2010:WorkflowViewState.IdRef="File_Move_3" OutputFile="[strMoveFile]" OverwriteFile="False" Source="[ReprocessFile]" Target="[configurationFolder+ &quot;\InProgress&quot;]" targetName="[InvoiceFileName.Replace(&quot;.pdf&quot;,&quot;_&quot;+RenameCounter.Tostring+&quot;.pdf&quot;)]" />
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_195">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Int32">[RenameCounter]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Int32">[RenameCounter+1]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </Sequence>
                          <Sequence x:Key="False" DisplayName="File Name not Already Present in Master Downloads Folder" sap2010:WorkflowViewState.IdRef="Sequence_112">
                            <If Condition="[System.Text.RegularExpressions.Regex.IsMatch(InvoiceFileName, &quot;[:#&amp;@`'*/\?&lt;&gt;|&quot;&quot;]&quot;)]" DisplayName="Check if the FileName Contains the special Characters" sap2010:WorkflowViewState.IdRef="If_30">
                              <If.Then>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_191">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(InvoiceFileName, "[:#&amp;@~`'*/\?&lt;&gt;|""]", "_")]</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </If.Then>
                            </If>
                            <ias:File_Move ErrorCode="{x:Null}" targetName="{x:Null}" ContinueOnError="True" DisplayName="Move File" sap2010:WorkflowViewState.IdRef="File_Move_5" OutputFile="[strMoveFile]" OverwriteFile="False" Source="[ReprocessFile]" Target="[configurationFolder+ &quot;\InProgress&quot;]" />
                          </Sequence>
                        </Switch>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_193">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Int32">[RenameCounter]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Int32">[RenameCounter+1]</InArgument>
                          </Assign.Value>
                        </Assign>
                      </Sequence>
                    </ActivityAction>
                  </ForEach>
                </If.Then>
              </If>
            </Sequence>
            <ias:Append_Line ErrorCode="{x:Null}" x:Key="False" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_67" Line="ReProcess folder doesn't exist." Source="[logFile]" />
          </Switch>
          <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[inProgressFolder]" DisplayName="Get Files from Inprogress" FileType="PDF" Files="[listInProgressFiles]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_12" IncludeSubDir="True" />
          <ForEach x:TypeArguments="x:String" DisplayName="Process Each file in Inprogress with GetOCRValues XAML" sap2010:WorkflowViewState.IdRef="ForEach`1_6" Values="[listInProgressFiles]">
            <ActivityAction x:TypeArguments="x:String">
              <ActivityAction.Argument>
                <DelegateInArgument x:TypeArguments="x:String" Name="item2" />
              </ActivityAction.Argument>
              <TryCatch DisplayName="TryCatch Each file in Inprogress" sap2010:WorkflowViewState.IdRef="TryCatch_7">
                <TryCatch.Try>
                  <Sequence DisplayName="Each file in Inprogress Sequence" sap2010:WorkflowViewState.IdRef="Sequence_71">
                    <Sequence.Variables>
                      <Variable x:TypeArguments="x:String" Name="CurrentOCRFile" />
                      <Variable x:TypeArguments="x:String" Name="OCRTextOut" />
                      <Variable x:TypeArguments="njl:JToken" Name="ReqOCRvalues" />
                      <Variable x:TypeArguments="x:String" Name="targetPages" />
                      <Variable x:TypeArguments="scg:List(x:String)" Name="approvalList" />
                    </Sequence.Variables>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_199">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:List(x:String)">[approvalList]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_52" Line="[&quot;File Name : &quot;+item2.Substring(item2.LastIndexOf(&quot;\&quot;c)+1,(item2.Length()-item2.LastIndexOf(&quot;\&quot;c))-1)]" Source="[logFile]" />
                    <Switch x:TypeArguments="x:Boolean" DisplayName="Check if SplittingDoc is True to send Specific OCR Text" Expression="[miscValues(&quot;SplittingDoc&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="Switch`1_14">
                      <Sequence x:Key="True" DisplayName="Get OCR Text Rquired for Prompt" sap2010:WorkflowViewState.IdRef="Sequence_92">
                        <Sequence.Variables>
                          <Variable x:TypeArguments="x:Int32" Name="indexTilde" />
                          <Variable x:TypeArguments="x:Int32" Name="indexLastDashBeforeTilde" />
                          <Variable x:TypeArguments="x:String" Name="SubFileName" />
                          <Variable x:TypeArguments="scg:List(x:String)" Name="ListOCRData" />
                          <Variable x:TypeArguments="s:String[]" Name="ArrayTargetPages" />
                        </Sequence.Variables>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_110">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Int32">[indexTilde]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Int32">[Path.GetfileName(item2).IndexOf("~"c)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_111">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Int32">[indexLastDashBeforeTilde]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Int32">[Path.GetfilenaMe(item2).LastIndexOf("{"c, indexTilde)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_112">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[SubFileName]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[Path.GetfilenaMe(item2).Substring(0, indexLastDashBeforeTilde)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_113">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[SubFileName]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[SubFileName+".txt"]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_175">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[SubFileName]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[If(SubFileName.EndsWith("-.txt"), SubFileName.Replace("-.txt", ".txt"), SubFileName)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[configurationFolder+&quot;\OutlookDownloads\OCRData&quot;]" DisplayName="Get OCR Files in OCR Data" FileType="All" Files="[ListOCRData]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_4" IncludeSubDir="True" />
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_114">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[CurrentOCRFile]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[ListOCRData.FirstOrDefault(Function(p) Path.GetFileName(p).Trim().ToLower() = SubFileName.Trim().ToLower())]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Read Text File" sap2010:WorkflowViewState.IdRef="File_Read_1" Source="[CurrentOCRFile]" Text="[OCRTextOut]" />
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_116">
                          <Assign.To>
                            <OutArgument x:TypeArguments="njl:JToken">[ReqOCRvalues]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(OCRTextOut)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_117">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[targetPages]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[Path.GetfileName(item2).Split("~"c)(2)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_118">
                          <Assign.To>
                            <OutArgument x:TypeArguments="s:String[]">[ArrayTargetPages]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="s:String[]">[targetPages.Split(","c).Select(Function(x) x.Trim()).ToArray()]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_119">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[strSpecificText]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String" xml:space="preserve">[JsonConvert.SerializeObject(
    ReqOCRvalues("data").
        Where(Function(p) ArrayTargetPages.Contains(p("PageNo").ToString())).
        Select(Function(p) New With {
            Key .OCR_text = p("OCR_text").ToString(),
            Key .PageNo = p("PageNo").ToString()
        })
)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_13">
                          <iad:CommentOut.Activities>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_201">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[strSpecificText]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String" xml:space="preserve">[String.Join(Environment.NewLine,
  ReqOCRvalues("data").
    Where(Function(p) ArrayTargetPages.Contains(p("PageNo").ToString())).
    Select(Function(p) p("OCR_text").ToString()))]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </iad:CommentOut.Activities>
                        </iad:CommentOut>
                      </Sequence>
                      <Sequence x:Key="False" DisplayName="Get OCR Text Rquired for Prompt" sap2010:WorkflowViewState.IdRef="Sequence_108">
                        <Sequence.Variables>
                          <Variable x:TypeArguments="x:Int32" Name="indexTilde" />
                          <Variable x:TypeArguments="x:Int32" Name="indexLastDashBeforeTilde" />
                          <Variable x:TypeArguments="x:String" Name="SubFileName" />
                          <Variable x:TypeArguments="scg:List(x:String)" Name="ListOCRData" />
                          <Variable x:TypeArguments="s:String[]" Name="ArrayTargetPages" />
                        </Sequence.Variables>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_167">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[SubFileName]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[Path.GetfileName(item2).Split("{"c)(0)+".txt"]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[configurationFolder+&quot;\OutlookDownloads\OCRData&quot;]" DisplayName="Get OCR Files in OCR Data" FileType="All" Files="[ListOCRData]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_9" IncludeSubDir="True" />
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_169">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[CurrentOCRFile]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[ListOCRData.FirstOrDefault(Function(p) Path.GetFileName(p).Trim().ToLower() = SubFileName.Trim().ToLower())]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Read Text File" sap2010:WorkflowViewState.IdRef="File_Read_7" Source="[CurrentOCRFile]" Text="[OCRTextOut]" />
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_170">
                          <Assign.To>
                            <OutArgument x:TypeArguments="njl:JToken">[ReqOCRvalues]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(OCRTextOut)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_173">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[strSpecificText]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[ReqOCRvalues.ToString()]</InArgument>
                          </Assign.Value>
                        </Assign>
                      </Sequence>
                    </Switch>
                    <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;documentPath&quot;,item2},{&quot;logFile&quot;,logFile},{&quot;configurationFolder&quot;,configurationFolder},{&quot;manualEntry&quot;,false},{&quot;emailSubject&quot;,&quot;NA&quot;},{&quot;emailReceivedTime&quot;,&quot;NA&quot;},{&quot;tenantID&quot;,tenantID},{&quot;colemanAPI&quot;,colemanAPI},{&quot;userIdentifier&quot;,userIdentifier},{&quot;distributionType&quot;,distributionType},{&quot;projectPath&quot;,projectPath},{&quot;enableMessageBoxes&quot;,enableMessageBoxes},{&quot;poFilterValues&quot;,poFilterValues},{&quot;poFilterCondition&quot;,poFilterCondition},{&quot;datalakeAPILogicalId&quot;,datalakeAPILogicalId},{&quot;chargeCode&quot;,chargeCode},{&quot;discountCode&quot;,discountCode},{&quot;authUser&quot;,authUser},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;extractNumericFromPO&quot;,extractNumericFromPO},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;poDiscountsHandlingConfig&quot;,poDiscountsHandlingConfig},{&quot;matchVendorItemCode&quot;,matchVendorItemCode},{&quot;extractFromWidgetDatalake&quot;,extractFromWidgetDatalake},{&quot;approvalRequired&quot;,approvalRequired},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;miscValues&quot;,miscValues},{&quot;processExpenseInvoice&quot;,processExpenseInvoice},{&quot;strSpecificText&quot;,strSpecificText},{&quot;finalizeStoreStat&quot;,finalizeStoreStat}}]" ContinueOnError="False" DisplayName="GetOCRValues.xaml Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_39" OutputArguments="[processPoResp]" ResponseCode="[processPoRespCode]" WorkflowFile="[projectPath+&quot;\GetOCRValuesNew.xaml&quot;]" />
                    <If Condition="[miscValues(&quot;AutomateApproval&quot;).ToString.ToLower = &quot;true&quot;]" DisplayName="Check If AutomateApproval is True" sap2010:WorkflowViewState.IdRef="If_31">
                      <If.Then>
                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_115">
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_200">
                            <Assign.To>
                              <OutArgument x:TypeArguments="scg:List(x:String)">[approvalList]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="scg:List(x:String)">[CType(processPoResp("approvalList"), List(Of String))]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <If Condition="[approvalList.Count&gt;0]" sap2010:WorkflowViewState.IdRef="If_32">
                            <If.Then>
                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_116">
                                <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_8" MethodName="Add">
                                  <InvokeMethod.TargetObject>
                                    <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[approvalLists]</InArgument>
                                  </InvokeMethod.TargetObject>
                                  <InArgument x:TypeArguments="scg:List(x:String)">[approvalList]</InArgument>
                                </InvokeMethod>
                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_64" Line="[&quot;Updated the approval list with the invoice number &quot; + approvalList(1).ToString]" Source="[logfile]" />
                              </Sequence>
                            </If.Then>
                          </If>
                        </Sequence>
                      </If.Then>
                    </If>
                  </Sequence>
                </TryCatch.Try>
                <TryCatch.Catches>
                  <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_6">
                    <ActivityAction x:TypeArguments="s:Exception">
                      <ActivityAction.Argument>
                        <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                      </ActivityAction.Argument>
                      <Sequence DisplayName="For each file in inprogress catch block" sap2010:WorkflowViewState.IdRef="Sequence_110">
                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_53" Line="[&quot;Below Exception occured in Get OCR Values New. xaml at file&quot; +path.getFileName(item2)+Environment.NewLine+exception.GetType().Name]" Source="[logfile]" />
                        <ias:File_Move ErrorCode="{x:Null}" targetName="{x:Null}" ContinueOnError="True" DisplayName="Move File" sap2010:WorkflowViewState.IdRef="File_Move_1" OutputFile="[strMoveFile]" OverwriteFile="False" Source="[item2]" Target="[configurationFolder+ &quot;\ReProcess&quot;]" />
                      </Sequence>
                    </ActivityAction>
                  </Catch>
                </TryCatch.Catches>
              </TryCatch>
            </ActivityAction>
          </ForEach>
          <If Condition="[miscValues(&quot;AutomateApproval&quot;).ToString.ToLower = &quot;true&quot; AND approvalLists.Count&gt;0]" sap2010:WorkflowViewState.IdRef="If_34">
            <If.Then>
              <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;approvalLists&quot;,approvalLists},{&quot;logFile&quot;,logFile},{&quot;tenantID&quot;,tenantID}}]" ContinueOnError="True" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_51" WorkflowFile="[projectPath+&quot;\approval.xaml&quot;]" />
            </If.Then>
          </If>
        </Sequence>
      </Sequence>
    </TryCatch.Try>
    <TryCatch.Catches>
      <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_5">
        <ActivityAction x:TypeArguments="s:Exception">
          <ActivityAction.Argument>
            <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
          </ActivityAction.Argument>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_70" Line="[&quot;Below Error Occurred in Outlook_ProcessNew Xaml.&quot;+environment.newline+exception.message]" Source="[logFile]" />
        </ActivityAction>
      </Catch>
    </TryCatch.Catches>
    <TryCatch.Finally>
      <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_16">
        <iad:CommentOut.Activities>
          <Rethrow sap2010:WorkflowViewState.IdRef="Rethrow_2" />
        </iad:CommentOut.Activities>
      </iad:CommentOut>
    </TryCatch.Finally>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </TryCatch>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_82" sap:VirtualizedContainerService.HintSize="498.666666666667,62" />
      <sap2010:ViewStateData Id="GetOutlookEmails_9" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="GetOutlookEmailsGraph_1" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Switch`1_17" sap:VirtualizedContainerService.HintSize="476.666666666667,274.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="GetOutlookEmails_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Switch`1_4" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeMethod_4" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="ForEach`1_5" sap:VirtualizedContainerService.HintSize="284.666666666667,280.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_5" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_60" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="DoWhile_3" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_17" sap:VirtualizedContainerService.HintSize="476.666666666667,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_42" sap:VirtualizedContainerService.HintSize="476.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_66" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_67" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_68" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_69" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_70" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_71" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_61" sap:VirtualizedContainerService.HintSize="476.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_56" sap:VirtualizedContainerService.HintSize="476.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_202" sap:VirtualizedContainerService.HintSize="287,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_52" sap:VirtualizedContainerService.HintSize="287,22" />
      <sap2010:ViewStateData Id="Assign_203" sap:VirtualizedContainerService.HintSize="287,60" />
      <sap2010:ViewStateData Id="InvokeMethod_9" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="ForEach`1_10" sap:VirtualizedContainerService.HintSize="287,276">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_204" sap:VirtualizedContainerService.HintSize="284.666666666667,62" />
      <sap2010:ViewStateData Id="Sequence_121" sap:VirtualizedContainerService.HintSize="309,764">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_205" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_206" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_122" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_35" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_207" sap:VirtualizedContainerService.HintSize="287,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_53" sap:VirtualizedContainerService.HintSize="287,22" />
      <sap2010:ViewStateData Id="Assign_208" sap:VirtualizedContainerService.HintSize="287,60" />
      <sap2010:ViewStateData Id="InvokeMethod_10" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="ForEach`1_11" sap:VirtualizedContainerService.HintSize="287,276" />
      <sap2010:ViewStateData Id="Assign_209" sap:VirtualizedContainerService.HintSize="284.666666666667,62" />
      <sap2010:ViewStateData Id="Sequence_123" sap:VirtualizedContainerService.HintSize="309,764">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_210" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_211" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_124" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_36" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_212" sap:VirtualizedContainerService.HintSize="287,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_54" sap:VirtualizedContainerService.HintSize="287,22" />
      <sap2010:ViewStateData Id="Assign_213" sap:VirtualizedContainerService.HintSize="287,60" />
      <sap2010:ViewStateData Id="InvokeMethod_11" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="ForEach`1_12" sap:VirtualizedContainerService.HintSize="287,276" />
      <sap2010:ViewStateData Id="Assign_214" sap:VirtualizedContainerService.HintSize="284.666666666667,62" />
      <sap2010:ViewStateData Id="Sequence_125" sap:VirtualizedContainerService.HintSize="309,764">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_215" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_216" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_126" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_37" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_217" sap:VirtualizedContainerService.HintSize="287,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_55" sap:VirtualizedContainerService.HintSize="287,22" />
      <sap2010:ViewStateData Id="Assign_218" sap:VirtualizedContainerService.HintSize="287,60" />
      <sap2010:ViewStateData Id="InvokeMethod_12" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="ForEach`1_13" sap:VirtualizedContainerService.HintSize="287,276" />
      <sap2010:ViewStateData Id="Assign_219" sap:VirtualizedContainerService.HintSize="284.666666666667,62" />
      <sap2010:ViewStateData Id="Sequence_127" sap:VirtualizedContainerService.HintSize="309,764">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_220" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_221" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_128" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_38" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_222" sap:VirtualizedContainerService.HintSize="287,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_56" sap:VirtualizedContainerService.HintSize="287,22" />
      <sap2010:ViewStateData Id="Assign_223" sap:VirtualizedContainerService.HintSize="287,60" />
      <sap2010:ViewStateData Id="InvokeMethod_13" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="ForEach`1_14" sap:VirtualizedContainerService.HintSize="287,276" />
      <sap2010:ViewStateData Id="Assign_224" sap:VirtualizedContainerService.HintSize="284.666666666667,62" />
      <sap2010:ViewStateData Id="Sequence_129" sap:VirtualizedContainerService.HintSize="309,764">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_225" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_226" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_130" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_39" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Parallel_6" sap:VirtualizedContainerService.HintSize="1274,98.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Throw_2" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Switch`1_6" sap:VirtualizedContainerService.HintSize="476.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_105" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_106" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_107" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_108" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_109" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_57" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Append_Line_59" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_90" sap:VirtualizedContainerService.HintSize="476.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_67" sap:VirtualizedContainerService.HintSize="498.666666666667,898.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Directory_GetFiles_3" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_48" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Append_Line_61" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_89" sap:VirtualizedContainerService.HintSize="498.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_62" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_198" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Path_Validate_1" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Directory_GetFiles_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_196" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_194" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_7" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="ForEach`1_9" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Directory_GetFiles_10" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_188" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_189" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_29" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="File_Move_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_195" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_111" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_191" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_30" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="File_Move_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_112" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_15" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_193" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_113" sap:VirtualizedContainerService.HintSize="264,380.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_8" sap:VirtualizedContainerService.HintSize="294.666666666667,533.333333333333" />
      <sap2010:ViewStateData Id="If_28" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_114" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_67" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Switch`1_16" sap:VirtualizedContainerService.HintSize="464,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Directory_GetFiles_12" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_199" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_52" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_110" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_111" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_112" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_113" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_175" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Directory_GetFiles_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_114" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="File_Read_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_116" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_117" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_118" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_119" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_201" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="CommentOut_13" sap:VirtualizedContainerService.HintSize="242,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_92" sap:VirtualizedContainerService.HintSize="264,1321.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_167" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Directory_GetFiles_9" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_169" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="File_Read_7" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_170" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_173" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_108" sap:VirtualizedContainerService.HintSize="264,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_14" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_39" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_200" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_8" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="Append_Line_64" sap:VirtualizedContainerService.HintSize="217.333333333333,22" />
      <sap2010:ViewStateData Id="Sequence_116" sap:VirtualizedContainerService.HintSize="239.333333333333,320">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_32" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_115" sap:VirtualizedContainerService.HintSize="264,278.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_31" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_71" sap:VirtualizedContainerService.HintSize="264,495.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_53" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="File_Move_1" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_110" sap:VirtualizedContainerService.HintSize="222,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Catch`1_6" sap:VirtualizedContainerService.HintSize="404.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_7" sap:VirtualizedContainerService.HintSize="418.666666666667,733.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_6" sap:VirtualizedContainerService.HintSize="464,886">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_51" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_34" sap:VirtualizedContainerService.HintSize="464,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_69" sap:VirtualizedContainerService.HintSize="498.666666666667,1644.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_72" sap:VirtualizedContainerService.HintSize="520.666666666667,2902">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_70" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_5" sap:VirtualizedContainerService.HintSize="512.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Rethrow_2" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_16" sap:VirtualizedContainerService.HintSize="214,118">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_5" sap:VirtualizedContainerService.HintSize="539.333333333333,3140" />
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="579.333333333333,3220" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>