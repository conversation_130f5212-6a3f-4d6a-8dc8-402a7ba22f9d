﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow" this:Workflow.expenseAccountCodes="11200|NA|NA|NA|NA|NA|NA"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:this="clr-namespace:RehostedWorkflowDesigner"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="commentStatus" Type="OutArgument(x:String)" />
    <x:Property Name="division" Type="InOutArgument(x:String)" />
    <x:Property Name="company" Type="OutArgument(x:String)" />
    <x:Property Name="authUser" Type="InArgument(x:String)" />
    <x:Property Name="expenseAccountCodes" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
    <x:Property Name="vendorName" Type="OutArgument(x:String)" />
    <x:Property Name="status" Type="OutArgument(x:String)" />
    <x:Property Name="vendorId" Type="InOutArgument(x:String)" />
    <x:Property Name="approvalRequired" Type="InArgument(x:Boolean)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="checkAmountBussinessRule" Type="InArgument(x:String)" />
    <x:Property Name="approvalWorkflow" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="GLCode" Type="InArgument(x:String)" />
    <x:Property Name="correlationID" Type="OutArgument(x:String)" />
    <x:Property Name="InYear" Type="OutArgument(x:String)" />
    <x:Property Name="AYear" Type="OutArgument(x:String)" />
    <x:Property Name="approvalList" Type="OutArgument(scg:List(x:String))" />
    <x:Property Name="APResp" Type="InArgument(x:String)" />
    <x:Property Name="ListocrLineValues" Type="InArgument(scg:List(scg:Dictionary(x:String, x:Object)))" />
    <x:Property Name="DictOcrValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>System</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System.Globalization</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_1">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:Int32" Name="StatusCode1" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj1" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode0" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj0" />
      <Variable x:TypeArguments="njl:JToken" Name="out0" />
      <Variable x:TypeArguments="x:String" Name="cucd" />
      <Variable x:TypeArguments="x:String" Name="pyme" />
      <Variable x:TypeArguments="x:String" Name="tepy" />
      <Variable x:TypeArguments="x:String" Name="cuam" />
      <Variable x:TypeArguments="x:String" Name="sino" />
      <Variable x:TypeArguments="x:String" Name="ivdate" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode2" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj2" />
      <Variable x:TypeArguments="x:String" Name="amt" />
      <Variable x:TypeArguments="s:String[]" Name="expenseCodeList" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="invoiceExistsOcrWorkflowOutput" />
      <Variable x:TypeArguments="x:Int32" Name="invoiceExistsOcrWorkflowStatus" />
      <Variable x:TypeArguments="njl:JToken" Name="out1" />
      <Variable x:TypeArguments="x:String" Name="countryCode" />
      <Variable x:TypeArguments="x:Boolean" Name="InvoiceAlreadyExists" />
      <Variable x:TypeArguments="x:String" Name="vat" />
      <Variable x:TypeArguments="x:String" Name="deliveryTerms" />
    </Sequence.Variables>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_166">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(x:String)">[approvalList]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(x:String)">[new list(Of string)]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_38">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[vendorId.ToUpper()]</InArgument>
      </Assign.Value>
    </Assign>
    <Sequence DisplayName="Fiscal year Sequence" sap2010:WorkflowViewState.IdRef="Sequence_49">
      <Sequence.Variables>
        <Variable x:TypeArguments="iru:ResponseObject" Name="respForFiscalYear" />
        <Variable x:TypeArguments="x:Int32" Name="respForFiscalYearCode" />
      </Sequence.Variables>
      <Assign DisplayName="Assign ivdate" sap2010:WorkflowViewState.IdRef="Assign_112">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[ivdate]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">[DateTime.ParseExact(DictOcrValues("INVOICE_RECEIPT_DATE").Tostring,"dd/MM/yyyy",Nothing).ToString("yyyyMMdd")]</InArgument>
        </Assign.Value>
      </Assign>
      <Assign sap2010:WorkflowViewState.IdRef="Assign_113">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[InYear]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">[ivdate.SubString(0,4)]</InArgument>
        </Assign.Value>
      </Assign>
      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Get Fiscal year IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_22" Response="[respForFiscalYear]" StatusCode="[respForFiscalYearCode]" Url="[tenantID+&quot;M3/m3api-rest/v2/execute/CRS900MI/GetSysCalDate&quot;]">
        <iai:IONAPIRequestWizard.Headers>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>Accept</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>application/json</x:String>
            </scg:List>
          </scg:List>
        </iai:IONAPIRequestWizard.Headers>
        <iai:IONAPIRequestWizard.QueryParameters>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>YMD8</x:String>
              <x:String>DIVI</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>ivdate</x:String>
              <x:String>division</x:String>
            </scg:List>
          </scg:List>
        </iai:IONAPIRequestWizard.QueryParameters>
      </iai:IONAPIRequestWizard>
      <If Condition="[respForFiscalYearCode = 200]" sap2010:WorkflowViewState.IdRef="If_33">
        <If.Then>
          <If Condition="[respForFiscalYear.ReadAsJson(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_32">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_47">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_114">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[AYear]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[ivdate.SubString(0,4)]</InArgument>
                  </Assign.Value>
                </Assign>
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_48">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_115">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[AYear]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[respForFiscalYear.ReadAsJson("results")(0)("records")(0)("CYP1").ToString.Substring(0,4)]</InArgument>
                  </Assign.Value>
                </Assign>
              </Sequence>
            </If.Else>
          </If>
        </If.Then>
      </If>
    </Sequence>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_45">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[company]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[miscValues("company").ToString]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_65">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[division]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[division]</InArgument>
      </Assign.Value>
    </Assign>
    <If Condition="[DictOcrValues(&quot;SUBTOTAL&quot;).Tostring = &quot;&quot; OR DictOcrValues(&quot;SUBTOTAL&quot;).Tostring= &quot;0&quot;]" sap2010:WorkflowViewState.IdRef="If_57">
      <If.Then>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_165">
          <Assign.To>
            <OutArgument x:TypeArguments="x:Object">[DictOcrValues("SUBTOTAL")]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:Object">[DictOcrValues("TOTAL")]</InArgument>
          </Assign.Value>
        </Assign>
      </If.Then>
    </If>
    <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;SupInvoiceNo&quot;,DictOcrValues(&quot;INVOICE_RECEIPT_ID&quot;).Tostring},{&quot;division&quot;,division},{&quot;AYear&quot;,AYear},{&quot;vendorID&quot;,vendorID}}]" ContinueOnError="True" DisplayName="VerifyInvoiceExists Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_3" OutputArguments="[invoiceExistsOcrWorkflowOutput]" ResponseCode="[invoiceExistsOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\VerifyInvoiceExists.xaml&quot;]" />
    <Assign sap2010:WorkflowViewState.IdRef="Assign_66">
      <Assign.To>
        <OutArgument x:TypeArguments="x:Boolean">[InvoiceAlreadyExists]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:Boolean">[CType(invoiceExistsOcrWorkflowOutput("invoiceExist"),boolean)]</InArgument>
      </Assign.Value>
    </Assign>
    <If Condition="[NOT InvoiceAlreadyExists]" sap2010:WorkflowViewState.IdRef="If_20">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_30">
          <Sequence.Variables>
            <Variable x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="LinesDict" />
          </Sequence.Variables>
          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for vendor name" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_2" Response="[respObj0]" StatusCode="[StatusCode0]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/CRS620MI/GetBasicData&quot;]">
            <iai:IONAPIRequestWizard.Headers>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>Accept</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>application/json</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.Headers>
            <iai:IONAPIRequestWizard.QueryParameters>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="8">
                  <x:String>extendedresult</x:String>
                  <x:String>format</x:String>
                  <x:String>righttrim</x:String>
                  <x:String>excludeempty</x:String>
                  <x:String>dateformat</x:String>
                  <x:String>SUNO</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="8">
                  <x:String>false</x:String>
                  <x:String>PRETTY</x:String>
                  <x:String>true</x:String>
                  <x:String>false</x:String>
                  <x:String>YMD8</x:String>
                  <x:String>vendorId</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.QueryParameters>
          </iai:IONAPIRequestWizard>
          <If Condition="[StatusCode0 = 200]" sap2010:WorkflowViewState.IdRef="If_1">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_2">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_1">
                  <Assign.To>
                    <OutArgument x:TypeArguments="njl:JToken">[out0]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj0.ReadAsText)]</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[out0(&quot;results&quot;)(0)(&quot;records&quot;).ToString &lt;&gt; &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_2">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_6">
                      <Sequence.Variables>
                        <Variable x:TypeArguments="x:String" Name="geoc" />
                      </Sequence.Variables>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_4">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_4">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[company]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("CONO").ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_5">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[tepy]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("TEPY").ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_6">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[pyme]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("PYME").ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_196">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[countryCode]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("CSCD").ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_200">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[deliveryTerms]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("TECD").ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_7">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[cucd]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("CUCD").ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_51">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[vendorName]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("SUNM").ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_37">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[geoc]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("GEOC").ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <If Condition="[miscValues(&quot;BusinessRuleAPResp&quot;).ToString.ToUpper() &lt;&gt; &quot;NA&quot;]" sap2010:WorkflowViewState.IdRef="If_61">
                          <If.Then>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_72">
                              <Sequence.Variables>
                                <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="APRespRespDict" />
                                <Variable x:TypeArguments="x:Int32" Name="APRespRespCode" />
                              </Sequence.Variables>
                              <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;division&quot;,division},{&quot;company&quot;,company},{&quot;VendorID&quot;,vendorID}}]" ContinueOnError="True" DisplayName="AP resp Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_8" OutputArguments="[APRespRespDict]" ResponseCode="[APRespRespCode]" WorkflowFile="[projectPath+&quot;\APResp.xaml&quot;]" />
                              <If Condition="[APRespRespCode = 200]" sap2010:WorkflowViewState.IdRef="If_59">
                                <If.Then>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_168">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[APResp]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[CType(APRespRespDict("APResp"), String)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </If.Then>
                              </If>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_169">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[authUser]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[APResp]</InArgument>
                                </Assign.Value>
                              </Assign>
                            </Sequence>
                          </If.Then>
                          <If.Else>
                            <If Condition="[authUser.ToUpper() = &quot;SUPPLIERAUTHUSER&quot;]" sap2010:WorkflowViewState.IdRef="If_60">
                              <If.Then>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_73">
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_11">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[authUser]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("RESP").ToString]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </Sequence>
                              </If.Then>
                            </If>
                          </If.Else>
                        </If>
                      </Sequence>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_5">
                        <Sequence.Variables>
                          <Variable x:TypeArguments="x:String" Name="inbnValue" />
                          <Variable x:TypeArguments="x:String" Name="SupAcho" />
                          <Variable x:TypeArguments="x:Int32" Name="StatusCode4" />
                          <Variable x:TypeArguments="iru:ResponseObject" Name="respObj4" />
                          <Variable x:TypeArguments="njl:JToken" Name="out4" />
                          <Variable x:TypeArguments="x:String" Name="bkid" />
                          <Variable x:TypeArguments="s:DateTime" Name="ivdate1" />
                          <Variable x:TypeArguments="x:String" Name="strCondition" />
                        </Sequence.Variables>
                        <Assign DisplayName="Assign SupAcho" sap2010:WorkflowViewState.IdRef="Assign_43">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[SupAcho]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">["ACHO:"+vendorId + " AND BKTP: 03"]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for bkid" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_6" Response="[respObj4]" StatusCode="[StatusCode4]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/CRS692MI/SearchBnkAcc&quot;]">
                          <iai:IONAPIRequestWizard.Headers>
                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                <x:String>Accept</x:String>
                              </scg:List>
                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                <x:String>application/json</x:String>
                              </scg:List>
                            </scg:List>
                          </iai:IONAPIRequestWizard.Headers>
                          <iai:IONAPIRequestWizard.QueryParameters>
                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                              <scg:List x:TypeArguments="x:String" Capacity="8">
                                <x:String>SQRY</x:String>
                                <x:String>dateformat</x:String>
                                <x:String>excludeempty</x:String>
                                <x:String>righttrim</x:String>
                                <x:String>format</x:String>
                                <x:String>extendedresult</x:String>
                              </scg:List>
                              <scg:List x:TypeArguments="x:String" Capacity="8">
                                <x:String>SupAcho</x:String>
                                <x:String>YMD8</x:String>
                                <x:String>false</x:String>
                                <x:String>true</x:String>
                                <x:String>PRETTY</x:String>
                                <x:String>false</x:String>
                              </scg:List>
                            </scg:List>
                          </iai:IONAPIRequestWizard.QueryParameters>
                        </iai:IONAPIRequestWizard>
                        <If Condition="[StatusCode4 = 200]" sap2010:WorkflowViewState.IdRef="If_11">
                          <If.Then>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_21">
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_39">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="njl:JToken">[out4]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj4.ReadAsText)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <If Condition="[out4(&quot;results&quot;)(0)(&quot;records&quot;).ToString &lt;&gt; &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_10">
                                <If.Then>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_19">
                                    <Assign DisplayName="Assign bkid" sap2010:WorkflowViewState.IdRef="Assign_40">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[bkid]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">[(out4("results")(0)("records")(0)("BKID")).ToString]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                  </Sequence>
                                </If.Then>
                                <If.Else>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_20">
                                    <Assign DisplayName="Assign bkid" sap2010:WorkflowViewState.IdRef="Assign_41">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[bkid]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">
                                          <Literal x:TypeArguments="x:String" Value="" />
                                        </InArgument>
                                      </Assign.Value>
                                    </Assign>
                                  </Sequence>
                                </If.Else>
                              </If>
                            </Sequence>
                          </If.Then>
                        </If>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_10">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[cuam]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[DictOcrValues("TOTAL").Tostring]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_9">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[sino]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[DictOcrValues("INVOICE_RECEIPT_ID").Tostring]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign DisplayName="Assign ivdate" sap2010:WorkflowViewState.IdRef="Assign_64">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[ivdate]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[DateTime.ParseExact(DictOcrValues("INVOICE_RECEIPT_DATE").Tostring, "dd/MM/yyyy", Nothing).ToString("yyyyMMdd")]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_70">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[System.DateTime.Now.ToString("yyyyMMddHHmmssfff").tostring + " -"+ DictOcrValues("INVOICE_RECEIPT_ID").Tostring + "-" + division + "-" + vendorID]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_1">
                          <TryCatch.Try>
                            <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_110">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[correlationID.Substring(0,35)]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </TryCatch.Try>
                          <TryCatch.Catches>
                            <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_1">
                              <ActivityAction x:TypeArguments="s:Exception">
                                <ActivityAction.Argument>
                                  <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                </ActivityAction.Argument>
                                <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_111">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[correlationID]</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </ActivityAction>
                            </Catch>
                          </TryCatch.Catches>
                        </TryCatch>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_201">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[strCondition]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[miscValues("handleCashDiscount").ToString.tolower+"-"+miscValues("mandateGEOC").ToString.tolower]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Switch x:TypeArguments="x:String" DisplayName="Switch strCondition" Expression="[strCondition]" sap2010:WorkflowViewState.IdRef="Switch`1_1">
                          <Sequence x:Key="true-true" DisplayName="true-true Sequence" sap2010:WorkflowViewState.IdRef="Sequence_84">
                            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_40" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                              <iai:IONAPIRequestWizard.Headers>
                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                    <x:String>Accept</x:String>
                                  </scg:List>
                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                    <x:String>application/json</x:String>
                                  </scg:List>
                                </scg:List>
                              </iai:IONAPIRequestWizard.Headers>
                              <iai:IONAPIRequestWizard.QueryParameters>
                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                  <scg:List x:TypeArguments="x:String" Capacity="32">
                                    <x:String>SUNO</x:String>
                                    <x:String>IVDT</x:String>
                                    <x:String>DIVI</x:String>
                                    <x:String>SINO</x:String>
                                    <x:String>CUCD</x:String>
                                    <x:String>TEPY</x:String>
                                    <x:String>PYME</x:String>
                                    <x:String>CUAM</x:String>
                                    <x:String>IMCD</x:String>
                                    <x:String>CRTP</x:String>
                                    <x:String>dateformat</x:String>
                                    <x:String>excludeempty</x:String>
                                    <x:String>righttrim</x:String>
                                    <x:String>format</x:String>
                                    <x:String>extendedresult</x:String>
                                    <x:String>APCD</x:String>
                                    <x:String>GEOC</x:String>
                                    <x:String>CORI</x:String>
                                    <x:String>TECD</x:String>
                                  </scg:List>
                                  <scg:List x:TypeArguments="x:String" Capacity="32">
                                    <x:String>vendorId</x:String>
                                    <x:String>ivdate</x:String>
                                    <x:String>division</x:String>
                                    <x:String>sino</x:String>
                                    <x:String>cucd</x:String>
                                    <x:String>tepy</x:String>
                                    <x:String>pyme</x:String>
                                    <x:String>cuam</x:String>
                                    <x:String>0</x:String>
                                    <x:String>1</x:String>
                                    <x:String>YMD8</x:String>
                                    <x:String>false</x:String>
                                    <x:String>true</x:String>
                                    <x:String>PRETTY</x:String>
                                    <x:String>false</x:String>
                                    <x:String>authUser</x:String>
                                    <x:String>geoc</x:String>
                                    <x:String>correlationID</x:String>
                                    <x:String>deliveryTerms</x:String>
                                  </scg:List>
                                </scg:List>
                              </iai:IONAPIRequestWizard.QueryParameters>
                            </iai:IONAPIRequestWizard>
                            <If Condition="[StatusCode1 = 200  and respObj1.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" sap2010:WorkflowViewState.IdRef="If_72">
                              <If.Then>
                                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_41" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                  <iai:IONAPIRequestWizard.Headers>
                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                        <x:String>Accept</x:String>
                                      </scg:List>
                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                        <x:String>application/json</x:String>
                                      </scg:List>
                                    </scg:List>
                                  </iai:IONAPIRequestWizard.Headers>
                                  <iai:IONAPIRequestWizard.QueryParameters>
                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                      <scg:List x:TypeArguments="x:String" Capacity="32">
                                        <x:String>SUNO</x:String>
                                        <x:String>IVDT</x:String>
                                        <x:String>DIVI</x:String>
                                        <x:String>SINO</x:String>
                                        <x:String>CUCD</x:String>
                                        <x:String>TEPY</x:String>
                                        <x:String>PYME</x:String>
                                        <x:String>CUAM</x:String>
                                        <x:String>IMCD</x:String>
                                        <x:String>CRTP</x:String>
                                        <x:String>dateformat</x:String>
                                        <x:String>excludeempty</x:String>
                                        <x:String>righttrim</x:String>
                                        <x:String>format</x:String>
                                        <x:String>extendedresult</x:String>
                                        <x:String>APCD</x:String>
                                        <x:String>BKID</x:String>
                                        <x:String>GEOC</x:String>
                                        <x:String>CORI</x:String>
                                        <x:String>TECD</x:String>
                                      </scg:List>
                                      <scg:List x:TypeArguments="x:String" Capacity="32">
                                        <x:String>vendorId</x:String>
                                        <x:String>ivdate</x:String>
                                        <x:String>division</x:String>
                                        <x:String>sino</x:String>
                                        <x:String>cucd</x:String>
                                        <x:String>tepy</x:String>
                                        <x:String>pyme</x:String>
                                        <x:String>cuam</x:String>
                                        <x:String>0</x:String>
                                        <x:String>1</x:String>
                                        <x:String>YMD8</x:String>
                                        <x:String>false</x:String>
                                        <x:String>true</x:String>
                                        <x:String>PRETTY</x:String>
                                        <x:String>false</x:String>
                                        <x:String>authUser</x:String>
                                        <x:String>bkid</x:String>
                                        <x:String>geoc</x:String>
                                        <x:String>correlationID</x:String>
                                        <x:String>deliveryTerms</x:String>
                                      </scg:List>
                                    </scg:List>
                                  </iai:IONAPIRequestWizard.QueryParameters>
                                </iai:IONAPIRequestWizard>
                              </If.Then>
                            </If>
                          </Sequence>
                          <Sequence x:Key="true-false" DisplayName="true-false Sequence" sap2010:WorkflowViewState.IdRef="Sequence_85">
                            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_42" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                              <iai:IONAPIRequestWizard.Headers>
                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                    <x:String>Accept</x:String>
                                  </scg:List>
                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                    <x:String>application/json</x:String>
                                  </scg:List>
                                </scg:List>
                              </iai:IONAPIRequestWizard.Headers>
                              <iai:IONAPIRequestWizard.QueryParameters>
                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                  <scg:List x:TypeArguments="x:String" Capacity="32">
                                    <x:String>SUNO</x:String>
                                    <x:String>IVDT</x:String>
                                    <x:String>DIVI</x:String>
                                    <x:String>SINO</x:String>
                                    <x:String>CUCD</x:String>
                                    <x:String>TEPY</x:String>
                                    <x:String>PYME</x:String>
                                    <x:String>CUAM</x:String>
                                    <x:String>IMCD</x:String>
                                    <x:String>CRTP</x:String>
                                    <x:String>dateformat</x:String>
                                    <x:String>excludeempty</x:String>
                                    <x:String>righttrim</x:String>
                                    <x:String>format</x:String>
                                    <x:String>extendedresult</x:String>
                                    <x:String>APCD</x:String>
                                    <x:String>CORI</x:String>
                                    <x:String>TECD</x:String>
                                  </scg:List>
                                  <scg:List x:TypeArguments="x:String" Capacity="32">
                                    <x:String>vendorId</x:String>
                                    <x:String>ivdate</x:String>
                                    <x:String>division</x:String>
                                    <x:String>sino</x:String>
                                    <x:String>cucd</x:String>
                                    <x:String>tepy</x:String>
                                    <x:String>pyme</x:String>
                                    <x:String>cuam</x:String>
                                    <x:String>0</x:String>
                                    <x:String>1</x:String>
                                    <x:String>YMD8</x:String>
                                    <x:String>false</x:String>
                                    <x:String>true</x:String>
                                    <x:String>PRETTY</x:String>
                                    <x:String>false</x:String>
                                    <x:String>authUser</x:String>
                                    <x:String>correlationID</x:String>
                                    <x:String>deliveryTerms</x:String>
                                  </scg:List>
                                </scg:List>
                              </iai:IONAPIRequestWizard.QueryParameters>
                            </iai:IONAPIRequestWizard>
                            <If Condition="[StatusCode1 = 200  and respObj1.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" sap2010:WorkflowViewState.IdRef="If_73">
                              <If.Then>
                                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_43" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                  <iai:IONAPIRequestWizard.Headers>
                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                        <x:String>Accept</x:String>
                                      </scg:List>
                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                        <x:String>application/json</x:String>
                                      </scg:List>
                                    </scg:List>
                                  </iai:IONAPIRequestWizard.Headers>
                                  <iai:IONAPIRequestWizard.QueryParameters>
                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                      <scg:List x:TypeArguments="x:String" Capacity="32">
                                        <x:String>SUNO</x:String>
                                        <x:String>IVDT</x:String>
                                        <x:String>DIVI</x:String>
                                        <x:String>SINO</x:String>
                                        <x:String>CUCD</x:String>
                                        <x:String>TEPY</x:String>
                                        <x:String>PYME</x:String>
                                        <x:String>CUAM</x:String>
                                        <x:String>IMCD</x:String>
                                        <x:String>CRTP</x:String>
                                        <x:String>dateformat</x:String>
                                        <x:String>excludeempty</x:String>
                                        <x:String>righttrim</x:String>
                                        <x:String>format</x:String>
                                        <x:String>extendedresult</x:String>
                                        <x:String>APCD</x:String>
                                        <x:String>BKID</x:String>
                                        <x:String>CORI</x:String>
                                        <x:String>TECD</x:String>
                                      </scg:List>
                                      <scg:List x:TypeArguments="x:String" Capacity="32">
                                        <x:String>vendorId</x:String>
                                        <x:String>ivdate</x:String>
                                        <x:String>division</x:String>
                                        <x:String>sino</x:String>
                                        <x:String>cucd</x:String>
                                        <x:String>tepy</x:String>
                                        <x:String>pyme</x:String>
                                        <x:String>cuam</x:String>
                                        <x:String>0</x:String>
                                        <x:String>1</x:String>
                                        <x:String>YMD8</x:String>
                                        <x:String>false</x:String>
                                        <x:String>true</x:String>
                                        <x:String>PRETTY</x:String>
                                        <x:String>false</x:String>
                                        <x:String>authUser</x:String>
                                        <x:String>bkid</x:String>
                                        <x:String>correlationID</x:String>
                                        <x:String>deliveryTerms</x:String>
                                      </scg:List>
                                    </scg:List>
                                  </iai:IONAPIRequestWizard.QueryParameters>
                                </iai:IONAPIRequestWizard>
                              </If.Then>
                            </If>
                          </Sequence>
                          <Sequence x:Key="false-true" DisplayName="false-true Sequence" sap2010:WorkflowViewState.IdRef="Sequence_86">
                            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_44" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                              <iai:IONAPIRequestWizard.Headers>
                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                    <x:String>Accept</x:String>
                                  </scg:List>
                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                    <x:String>application/json</x:String>
                                  </scg:List>
                                </scg:List>
                              </iai:IONAPIRequestWizard.Headers>
                              <iai:IONAPIRequestWizard.QueryParameters>
                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                  <scg:List x:TypeArguments="x:String" Capacity="32">
                                    <x:String>SUNO</x:String>
                                    <x:String>IVDT</x:String>
                                    <x:String>DIVI</x:String>
                                    <x:String>SINO</x:String>
                                    <x:String>CUCD</x:String>
                                    <x:String>TEPY</x:String>
                                    <x:String>PYME</x:String>
                                    <x:String>CUAM</x:String>
                                    <x:String>IMCD</x:String>
                                    <x:String>CRTP</x:String>
                                    <x:String>dateformat</x:String>
                                    <x:String>excludeempty</x:String>
                                    <x:String>righttrim</x:String>
                                    <x:String>format</x:String>
                                    <x:String>extendedresult</x:String>
                                    <x:String>APCD</x:String>
                                    <x:String>GEOC</x:String>
                                    <x:String>CORI</x:String>
                                  </scg:List>
                                  <scg:List x:TypeArguments="x:String" Capacity="32">
                                    <x:String>vendorId</x:String>
                                    <x:String>ivdate</x:String>
                                    <x:String>division</x:String>
                                    <x:String>sino</x:String>
                                    <x:String>cucd</x:String>
                                    <x:String>tepy</x:String>
                                    <x:String>pyme</x:String>
                                    <x:String>cuam</x:String>
                                    <x:String>0</x:String>
                                    <x:String>1</x:String>
                                    <x:String>YMD8</x:String>
                                    <x:String>false</x:String>
                                    <x:String>true</x:String>
                                    <x:String>PRETTY</x:String>
                                    <x:String>false</x:String>
                                    <x:String>authUser</x:String>
                                    <x:String>geoc</x:String>
                                    <x:String>correlationID</x:String>
                                  </scg:List>
                                </scg:List>
                              </iai:IONAPIRequestWizard.QueryParameters>
                            </iai:IONAPIRequestWizard>
                            <If Condition="[StatusCode1 = 200  and respObj1.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" sap2010:WorkflowViewState.IdRef="If_74">
                              <If.Then>
                                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_45" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                  <iai:IONAPIRequestWizard.Headers>
                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                        <x:String>Accept</x:String>
                                      </scg:List>
                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                        <x:String>application/json</x:String>
                                      </scg:List>
                                    </scg:List>
                                  </iai:IONAPIRequestWizard.Headers>
                                  <iai:IONAPIRequestWizard.QueryParameters>
                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                      <scg:List x:TypeArguments="x:String" Capacity="32">
                                        <x:String>SUNO</x:String>
                                        <x:String>IVDT</x:String>
                                        <x:String>DIVI</x:String>
                                        <x:String>SINO</x:String>
                                        <x:String>CUCD</x:String>
                                        <x:String>TEPY</x:String>
                                        <x:String>PYME</x:String>
                                        <x:String>CUAM</x:String>
                                        <x:String>IMCD</x:String>
                                        <x:String>CRTP</x:String>
                                        <x:String>dateformat</x:String>
                                        <x:String>excludeempty</x:String>
                                        <x:String>righttrim</x:String>
                                        <x:String>format</x:String>
                                        <x:String>extendedresult</x:String>
                                        <x:String>APCD</x:String>
                                        <x:String>BKID</x:String>
                                        <x:String>GEOC</x:String>
                                        <x:String>CORI</x:String>
                                      </scg:List>
                                      <scg:List x:TypeArguments="x:String" Capacity="32">
                                        <x:String>vendorId</x:String>
                                        <x:String>ivdate</x:String>
                                        <x:String>division</x:String>
                                        <x:String>sino</x:String>
                                        <x:String>cucd</x:String>
                                        <x:String>tepy</x:String>
                                        <x:String>pyme</x:String>
                                        <x:String>cuam</x:String>
                                        <x:String>0</x:String>
                                        <x:String>1</x:String>
                                        <x:String>YMD8</x:String>
                                        <x:String>false</x:String>
                                        <x:String>true</x:String>
                                        <x:String>PRETTY</x:String>
                                        <x:String>false</x:String>
                                        <x:String>authUser</x:String>
                                        <x:String>bkid</x:String>
                                        <x:String>geoc</x:String>
                                        <x:String>correlationID</x:String>
                                      </scg:List>
                                    </scg:List>
                                  </iai:IONAPIRequestWizard.QueryParameters>
                                </iai:IONAPIRequestWizard>
                              </If.Then>
                            </If>
                          </Sequence>
                          <Sequence x:Key="false-false" DisplayName="false-false Sequence" sap2010:WorkflowViewState.IdRef="Sequence_87">
                            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_46" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                              <iai:IONAPIRequestWizard.Headers>
                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                    <x:String>Accept</x:String>
                                  </scg:List>
                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                    <x:String>application/json</x:String>
                                  </scg:List>
                                </scg:List>
                              </iai:IONAPIRequestWizard.Headers>
                              <iai:IONAPIRequestWizard.QueryParameters>
                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                  <scg:List x:TypeArguments="x:String" Capacity="32">
                                    <x:String>SUNO</x:String>
                                    <x:String>IVDT</x:String>
                                    <x:String>DIVI</x:String>
                                    <x:String>SINO</x:String>
                                    <x:String>CUCD</x:String>
                                    <x:String>TEPY</x:String>
                                    <x:String>PYME</x:String>
                                    <x:String>CUAM</x:String>
                                    <x:String>IMCD</x:String>
                                    <x:String>CRTP</x:String>
                                    <x:String>dateformat</x:String>
                                    <x:String>excludeempty</x:String>
                                    <x:String>righttrim</x:String>
                                    <x:String>format</x:String>
                                    <x:String>extendedresult</x:String>
                                    <x:String>APCD</x:String>
                                    <x:String>CORI</x:String>
                                  </scg:List>
                                  <scg:List x:TypeArguments="x:String" Capacity="32">
                                    <x:String>vendorId</x:String>
                                    <x:String>ivdate</x:String>
                                    <x:String>division</x:String>
                                    <x:String>sino</x:String>
                                    <x:String>cucd</x:String>
                                    <x:String>tepy</x:String>
                                    <x:String>pyme</x:String>
                                    <x:String>cuam</x:String>
                                    <x:String>0</x:String>
                                    <x:String>1</x:String>
                                    <x:String>YMD8</x:String>
                                    <x:String>false</x:String>
                                    <x:String>true</x:String>
                                    <x:String>PRETTY</x:String>
                                    <x:String>false</x:String>
                                    <x:String>authUser</x:String>
                                    <x:String>correlationID</x:String>
                                  </scg:List>
                                </scg:List>
                              </iai:IONAPIRequestWizard.QueryParameters>
                            </iai:IONAPIRequestWizard>
                            <If Condition="[StatusCode1 = 200  and respObj1.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" sap2010:WorkflowViewState.IdRef="If_75">
                              <If.Then>
                                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_47" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                  <iai:IONAPIRequestWizard.Headers>
                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                        <x:String>Accept</x:String>
                                      </scg:List>
                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                        <x:String>application/json</x:String>
                                      </scg:List>
                                    </scg:List>
                                  </iai:IONAPIRequestWizard.Headers>
                                  <iai:IONAPIRequestWizard.QueryParameters>
                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                      <scg:List x:TypeArguments="x:String" Capacity="32">
                                        <x:String>SUNO</x:String>
                                        <x:String>IVDT</x:String>
                                        <x:String>DIVI</x:String>
                                        <x:String>SINO</x:String>
                                        <x:String>CUCD</x:String>
                                        <x:String>TEPY</x:String>
                                        <x:String>PYME</x:String>
                                        <x:String>CUAM</x:String>
                                        <x:String>IMCD</x:String>
                                        <x:String>CRTP</x:String>
                                        <x:String>dateformat</x:String>
                                        <x:String>excludeempty</x:String>
                                        <x:String>righttrim</x:String>
                                        <x:String>format</x:String>
                                        <x:String>extendedresult</x:String>
                                        <x:String>APCD</x:String>
                                        <x:String>BKID</x:String>
                                        <x:String>CORI</x:String>
                                      </scg:List>
                                      <scg:List x:TypeArguments="x:String" Capacity="32">
                                        <x:String>vendorId</x:String>
                                        <x:String>ivdate</x:String>
                                        <x:String>division</x:String>
                                        <x:String>sino</x:String>
                                        <x:String>cucd</x:String>
                                        <x:String>tepy</x:String>
                                        <x:String>pyme</x:String>
                                        <x:String>cuam</x:String>
                                        <x:String>0</x:String>
                                        <x:String>1</x:String>
                                        <x:String>YMD8</x:String>
                                        <x:String>false</x:String>
                                        <x:String>true</x:String>
                                        <x:String>PRETTY</x:String>
                                        <x:String>false</x:String>
                                        <x:String>authUser</x:String>
                                        <x:String>bkid</x:String>
                                        <x:String>correlationID</x:String>
                                      </scg:List>
                                    </scg:List>
                                  </iai:IONAPIRequestWizard.QueryParameters>
                                </iai:IONAPIRequestWizard>
                              </If.Then>
                            </If>
                          </Sequence>
                        </Switch>
                        <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_4">
                          <iad:CommentOut.Activities>
                            <If Condition="[miscValues(&quot;handleCashDiscount&quot;).ToString.tolower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_68">
                              <If.Then>
                                <If Condition="[miscValues(&quot;mandateGEOC&quot;).ToString.tolower = &quot;true&quot;]" DisplayName="If Master" sap2010:WorkflowViewState.IdRef="If_67">
                                  <If.Then>
                                    <Sequence DisplayName="true-true Sequence" sap2010:WorkflowViewState.IdRef="Sequence_77">
                                      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_28" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                        <iai:IONAPIRequestWizard.Headers>
                                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                            <scg:List x:TypeArguments="x:String" Capacity="4">
                                              <x:String>Accept</x:String>
                                            </scg:List>
                                            <scg:List x:TypeArguments="x:String" Capacity="4">
                                              <x:String>application/json</x:String>
                                            </scg:List>
                                          </scg:List>
                                        </iai:IONAPIRequestWizard.Headers>
                                        <iai:IONAPIRequestWizard.QueryParameters>
                                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                            <scg:List x:TypeArguments="x:String" Capacity="32">
                                              <x:String>SUNO</x:String>
                                              <x:String>IVDT</x:String>
                                              <x:String>DIVI</x:String>
                                              <x:String>SINO</x:String>
                                              <x:String>CUCD</x:String>
                                              <x:String>TEPY</x:String>
                                              <x:String>PYME</x:String>
                                              <x:String>CUAM</x:String>
                                              <x:String>IMCD</x:String>
                                              <x:String>CRTP</x:String>
                                              <x:String>dateformat</x:String>
                                              <x:String>excludeempty</x:String>
                                              <x:String>righttrim</x:String>
                                              <x:String>format</x:String>
                                              <x:String>extendedresult</x:String>
                                              <x:String>APCD</x:String>
                                              <x:String>GEOC</x:String>
                                              <x:String>CORI</x:String>
                                              <x:String>TECD</x:String>
                                            </scg:List>
                                            <scg:List x:TypeArguments="x:String" Capacity="32">
                                              <x:String>vendorId</x:String>
                                              <x:String>ivdate</x:String>
                                              <x:String>division</x:String>
                                              <x:String>sino</x:String>
                                              <x:String>cucd</x:String>
                                              <x:String>tepy</x:String>
                                              <x:String>pyme</x:String>
                                              <x:String>cuam</x:String>
                                              <x:String>0</x:String>
                                              <x:String>1</x:String>
                                              <x:String>YMD8</x:String>
                                              <x:String>false</x:String>
                                              <x:String>true</x:String>
                                              <x:String>PRETTY</x:String>
                                              <x:String>false</x:String>
                                              <x:String>authUser</x:String>
                                              <x:String>geoc</x:String>
                                              <x:String>correlationID</x:String>
                                              <x:String>deliveryTerms</x:String>
                                            </scg:List>
                                          </scg:List>
                                        </iai:IONAPIRequestWizard.QueryParameters>
                                      </iai:IONAPIRequestWizard>
                                      <If Condition="[StatusCode1 = 200  and respObj1.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" sap2010:WorkflowViewState.IdRef="If_65">
                                        <If.Then>
                                          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_36" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                            <iai:IONAPIRequestWizard.Headers>
                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                  <x:String>Accept</x:String>
                                                </scg:List>
                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                  <x:String>application/json</x:String>
                                                </scg:List>
                                              </scg:List>
                                            </iai:IONAPIRequestWizard.Headers>
                                            <iai:IONAPIRequestWizard.QueryParameters>
                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                <scg:List x:TypeArguments="x:String" Capacity="32">
                                                  <x:String>SUNO</x:String>
                                                  <x:String>IVDT</x:String>
                                                  <x:String>DIVI</x:String>
                                                  <x:String>SINO</x:String>
                                                  <x:String>CUCD</x:String>
                                                  <x:String>TEPY</x:String>
                                                  <x:String>PYME</x:String>
                                                  <x:String>CUAM</x:String>
                                                  <x:String>IMCD</x:String>
                                                  <x:String>CRTP</x:String>
                                                  <x:String>dateformat</x:String>
                                                  <x:String>excludeempty</x:String>
                                                  <x:String>righttrim</x:String>
                                                  <x:String>format</x:String>
                                                  <x:String>extendedresult</x:String>
                                                  <x:String>APCD</x:String>
                                                  <x:String>BKID</x:String>
                                                  <x:String>GEOC</x:String>
                                                  <x:String>CORI</x:String>
                                                  <x:String>TECD</x:String>
                                                </scg:List>
                                                <scg:List x:TypeArguments="x:String" Capacity="32">
                                                  <x:String>vendorId</x:String>
                                                  <x:String>ivdate</x:String>
                                                  <x:String>division</x:String>
                                                  <x:String>sino</x:String>
                                                  <x:String>cucd</x:String>
                                                  <x:String>tepy</x:String>
                                                  <x:String>pyme</x:String>
                                                  <x:String>cuam</x:String>
                                                  <x:String>0</x:String>
                                                  <x:String>1</x:String>
                                                  <x:String>YMD8</x:String>
                                                  <x:String>false</x:String>
                                                  <x:String>true</x:String>
                                                  <x:String>PRETTY</x:String>
                                                  <x:String>false</x:String>
                                                  <x:String>authUser</x:String>
                                                  <x:String>bkid</x:String>
                                                  <x:String>geoc</x:String>
                                                  <x:String>correlationID</x:String>
                                                  <x:String>deliveryTerms</x:String>
                                                </scg:List>
                                              </scg:List>
                                            </iai:IONAPIRequestWizard.QueryParameters>
                                          </iai:IONAPIRequestWizard>
                                        </If.Then>
                                      </If>
                                    </Sequence>
                                  </If.Then>
                                  <If.Else>
                                    <Sequence DisplayName="true-false Sequence" sap2010:WorkflowViewState.IdRef="Sequence_79">
                                      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_30" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                        <iai:IONAPIRequestWizard.Headers>
                                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                            <scg:List x:TypeArguments="x:String" Capacity="4">
                                              <x:String>Accept</x:String>
                                            </scg:List>
                                            <scg:List x:TypeArguments="x:String" Capacity="4">
                                              <x:String>application/json</x:String>
                                            </scg:List>
                                          </scg:List>
                                        </iai:IONAPIRequestWizard.Headers>
                                        <iai:IONAPIRequestWizard.QueryParameters>
                                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                            <scg:List x:TypeArguments="x:String" Capacity="32">
                                              <x:String>SUNO</x:String>
                                              <x:String>IVDT</x:String>
                                              <x:String>DIVI</x:String>
                                              <x:String>SINO</x:String>
                                              <x:String>CUCD</x:String>
                                              <x:String>TEPY</x:String>
                                              <x:String>PYME</x:String>
                                              <x:String>CUAM</x:String>
                                              <x:String>IMCD</x:String>
                                              <x:String>CRTP</x:String>
                                              <x:String>dateformat</x:String>
                                              <x:String>excludeempty</x:String>
                                              <x:String>righttrim</x:String>
                                              <x:String>format</x:String>
                                              <x:String>extendedresult</x:String>
                                              <x:String>APCD</x:String>
                                              <x:String>CORI</x:String>
                                              <x:String>TECD</x:String>
                                            </scg:List>
                                            <scg:List x:TypeArguments="x:String" Capacity="32">
                                              <x:String>vendorId</x:String>
                                              <x:String>ivdate</x:String>
                                              <x:String>division</x:String>
                                              <x:String>sino</x:String>
                                              <x:String>cucd</x:String>
                                              <x:String>tepy</x:String>
                                              <x:String>pyme</x:String>
                                              <x:String>cuam</x:String>
                                              <x:String>0</x:String>
                                              <x:String>1</x:String>
                                              <x:String>YMD8</x:String>
                                              <x:String>false</x:String>
                                              <x:String>true</x:String>
                                              <x:String>PRETTY</x:String>
                                              <x:String>false</x:String>
                                              <x:String>authUser</x:String>
                                              <x:String>correlationID</x:String>
                                              <x:String>deliveryTerms</x:String>
                                            </scg:List>
                                          </scg:List>
                                        </iai:IONAPIRequestWizard.QueryParameters>
                                      </iai:IONAPIRequestWizard>
                                      <If Condition="[StatusCode1 = 200  and respObj1.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" sap2010:WorkflowViewState.IdRef="If_66">
                                        <If.Then>
                                          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_37" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                            <iai:IONAPIRequestWizard.Headers>
                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                  <x:String>Accept</x:String>
                                                </scg:List>
                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                  <x:String>application/json</x:String>
                                                </scg:List>
                                              </scg:List>
                                            </iai:IONAPIRequestWizard.Headers>
                                            <iai:IONAPIRequestWizard.QueryParameters>
                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                <scg:List x:TypeArguments="x:String" Capacity="32">
                                                  <x:String>SUNO</x:String>
                                                  <x:String>IVDT</x:String>
                                                  <x:String>DIVI</x:String>
                                                  <x:String>SINO</x:String>
                                                  <x:String>CUCD</x:String>
                                                  <x:String>TEPY</x:String>
                                                  <x:String>PYME</x:String>
                                                  <x:String>CUAM</x:String>
                                                  <x:String>IMCD</x:String>
                                                  <x:String>CRTP</x:String>
                                                  <x:String>dateformat</x:String>
                                                  <x:String>excludeempty</x:String>
                                                  <x:String>righttrim</x:String>
                                                  <x:String>format</x:String>
                                                  <x:String>extendedresult</x:String>
                                                  <x:String>APCD</x:String>
                                                  <x:String>BKID</x:String>
                                                  <x:String>CORI</x:String>
                                                  <x:String>TECD</x:String>
                                                </scg:List>
                                                <scg:List x:TypeArguments="x:String" Capacity="32">
                                                  <x:String>vendorId</x:String>
                                                  <x:String>ivdate</x:String>
                                                  <x:String>division</x:String>
                                                  <x:String>sino</x:String>
                                                  <x:String>cucd</x:String>
                                                  <x:String>tepy</x:String>
                                                  <x:String>pyme</x:String>
                                                  <x:String>cuam</x:String>
                                                  <x:String>0</x:String>
                                                  <x:String>1</x:String>
                                                  <x:String>YMD8</x:String>
                                                  <x:String>false</x:String>
                                                  <x:String>true</x:String>
                                                  <x:String>PRETTY</x:String>
                                                  <x:String>false</x:String>
                                                  <x:String>authUser</x:String>
                                                  <x:String>bkid</x:String>
                                                  <x:String>correlationID</x:String>
                                                  <x:String>deliveryTerms</x:String>
                                                </scg:List>
                                              </scg:List>
                                            </iai:IONAPIRequestWizard.QueryParameters>
                                          </iai:IONAPIRequestWizard>
                                        </If.Then>
                                      </If>
                                    </Sequence>
                                  </If.Else>
                                </If>
                              </If.Then>
                              <If.Else>
                                <If Condition="[miscValues(&quot;mandateGEOC&quot;).ToString.tolower = &quot;true&quot;]" DisplayName="If Master" sap2010:WorkflowViewState.IdRef="If_71">
                                  <If.Then>
                                    <Sequence DisplayName="false-true Sequence" sap2010:WorkflowViewState.IdRef="Sequence_81">
                                      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_32" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                        <iai:IONAPIRequestWizard.Headers>
                                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                            <scg:List x:TypeArguments="x:String" Capacity="4">
                                              <x:String>Accept</x:String>
                                            </scg:List>
                                            <scg:List x:TypeArguments="x:String" Capacity="4">
                                              <x:String>application/json</x:String>
                                            </scg:List>
                                          </scg:List>
                                        </iai:IONAPIRequestWizard.Headers>
                                        <iai:IONAPIRequestWizard.QueryParameters>
                                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                            <scg:List x:TypeArguments="x:String" Capacity="32">
                                              <x:String>SUNO</x:String>
                                              <x:String>IVDT</x:String>
                                              <x:String>DIVI</x:String>
                                              <x:String>SINO</x:String>
                                              <x:String>CUCD</x:String>
                                              <x:String>TEPY</x:String>
                                              <x:String>PYME</x:String>
                                              <x:String>CUAM</x:String>
                                              <x:String>IMCD</x:String>
                                              <x:String>CRTP</x:String>
                                              <x:String>dateformat</x:String>
                                              <x:String>excludeempty</x:String>
                                              <x:String>righttrim</x:String>
                                              <x:String>format</x:String>
                                              <x:String>extendedresult</x:String>
                                              <x:String>APCD</x:String>
                                              <x:String>GEOC</x:String>
                                              <x:String>CORI</x:String>
                                            </scg:List>
                                            <scg:List x:TypeArguments="x:String" Capacity="32">
                                              <x:String>vendorId</x:String>
                                              <x:String>ivdate</x:String>
                                              <x:String>division</x:String>
                                              <x:String>sino</x:String>
                                              <x:String>cucd</x:String>
                                              <x:String>tepy</x:String>
                                              <x:String>pyme</x:String>
                                              <x:String>cuam</x:String>
                                              <x:String>0</x:String>
                                              <x:String>1</x:String>
                                              <x:String>YMD8</x:String>
                                              <x:String>false</x:String>
                                              <x:String>true</x:String>
                                              <x:String>PRETTY</x:String>
                                              <x:String>false</x:String>
                                              <x:String>authUser</x:String>
                                              <x:String>geoc</x:String>
                                              <x:String>correlationID</x:String>
                                            </scg:List>
                                          </scg:List>
                                        </iai:IONAPIRequestWizard.QueryParameters>
                                      </iai:IONAPIRequestWizard>
                                      <If Condition="[StatusCode1 = 200  and respObj1.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" sap2010:WorkflowViewState.IdRef="If_69">
                                        <If.Then>
                                          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_38" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                            <iai:IONAPIRequestWizard.Headers>
                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                  <x:String>Accept</x:String>
                                                </scg:List>
                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                  <x:String>application/json</x:String>
                                                </scg:List>
                                              </scg:List>
                                            </iai:IONAPIRequestWizard.Headers>
                                            <iai:IONAPIRequestWizard.QueryParameters>
                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                <scg:List x:TypeArguments="x:String" Capacity="32">
                                                  <x:String>SUNO</x:String>
                                                  <x:String>IVDT</x:String>
                                                  <x:String>DIVI</x:String>
                                                  <x:String>SINO</x:String>
                                                  <x:String>CUCD</x:String>
                                                  <x:String>TEPY</x:String>
                                                  <x:String>PYME</x:String>
                                                  <x:String>CUAM</x:String>
                                                  <x:String>IMCD</x:String>
                                                  <x:String>CRTP</x:String>
                                                  <x:String>dateformat</x:String>
                                                  <x:String>excludeempty</x:String>
                                                  <x:String>righttrim</x:String>
                                                  <x:String>format</x:String>
                                                  <x:String>extendedresult</x:String>
                                                  <x:String>APCD</x:String>
                                                  <x:String>BKID</x:String>
                                                  <x:String>GEOC</x:String>
                                                  <x:String>CORI</x:String>
                                                </scg:List>
                                                <scg:List x:TypeArguments="x:String" Capacity="32">
                                                  <x:String>vendorId</x:String>
                                                  <x:String>ivdate</x:String>
                                                  <x:String>division</x:String>
                                                  <x:String>sino</x:String>
                                                  <x:String>cucd</x:String>
                                                  <x:String>tepy</x:String>
                                                  <x:String>pyme</x:String>
                                                  <x:String>cuam</x:String>
                                                  <x:String>0</x:String>
                                                  <x:String>1</x:String>
                                                  <x:String>YMD8</x:String>
                                                  <x:String>false</x:String>
                                                  <x:String>true</x:String>
                                                  <x:String>PRETTY</x:String>
                                                  <x:String>false</x:String>
                                                  <x:String>authUser</x:String>
                                                  <x:String>bkid</x:String>
                                                  <x:String>geoc</x:String>
                                                  <x:String>correlationID</x:String>
                                                </scg:List>
                                              </scg:List>
                                            </iai:IONAPIRequestWizard.QueryParameters>
                                          </iai:IONAPIRequestWizard>
                                        </If.Then>
                                      </If>
                                    </Sequence>
                                  </If.Then>
                                  <If.Else>
                                    <Sequence DisplayName="false-false Sequence" sap2010:WorkflowViewState.IdRef="Sequence_83">
                                      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_34" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                        <iai:IONAPIRequestWizard.Headers>
                                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                            <scg:List x:TypeArguments="x:String" Capacity="4">
                                              <x:String>Accept</x:String>
                                            </scg:List>
                                            <scg:List x:TypeArguments="x:String" Capacity="4">
                                              <x:String>application/json</x:String>
                                            </scg:List>
                                          </scg:List>
                                        </iai:IONAPIRequestWizard.Headers>
                                        <iai:IONAPIRequestWizard.QueryParameters>
                                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                            <scg:List x:TypeArguments="x:String" Capacity="32">
                                              <x:String>SUNO</x:String>
                                              <x:String>IVDT</x:String>
                                              <x:String>DIVI</x:String>
                                              <x:String>SINO</x:String>
                                              <x:String>CUCD</x:String>
                                              <x:String>TEPY</x:String>
                                              <x:String>PYME</x:String>
                                              <x:String>CUAM</x:String>
                                              <x:String>IMCD</x:String>
                                              <x:String>CRTP</x:String>
                                              <x:String>dateformat</x:String>
                                              <x:String>excludeempty</x:String>
                                              <x:String>righttrim</x:String>
                                              <x:String>format</x:String>
                                              <x:String>extendedresult</x:String>
                                              <x:String>APCD</x:String>
                                              <x:String>CORI</x:String>
                                            </scg:List>
                                            <scg:List x:TypeArguments="x:String" Capacity="32">
                                              <x:String>vendorId</x:String>
                                              <x:String>ivdate</x:String>
                                              <x:String>division</x:String>
                                              <x:String>sino</x:String>
                                              <x:String>cucd</x:String>
                                              <x:String>tepy</x:String>
                                              <x:String>pyme</x:String>
                                              <x:String>cuam</x:String>
                                              <x:String>0</x:String>
                                              <x:String>1</x:String>
                                              <x:String>YMD8</x:String>
                                              <x:String>false</x:String>
                                              <x:String>true</x:String>
                                              <x:String>PRETTY</x:String>
                                              <x:String>false</x:String>
                                              <x:String>authUser</x:String>
                                              <x:String>correlationID</x:String>
                                            </scg:List>
                                          </scg:List>
                                        </iai:IONAPIRequestWizard.QueryParameters>
                                      </iai:IONAPIRequestWizard>
                                      <If Condition="[StatusCode1 = 200  and respObj1.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" sap2010:WorkflowViewState.IdRef="If_70">
                                        <If.Then>
                                          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_39" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                            <iai:IONAPIRequestWizard.Headers>
                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                  <x:String>Accept</x:String>
                                                </scg:List>
                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                  <x:String>application/json</x:String>
                                                </scg:List>
                                              </scg:List>
                                            </iai:IONAPIRequestWizard.Headers>
                                            <iai:IONAPIRequestWizard.QueryParameters>
                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                <scg:List x:TypeArguments="x:String" Capacity="32">
                                                  <x:String>SUNO</x:String>
                                                  <x:String>IVDT</x:String>
                                                  <x:String>DIVI</x:String>
                                                  <x:String>SINO</x:String>
                                                  <x:String>CUCD</x:String>
                                                  <x:String>TEPY</x:String>
                                                  <x:String>PYME</x:String>
                                                  <x:String>CUAM</x:String>
                                                  <x:String>IMCD</x:String>
                                                  <x:String>CRTP</x:String>
                                                  <x:String>dateformat</x:String>
                                                  <x:String>excludeempty</x:String>
                                                  <x:String>righttrim</x:String>
                                                  <x:String>format</x:String>
                                                  <x:String>extendedresult</x:String>
                                                  <x:String>APCD</x:String>
                                                  <x:String>BKID</x:String>
                                                  <x:String>CORI</x:String>
                                                </scg:List>
                                                <scg:List x:TypeArguments="x:String" Capacity="32">
                                                  <x:String>vendorId</x:String>
                                                  <x:String>ivdate</x:String>
                                                  <x:String>division</x:String>
                                                  <x:String>sino</x:String>
                                                  <x:String>cucd</x:String>
                                                  <x:String>tepy</x:String>
                                                  <x:String>pyme</x:String>
                                                  <x:String>cuam</x:String>
                                                  <x:String>0</x:String>
                                                  <x:String>1</x:String>
                                                  <x:String>YMD8</x:String>
                                                  <x:String>false</x:String>
                                                  <x:String>true</x:String>
                                                  <x:String>PRETTY</x:String>
                                                  <x:String>false</x:String>
                                                  <x:String>authUser</x:String>
                                                  <x:String>bkid</x:String>
                                                  <x:String>correlationID</x:String>
                                                </scg:List>
                                              </scg:List>
                                            </iai:IONAPIRequestWizard.QueryParameters>
                                          </iai:IONAPIRequestWizard>
                                        </If.Then>
                                      </If>
                                    </Sequence>
                                  </If.Else>
                                </If>
                              </If.Else>
                            </If>
                          </iad:CommentOut.Activities>
                        </iad:CommentOut>
                        <If Condition="[StatusCode1 = 200]" sap2010:WorkflowViewState.IdRef="If_5">
                          <If.Then>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_10">
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_12">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj1.ReadAsText)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_4">
                                <If.Then>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_8">
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_13">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:Boolean">[InvoiceAlreadyExists]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_14">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">[(out1("results")(0)("errorMessage")).ToString]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_15">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                  </Sequence>
                                </If.Then>
                                <If.Else>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_9">
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_16">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:Boolean">[InvoiceAlreadyExists]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_17">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[inbnValue]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">[(out1("results")(0)("records")(0)("INBN")).ToString]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_18">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">["Invoice Header Created"]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_6" Line="[commentStatus]" Source="[logfile]" />
                                  </Sequence>
                                </If.Else>
                              </If>
                              <If Condition="[NOT InvoiceAlreadyExists]" sap2010:WorkflowViewState.IdRef="If_6">
                                <If.Then>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_12">
                                    <Sequence.Variables>
                                      <Variable x:TypeArguments="x:String" Name="accRule" />
                                      <Variable x:TypeArguments="x:String" Name="productGr" />
                                      <Variable x:TypeArguments="x:String" Name="orderNo" />
                                      <Variable x:TypeArguments="x:String" Name="partnerCo" />
                                      <Variable x:TypeArguments="x:String" Name="finGrp" />
                                      <Variable x:TypeArguments="x:String" Name="costCtr" />
                                      <Variable x:TypeArguments="x:String" Name="account" />
                                      <Variable x:TypeArguments="x:String" Name="AIT1" />
                                      <Variable x:TypeArguments="x:String" Name="AIT2" />
                                      <Variable x:TypeArguments="x:String" Name="AIT3" />
                                      <Variable x:TypeArguments="x:String" Name="AIT4" />
                                      <Variable x:TypeArguments="x:String" Name="AIT5" />
                                      <Variable x:TypeArguments="x:String" Name="AIT6" />
                                      <Variable x:TypeArguments="x:String" Name="AIT7" />
                                    </Sequence.Variables>
                                    <If Condition="[GLCode = &quot;DISTRIBUTED&quot; AND ListocrLineValues.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_29">
                                      <If.Then>
                                        <ForEach x:TypeArguments="scg:Dictionary(x:String, x:Object)" DisplayName="ForEach&lt;Dictionary&lt;String,Object&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_12" Values="[ListocrLineValues]">
                                          <ActivityAction x:TypeArguments="scg:Dictionary(x:String, x:Object)">
                                            <ActivityAction.Argument>
                                              <DelegateInArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="item" />
                                            </ActivityAction.Argument>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_40">
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_194">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary (Of String, Object)]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_195">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[item]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_88">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[AIT1]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(0)]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_90">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[AIT2]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(1)]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_91">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[AIT3]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(2)]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_92">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[AIT4]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(3)]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_89">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[AIT5]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(4)]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_93">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[AIT6]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(5)]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_94">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[AIT7]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(6)]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_28">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[amt]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[LinesDict("LINE_AMOUNT").ToString]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_4" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                <iai:IONAPIRequestWizard.Headers>
                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>Accept</x:String>
                                                    </scg:List>
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>application/json</x:String>
                                                    </scg:List>
                                                  </scg:List>
                                                </iai:IONAPIRequestWizard.Headers>
                                                <iai:IONAPIRequestWizard.QueryParameters>
                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                    <scg:List x:TypeArguments="x:String" Capacity="16">
                                                      <x:String>INBN</x:String>
                                                      <x:String>RDTP</x:String>
                                                      <x:String>DIVI</x:String>
                                                      <x:String>NLAM</x:String>
                                                      <x:String>AIT1</x:String>
                                                      <x:String>AIT2</x:String>
                                                      <x:String>AIT3</x:String>
                                                      <x:String>AIT4</x:String>
                                                      <x:String>AIT5</x:String>
                                                      <x:String>AIT6</x:String>
                                                      <x:String>AIT7</x:String>
                                                    </scg:List>
                                                    <scg:List x:TypeArguments="x:String" Capacity="16">
                                                      <x:String>inbnValue</x:String>
                                                      <x:String>8</x:String>
                                                      <x:String>division</x:String>
                                                      <x:String>amt</x:String>
                                                      <x:String>AIT1</x:String>
                                                      <x:String>AIT2</x:String>
                                                      <x:String>AIT3</x:String>
                                                      <x:String>AIT4</x:String>
                                                      <x:String>AIT5</x:String>
                                                      <x:String>AIT6</x:String>
                                                      <x:String>AIT7</x:String>
                                                    </scg:List>
                                                  </scg:List>
                                                </iai:IONAPIRequestWizard.QueryParameters>
                                              </iai:IONAPIRequestWizard>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_72">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_26">
                                                <If.Then>
                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_37">
                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_74">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="x:String" xml:space="preserve">["Error occured while adding the line.  "  +  (out1("results")(0)("errorMessage")).ToString]</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_75">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_5" Line="[commentStatus]" Source="[logfile]" />
                                                  </Sequence>
                                                </If.Then>
                                                <If.Else>
                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_38">
                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_78">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="x:String">["Invoice Line created"]</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_79">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="x:String">["SUCCESS"]</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_7" Line="[commentStatus]" Source="[logfile]" />
                                                  </Sequence>
                                                </If.Else>
                                              </If>
                                            </Sequence>
                                          </ActivityAction>
                                        </ForEach>
                                      </If.Then>
                                      <If.Else>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_43">
                                          <If Condition="[GLCode = &quot;DISTRIBUTED&quot;]" sap2010:WorkflowViewState.IdRef="If_30">
                                            <If.Then>
                                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_8" Line="[commentStatus]" Source="[logfile]" />
                                            </If.Then>
                                            <If.Else>
                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_44">
                                                <If Condition="[ListocrLineValues.Count&gt;0]" DisplayName="If GLCode did not obtain from excep" sap2010:WorkflowViewState.IdRef="If_39">
                                                  <If.Then>
                                                    <If Condition="[ListocrLineValues(0)(&quot;INVOICE_RECEIPT_DATE&quot;).Tostring.Contains(&quot;~&quot;)]" sap2010:WorkflowViewState.IdRef="If_40">
                                                      <If.Else>
                                                        <If Condition="[vendorId &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_48">
                                                          <If.Then>
                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_63">
                                                              <Sequence.Variables>
                                                                <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="vendorResponseDictionary" />
                                                                <Variable x:TypeArguments="x:Int32" Name="vendorResponseCode" />
                                                                <Variable x:TypeArguments="x:String" Name="invoiceType" />
                                                              </Sequence.Variables>
                                                              <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;vendorID&quot;,vendorID},{&quot;company&quot;,company},{&quot;division&quot;,division}}]" ContinueOnError="True" DisplayName="SupplierInfo Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_4" OutputArguments="[vendorResponseDictionary]" ResponseCode="[vendorResponseCode]" WorkflowFile="[projectPath+&quot;\SupplierInfo.xaml&quot;]" />
                                                              <If Condition="[vendorResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_43">
                                                                <If.Then>
                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_58">
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_143">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[invoiceType]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("invoiceType"), String)]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <If Condition="[invoiceType.ToLower.contains(&quot;non po&quot;) OR invoiceType.ToLower.contains(&quot;expense&quot;)]" sap2010:WorkflowViewState.IdRef="If_42">
                                                                      <If.Then>
                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_57">
                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_144">
                                                                            <Assign.To>
                                                                              <OutArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</OutArgument>
                                                                            </Assign.To>
                                                                            <Assign.Value>
                                                                              <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[New List(Of Dictionary(Of String, Object))]</InArgument>
                                                                            </Assign.Value>
                                                                          </Assign>
                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_171">
                                                                            <Assign.To>
                                                                              <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                                            </Assign.To>
                                                                            <Assign.Value>
                                                                              <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary (Of String, Object)]</InArgument>
                                                                            </Assign.Value>
                                                                          </Assign>
                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_146">
                                                                            <Assign.To>
                                                                              <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                            </Assign.To>
                                                                            <Assign.Value>
                                                                              <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("GLCode"), String)]</InArgument>
                                                                            </Assign.Value>
                                                                          </Assign>
                                                                          <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_2">
                                                                            <TryCatch.Try>
                                                                              <If Condition="[invoiceType.ToLower.contains(&quot;non po&quot;) OR invoiceType.ToLower.contains(&quot;expense&quot;)]" sap2010:WorkflowViewState.IdRef="If_41">
                                                                                <If.Then>
                                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_56">
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_172">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:Object">[LinesDict("SUPPLIER_ITEM_CODE")]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_173">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:Object">[LinesDict("Dimensions")]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:Object">[GLCode]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_174">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:Object">[LinesDict("QUANTITY")]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_175">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:Object">[LinesDict("UNIT_PRICE")]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_176">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:Object">[LinesDict("LINE_AMOUNT")]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:Object">[DictOcrValues("SUBTOTAL").Tostring]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <InvokeMethod DisplayName="Assign to ocrValueLines InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_6" MethodName="Add">
                                                                                      <InvokeMethod.TargetObject>
                                                                                        <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</InArgument>
                                                                                      </InvokeMethod.TargetObject>
                                                                                      <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</InArgument>
                                                                                    </InvokeMethod>
                                                                                  </Sequence>
                                                                                </If.Then>
                                                                              </If>
                                                                            </TryCatch.Try>
                                                                            <TryCatch.Catches>
                                                                              <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_2">
                                                                                <ActivityAction x:TypeArguments="s:Exception">
                                                                                  <ActivityAction.Argument>
                                                                                    <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                  </ActivityAction.Argument>
                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_147">
                                                                                    <Assign.To>
                                                                                      <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                    </Assign.To>
                                                                                    <Assign.Value>
                                                                                      <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("GLCode"), String)]</InArgument>
                                                                                    </Assign.Value>
                                                                                  </Assign>
                                                                                </ActivityAction>
                                                                              </Catch>
                                                                            </TryCatch.Catches>
                                                                          </TryCatch>
                                                                        </Sequence>
                                                                      </If.Then>
                                                                    </If>
                                                                  </Sequence>
                                                                </If.Then>
                                                              </If>
                                                              <If Condition="[vendorResponseCode &lt;&gt; 200 OR GLCode = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_47">
                                                                <If.Then>
                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_62">
                                                                    <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;vendorID&quot;,vendorID},{&quot;Division&quot;,division},{&quot;businessRule&quot;,miscValues(&quot;businessRuleForDivisionGLCode&quot;).ToString}}]" ContinueOnError="True" DisplayName="DivisiontoGLCode Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_5" OutputArguments="[vendorResponseDictionary]" ResponseCode="[vendorResponseCode]" WorkflowFile="[projectPath+&quot;\DivisiontoGLCode.xaml&quot;]" />
                                                                    <If Condition="[vendorResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_46">
                                                                      <If.Then>
                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_61">
                                                                          <Sequence.Variables>
                                                                            <Variable x:TypeArguments="x:String" Name="comb" />
                                                                          </Sequence.Variables>
                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_148">
                                                                            <Assign.To>
                                                                              <OutArgument x:TypeArguments="x:String">[comb]</OutArgument>
                                                                            </Assign.To>
                                                                            <Assign.Value>
                                                                              <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("comb"), String)]</InArgument>
                                                                            </Assign.Value>
                                                                          </Assign>
                                                                          <If Condition="[comb &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_45">
                                                                            <If.Then>
                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_60">
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_149">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("AIT1"), String)]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_150">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[invoiceType]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">Expense Invoice</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_151">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[New List(Of Dictionary(Of String, Object))]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_152">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary(Of String, Object)]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_3">
                                                                                  <TryCatch.Try>
                                                                                    <If Condition="[invoiceType.ToLower.contains(&quot;non po&quot;) OR invoiceType.ToLower.contains(&quot;expense&quot;)]" sap2010:WorkflowViewState.IdRef="If_44">
                                                                                      <If.Then>
                                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_59">
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_177">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:Object">[LinesDict("SUPPLIER_ITEM_CODE")]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_178">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:Object">[LinesDict("Dimensions")]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:Object">[comb]</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_179">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:Object">[LinesDict("QUANTITY")]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_180">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:Object">[LinesDict("UNIT_PRICE")]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_181">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:Object">[LinesDict("LINE_AMOUNT")]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:Object">[DictOcrValues("SUBTOTAL").Tostring]</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                          <InvokeMethod DisplayName="Assign to ocrValueLines InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_12" MethodName="Add">
                                                                                            <InvokeMethod.TargetObject>
                                                                                              <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</InArgument>
                                                                                            </InvokeMethod.TargetObject>
                                                                                            <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</InArgument>
                                                                                          </InvokeMethod>
                                                                                        </Sequence>
                                                                                      </If.Then>
                                                                                    </If>
                                                                                  </TryCatch.Try>
                                                                                  <TryCatch.Catches>
                                                                                    <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_3">
                                                                                      <ActivityAction x:TypeArguments="s:Exception">
                                                                                        <ActivityAction.Argument>
                                                                                          <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                        </ActivityAction.Argument>
                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_153">
                                                                                          <Assign.To>
                                                                                            <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                          </Assign.To>
                                                                                          <Assign.Value>
                                                                                            <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("AIT1"), String)]</InArgument>
                                                                                          </Assign.Value>
                                                                                        </Assign>
                                                                                      </ActivityAction>
                                                                                    </Catch>
                                                                                  </TryCatch.Catches>
                                                                                </TryCatch>
                                                                              </Sequence>
                                                                            </If.Then>
                                                                          </If>
                                                                        </Sequence>
                                                                      </If.Then>
                                                                    </If>
                                                                  </Sequence>
                                                                </If.Then>
                                                              </If>
                                                            </Sequence>
                                                          </If.Then>
                                                        </If>
                                                      </If.Else>
                                                    </If>
                                                  </If.Then>
                                                  <If.Else>
                                                    <If Condition="[vendorId &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_56">
                                                      <If.Then>
                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_71">
                                                          <Sequence.Variables>
                                                            <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="vendorResponseDictionary" />
                                                            <Variable x:TypeArguments="x:Int32" Name="vendorResponseCode" />
                                                            <Variable x:TypeArguments="x:String" Name="invoiceType" />
                                                          </Sequence.Variables>
                                                          <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;vendorID&quot;,vendorID},{&quot;company&quot;,company},{&quot;division&quot;,division}}]" ContinueOnError="True" DisplayName="SupplierInfo Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_6" OutputArguments="[vendorResponseDictionary]" ResponseCode="[vendorResponseCode]" WorkflowFile="[projectPath+&quot;\SupplierInfo.xaml&quot;]" />
                                                          <If Condition="[vendorResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_51">
                                                            <If.Then>
                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_66">
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_154">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:String">[invoiceType]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("invoiceType"), String)]</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                                <If Condition="[invoiceType.ToLower.contains(&quot;non po&quot;) OR invoiceType.ToLower.contains(&quot;expense&quot;)]" sap2010:WorkflowViewState.IdRef="If_50">
                                                                  <If.Then>
                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_65">
                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_155">
                                                                        <Assign.To>
                                                                          <OutArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</OutArgument>
                                                                        </Assign.To>
                                                                        <Assign.Value>
                                                                          <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[New List(Of Dictionary(Of String, Object))]</InArgument>
                                                                        </Assign.Value>
                                                                      </Assign>
                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_156">
                                                                        <Assign.To>
                                                                          <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                                        </Assign.To>
                                                                        <Assign.Value>
                                                                          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary(Of String, Object)]</InArgument>
                                                                        </Assign.Value>
                                                                      </Assign>
                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_157">
                                                                        <Assign.To>
                                                                          <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                        </Assign.To>
                                                                        <Assign.Value>
                                                                          <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("GLCode"), String)]</InArgument>
                                                                        </Assign.Value>
                                                                      </Assign>
                                                                      <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_4">
                                                                        <TryCatch.Try>
                                                                          <If Condition="[invoiceType.ToLower.contains(&quot;non po&quot;) OR invoiceType.ToLower.contains(&quot;expense&quot;)]" sap2010:WorkflowViewState.IdRef="If_49">
                                                                            <If.Then>
                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_64">
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_189">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:Object">[LinesDict("SUPPLIER_ITEM_CODE")]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_190">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:Object">[LinesDict("Dimensions")]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:Object">[GLCode]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_191">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:Object">[LinesDict("QUANTITY")]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_192">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:Object">[LinesDict("UNIT_PRICE")]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_193">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:Object">[LinesDict("LINE_AMOUNT")]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:Object">[DictOcrValues("SUBTOTAL").Tostring]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <InvokeMethod DisplayName="Assign to ocrValueLines InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_18" MethodName="Add">
                                                                                  <InvokeMethod.TargetObject>
                                                                                    <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</InArgument>
                                                                                  </InvokeMethod.TargetObject>
                                                                                  <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</InArgument>
                                                                                </InvokeMethod>
                                                                              </Sequence>
                                                                            </If.Then>
                                                                          </If>
                                                                        </TryCatch.Try>
                                                                        <TryCatch.Catches>
                                                                          <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_4">
                                                                            <ActivityAction x:TypeArguments="s:Exception">
                                                                              <ActivityAction.Argument>
                                                                                <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                              </ActivityAction.Argument>
                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_158">
                                                                                <Assign.To>
                                                                                  <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                </Assign.To>
                                                                                <Assign.Value>
                                                                                  <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("GLCode"), String)]</InArgument>
                                                                                </Assign.Value>
                                                                              </Assign>
                                                                            </ActivityAction>
                                                                          </Catch>
                                                                        </TryCatch.Catches>
                                                                      </TryCatch>
                                                                    </Sequence>
                                                                  </If.Then>
                                                                </If>
                                                              </Sequence>
                                                            </If.Then>
                                                          </If>
                                                          <If Condition="[vendorResponseCode &lt;&gt; 200 OR NOT ListOcrLineValues.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_55">
                                                            <If.Then>
                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_70">
                                                                <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;vendorID&quot;,vendorID},{&quot;Division&quot;,division},{&quot;businessRule&quot;,miscValues(&quot;businessRuleForDivisionGLCode&quot;).ToString}}]" ContinueOnError="True" DisplayName="DivisiontoGLCode Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_7" OutputArguments="[vendorResponseDictionary]" ResponseCode="[vendorResponseCode]" WorkflowFile="[projectPath+&quot;\DivisiontoGLCode.xaml&quot;]" />
                                                                <If Condition="[vendorResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_54">
                                                                  <If.Then>
                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_69">
                                                                      <Sequence.Variables>
                                                                        <Variable x:TypeArguments="x:String" Name="comb" />
                                                                      </Sequence.Variables>
                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_159">
                                                                        <Assign.To>
                                                                          <OutArgument x:TypeArguments="x:String">[comb]</OutArgument>
                                                                        </Assign.To>
                                                                        <Assign.Value>
                                                                          <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("comb"), String)]</InArgument>
                                                                        </Assign.Value>
                                                                      </Assign>
                                                                      <If Condition="[comb &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_53">
                                                                        <If.Then>
                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_68">
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_160">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("AIT1"), String)]</InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_161">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="x:String">[invoiceType]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="x:String">Expense Invoice</InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_162">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[New List(Of Dictionary(Of String, Object))]</InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_163">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary(Of String, Object)]</InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                            <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_5">
                                                                              <TryCatch.Try>
                                                                                <If Condition="[invoiceType.ToLower.contains(&quot;non po&quot;) OR invoiceType.ToLower.contains(&quot;expense&quot;)]" sap2010:WorkflowViewState.IdRef="If_52">
                                                                                  <If.Then>
                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_67">
                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_184">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:Object">[LinesDict("SUPPLIER_ITEM_CODE")]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_185">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:Object">[LinesDict("Dimensions")]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:Object">[comb]</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_186">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:Object">[LinesDict("QUANTITY")]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_187">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:Object">[LinesDict("UNIT_PRICE")]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_188">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:Object">[LinesDict("LINE_AMOUNT")]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:Object">[DictOcrValues("SUBTOTAL").Tostring]</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                      <InvokeMethod DisplayName="Assign to ocrValueLines InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_24" MethodName="Add">
                                                                                        <InvokeMethod.TargetObject>
                                                                                          <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</InArgument>
                                                                                        </InvokeMethod.TargetObject>
                                                                                        <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</InArgument>
                                                                                      </InvokeMethod>
                                                                                    </Sequence>
                                                                                  </If.Then>
                                                                                </If>
                                                                              </TryCatch.Try>
                                                                              <TryCatch.Catches>
                                                                                <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_5">
                                                                                  <ActivityAction x:TypeArguments="s:Exception">
                                                                                    <ActivityAction.Argument>
                                                                                      <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                    </ActivityAction.Argument>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_164">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("AIT1"), String)]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                  </ActivityAction>
                                                                                </Catch>
                                                                              </TryCatch.Catches>
                                                                            </TryCatch>
                                                                          </Sequence>
                                                                        </If.Then>
                                                                      </If>
                                                                    </Sequence>
                                                                  </If.Then>
                                                                </If>
                                                              </Sequence>
                                                            </If.Then>
                                                          </If>
                                                        </Sequence>
                                                      </If.Then>
                                                    </If>
                                                  </If.Else>
                                                </If>
                                                <If Condition="[ListocrLineValues.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_35">
                                                  <If.Then>
                                                    <ForEach x:TypeArguments="scg:Dictionary(x:String, x:Object)" DisplayName="ForEach&lt;Dictionary&lt;String,Object&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_11" Values="[ListocrLineValues]">
                                                      <ActivityAction x:TypeArguments="scg:Dictionary(x:String, x:Object)">
                                                        <ActivityAction.Argument>
                                                          <DelegateInArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="item" />
                                                        </ActivityAction.Argument>
                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_52">
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_182">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary(Of String, Object)]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_183">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[item]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_116">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:String">[AIT1]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(0)]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_117">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:String">[AIT2]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(1)]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_118">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:String">[AIT3]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(2)]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_119">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:String">[AIT4]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(3)]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_120">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:String">[AIT5]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(4)]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_121">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:String">[AIT6]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(5)]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_122">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:String">[AIT7]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(6)]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_123">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:String">[amt]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:String">[LinesDict("LINE_AMOUNT").ToString]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_23" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                            <iai:IONAPIRequestWizard.Headers>
                                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                  <x:String>Accept</x:String>
                                                                </scg:List>
                                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                  <x:String>application/json</x:String>
                                                                </scg:List>
                                                              </scg:List>
                                                            </iai:IONAPIRequestWizard.Headers>
                                                            <iai:IONAPIRequestWizard.QueryParameters>
                                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                  <x:String>INBN</x:String>
                                                                  <x:String>RDTP</x:String>
                                                                  <x:String>DIVI</x:String>
                                                                  <x:String>NLAM</x:String>
                                                                  <x:String>AIT1</x:String>
                                                                  <x:String>AIT2</x:String>
                                                                  <x:String>AIT3</x:String>
                                                                  <x:String>AIT4</x:String>
                                                                  <x:String>AIT5</x:String>
                                                                  <x:String>AIT6</x:String>
                                                                  <x:String>AIT7</x:String>
                                                                </scg:List>
                                                                <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                  <x:String>inbnValue</x:String>
                                                                  <x:String>8</x:String>
                                                                  <x:String>division</x:String>
                                                                  <x:String>amt</x:String>
                                                                  <x:String>AIT1</x:String>
                                                                  <x:String>AIT2</x:String>
                                                                  <x:String>AIT3</x:String>
                                                                  <x:String>AIT4</x:String>
                                                                  <x:String>AIT5</x:String>
                                                                  <x:String>AIT6</x:String>
                                                                  <x:String>AIT7</x:String>
                                                                </scg:List>
                                                              </scg:List>
                                                            </iai:IONAPIRequestWizard.QueryParameters>
                                                          </iai:IONAPIRequestWizard>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_124">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_36">
                                                            <If.Then>
                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_50">
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_125">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:String" xml:space="preserve">["Error occured while adding the line.  "  +  (out1("results")(0)("errorMessage")).ToString]</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_126">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_11" Line="[commentStatus]" Source="[logfile]" />
                                                              </Sequence>
                                                            </If.Then>
                                                            <If.Else>
                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_51">
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_127">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:String">["Invoice Line created"]</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_128">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:String">["SUCCESS"]</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_12" Line="[commentStatus]" Source="[logfile]" />
                                                              </Sequence>
                                                            </If.Else>
                                                          </If>
                                                        </Sequence>
                                                      </ActivityAction>
                                                    </ForEach>
                                                  </If.Then>
                                                  <If.Else>
                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_53">
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_129">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[AIT1]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">[GLCode]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_130">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[AIT2]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">
                                                            <Literal x:TypeArguments="x:String" Value="" />
                                                          </InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_131">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[AIT3]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">
                                                            <Literal x:TypeArguments="x:String" Value="" />
                                                          </InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_132">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[AIT4]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">
                                                            <Literal x:TypeArguments="x:String" Value="" />
                                                          </InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_133">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[AIT5]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">
                                                            <Literal x:TypeArguments="x:String" Value="" />
                                                          </InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_134">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[AIT6]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">
                                                            <Literal x:TypeArguments="x:String" Value="" />
                                                          </InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_135">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[AIT7]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">
                                                            <Literal x:TypeArguments="x:String" Value="" />
                                                          </InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_136">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[amt]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">[DictOcrValues("SUBTOTAL").Tostring]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_24" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                        <iai:IONAPIRequestWizard.Headers>
                                                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                            <scg:List x:TypeArguments="x:String" Capacity="4">
                                                              <x:String>Accept</x:String>
                                                            </scg:List>
                                                            <scg:List x:TypeArguments="x:String" Capacity="4">
                                                              <x:String>application/json</x:String>
                                                            </scg:List>
                                                          </scg:List>
                                                        </iai:IONAPIRequestWizard.Headers>
                                                        <iai:IONAPIRequestWizard.QueryParameters>
                                                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                            <scg:List x:TypeArguments="x:String" Capacity="16">
                                                              <x:String>INBN</x:String>
                                                              <x:String>RDTP</x:String>
                                                              <x:String>DIVI</x:String>
                                                              <x:String>NLAM</x:String>
                                                              <x:String>AIT1</x:String>
                                                              <x:String>AIT2</x:String>
                                                              <x:String>AIT3</x:String>
                                                              <x:String>AIT4</x:String>
                                                              <x:String>AIT5</x:String>
                                                              <x:String>AIT6</x:String>
                                                              <x:String>AIT7</x:String>
                                                            </scg:List>
                                                            <scg:List x:TypeArguments="x:String" Capacity="16">
                                                              <x:String>inbnValue</x:String>
                                                              <x:String>8</x:String>
                                                              <x:String>division</x:String>
                                                              <x:String>amt</x:String>
                                                              <x:String>AIT1</x:String>
                                                              <x:String>AIT2</x:String>
                                                              <x:String>AIT3</x:String>
                                                              <x:String>AIT4</x:String>
                                                              <x:String>AIT5</x:String>
                                                              <x:String>AIT6</x:String>
                                                              <x:String>AIT7</x:String>
                                                            </scg:List>
                                                          </scg:List>
                                                        </iai:IONAPIRequestWizard.QueryParameters>
                                                      </iai:IONAPIRequestWizard>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_137">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_37">
                                                        <If.Then>
                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_54">
                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_138">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="x:String" xml:space="preserve">["Error occured while adding the line.  "  +  (out1("results")(0)("errorMessage")).ToString]</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_139">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_13" Line="[commentStatus]" Source="[logfile]" />
                                                          </Sequence>
                                                        </If.Then>
                                                        <If.Else>
                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_55">
                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_140">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="x:String">["Invoice Line created"]</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_141">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="x:String">["SUCCESS"]</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_14" Line="[commentStatus]" Source="[logfile]" />
                                                          </Sequence>
                                                        </If.Else>
                                                      </If>
                                                    </Sequence>
                                                  </If.Else>
                                                </If>
                                              </Sequence>
                                            </If.Else>
                                          </If>
                                        </Sequence>
                                      </If.Else>
                                    </If>
                                    <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_2">
                                      <iad:CommentOut.Activities>
                                        <If Condition="[(DictOcrValues(&quot;VAT_AMOUNT&quot;).Tostring.contains(&quot;,&quot;) AND DictOcrValues(&quot;VAT_PERCENTAGE&quot;).Tostring.contains(&quot;%&quot;)) OR (Cint(DictOcrValues(&quot;VAT_AMOUNT&quot;).Tostring)&lt;&gt;0)]" sap2010:WorkflowViewState.IdRef="If_28">
                                          <If.Then>
                                            <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;vat&quot;,DictOcrValues(&quot;VAT_AMOUNT&quot;).Tostring},{&quot;VatPercentage&quot;,DictOcrValues(&quot;VAT_PERCENTAGE&quot;).Tostring},{&quot;division&quot;,division},{&quot;tenantID&quot;,tenantID},{&quot;inbnValue&quot;,inbnValue},{&quot;CountryCode&quot;,countryCode},{&quot;AccountStartsWith&quot;,AIT1.substring(0,1)},{&quot;logfile&quot;,logfile},{&quot;invoiceType&quot;,&quot;nonpo&quot;}}]" ContinueOnError="True" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_10" WorkflowFile="[projectPath+&quot;\vatConfiguration.xaml&quot;]" />
                                          </If.Then>
                                          <If.Else>
                                            <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_1">
                                              <iad:CommentOut.Activities>
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_95">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[vat]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String">[DictOcrValues("VAT_AMOUNT").Tostring]</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_142">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[vatCode]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String">[miscValues("vatCodeConfig").ToString]</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                                <If Condition="[miscValues(&quot;vatCodeConfig&quot;).ToString = &quot;NA&quot;]" sap2010:WorkflowViewState.IdRef="If_38">
                                                  <If.Then>
                                                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_20" Response="[respout1]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                      <iai:IONAPIRequestWizard.Headers>
                                                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                                            <x:String>Accept</x:String>
                                                          </scg:List>
                                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                                            <x:String>application/json</x:String>
                                                          </scg:List>
                                                        </scg:List>
                                                      </iai:IONAPIRequestWizard.Headers>
                                                      <iai:IONAPIRequestWizard.QueryParameters>
                                                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                                            <x:String>INBN</x:String>
                                                            <x:String>RDTP</x:String>
                                                            <x:String>DIVI</x:String>
                                                            <x:String>GLAM</x:String>
                                                          </scg:List>
                                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                                            <x:String>inbnValue</x:String>
                                                            <x:String>3</x:String>
                                                            <x:String>division</x:String>
                                                            <x:String>vat</x:String>
                                                          </scg:List>
                                                        </scg:List>
                                                      </iai:IONAPIRequestWizard.QueryParameters>
                                                    </iai:IONAPIRequestWizard>
                                                  </If.Then>
                                                  <If.Else>
                                                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_25" Response="[respout1]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                      <iai:IONAPIRequestWizard.Headers>
                                                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                                            <x:String>Accept</x:String>
                                                          </scg:List>
                                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                                            <x:String>application/json</x:String>
                                                          </scg:List>
                                                        </scg:List>
                                                      </iai:IONAPIRequestWizard.Headers>
                                                      <iai:IONAPIRequestWizard.QueryParameters>
                                                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                          <scg:List x:TypeArguments="x:String" Capacity="8">
                                                            <x:String>INBN</x:String>
                                                            <x:String>RDTP</x:String>
                                                            <x:String>DIVI</x:String>
                                                            <x:String>VTA1</x:String>
                                                            <x:String>VTCD</x:String>
                                                          </scg:List>
                                                          <scg:List x:TypeArguments="x:String" Capacity="8">
                                                            <x:String>inbnValue</x:String>
                                                            <x:String>3</x:String>
                                                            <x:String>division</x:String>
                                                            <x:String>vat</x:String>
                                                            <x:String>vatCode</x:String>
                                                          </scg:List>
                                                        </scg:List>
                                                      </iai:IONAPIRequestWizard.QueryParameters>
                                                    </iai:IONAPIRequestWizard>
                                                  </If.Else>
                                                </If>
                                              </iad:CommentOut.Activities>
                                            </iad:CommentOut>
                                          </If.Else>
                                        </If>
                                      </iad:CommentOut.Activities>
                                    </iad:CommentOut>
                                    <If Condition="[DictOcrValues(&quot;VAT_PERCENTAGE&quot;).Tostring.contains(&quot;%&quot;)]" DisplayName="If Updated" sap2010:WorkflowViewState.IdRef="If_64">
                                      <If.Then>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_74">
                                          <Sequence.Variables>
                                            <Variable x:TypeArguments="iru:ResponseObject" Name="respout1" />
                                            <Variable x:TypeArguments="x:String" Name="vatCode" />
                                          </Sequence.Variables>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_197">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[vat]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[DictOcrValues("VAT_AMOUNT").Tostring]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_3">
                                            <iad:CommentOut.Activities>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_198">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[vatCode]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[miscValues("vatCodeConfig").ToString]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <If Condition="[miscValues(&quot;vatCodeConfig&quot;).ToString = &quot;NA&quot;]" sap2010:WorkflowViewState.IdRef="If_62">
                                                <If.Then>
                                                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_26" Response="[respout1]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                    <iai:IONAPIRequestWizard.Headers>
                                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                                          <x:String>Accept</x:String>
                                                        </scg:List>
                                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                                          <x:String>application/json</x:String>
                                                        </scg:List>
                                                      </scg:List>
                                                    </iai:IONAPIRequestWizard.Headers>
                                                    <iai:IONAPIRequestWizard.QueryParameters>
                                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                                          <x:String>INBN</x:String>
                                                          <x:String>RDTP</x:String>
                                                          <x:String>DIVI</x:String>
                                                          <x:String>GLAM</x:String>
                                                        </scg:List>
                                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                                          <x:String>inbnValue</x:String>
                                                          <x:String>3</x:String>
                                                          <x:String>division</x:String>
                                                          <x:String>vat</x:String>
                                                        </scg:List>
                                                      </scg:List>
                                                    </iai:IONAPIRequestWizard.QueryParameters>
                                                  </iai:IONAPIRequestWizard>
                                                </If.Then>
                                                <If.Else>
                                                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_27" Response="[respout1]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                    <iai:IONAPIRequestWizard.Headers>
                                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                                          <x:String>Accept</x:String>
                                                        </scg:List>
                                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                                          <x:String>application/json</x:String>
                                                        </scg:List>
                                                      </scg:List>
                                                    </iai:IONAPIRequestWizard.Headers>
                                                    <iai:IONAPIRequestWizard.QueryParameters>
                                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                        <scg:List x:TypeArguments="x:String" Capacity="8">
                                                          <x:String>INBN</x:String>
                                                          <x:String>RDTP</x:String>
                                                          <x:String>DIVI</x:String>
                                                          <x:String>VTA1</x:String>
                                                          <x:String>VTCD</x:String>
                                                        </scg:List>
                                                        <scg:List x:TypeArguments="x:String" Capacity="8">
                                                          <x:String>inbnValue</x:String>
                                                          <x:String>3</x:String>
                                                          <x:String>division</x:String>
                                                          <x:String>vat</x:String>
                                                          <x:String>vatCode</x:String>
                                                        </scg:List>
                                                      </scg:List>
                                                    </iai:IONAPIRequestWizard.QueryParameters>
                                                  </iai:IONAPIRequestWizard>
                                                </If.Else>
                                              </If>
                                            </iad:CommentOut.Activities>
                                          </iad:CommentOut>
                                          <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;vat&quot;,DictOcrValues(&quot;VAT_AMOUNT&quot;).Tostring},{&quot;VatPercentage&quot;,DictOcrValues(&quot;VAT_PERCENTAGE&quot;).Tostring},{&quot;division&quot;,division},{&quot;tenantID&quot;,tenantID},{&quot;inbnValue&quot;,inbnValue},{&quot;CountryCode&quot;,countryCode},{&quot;AccountStartsWith&quot;,&quot;&quot;},{&quot;logfile&quot;,logfile},{&quot;invoiceType&quot;,&quot;po&quot;}}]" ContinueOnError="True" DisplayName="Invoke vatConfiguration" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_11" WorkflowFile="[projectPath+&quot;\vatConfiguration.xaml&quot;]" />
                                        </Sequence>
                                      </If.Then>
                                      <If.Else>
                                        <If Condition="[(Convert.ToInt32(DictOcrValues(&quot;VAT_AMOUNT&quot;).Tostring)&lt;&gt;0)]" sap2010:WorkflowViewState.IdRef="If_63">
                                          <If.Then>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_75">
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_199">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[vat]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[DictOcrValues("VAT_AMOUNT").Tostring]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;vat&quot;,DictOcrValues(&quot;VAT_AMOUNT&quot;).Tostring},{&quot;VatPercentage&quot;,DictOcrValues(&quot;VAT_PERCENTAGE&quot;).Tostring},{&quot;division&quot;,division},{&quot;tenantID&quot;,tenantID},{&quot;inbnValue&quot;,inbnValue},{&quot;CountryCode&quot;,countryCode},{&quot;AccountStartsWith&quot;,&quot;&quot;},{&quot;logfile&quot;,logfile},{&quot;invoiceType&quot;,&quot;po&quot;}}]" ContinueOnError="True" DisplayName="Invoke vatConfiguration" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_12" WorkflowFile="[projectPath+&quot;\vatConfiguration.xaml&quot;]" />
                                            </Sequence>
                                          </If.Then>
                                        </If>
                                      </If.Else>
                                    </If>
                                  </Sequence>
                                </If.Then>
                              </If>
                              <If Condition="[inbnValue = &quot;&quot;]" DisplayName="If validation" sap2010:WorkflowViewState.IdRef="If_15">
                                <If.Then>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_52">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </If.Then>
                                <If.Else>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_25">
                                    <Sequence.Variables>
                                      <Variable x:TypeArguments="x:String" Name="SuccessMoved" />
                                      <Variable x:TypeArguments="x:Int32" Name="respout1" />
                                    </Sequence.Variables>
                                    <If Condition="[approvalRequired]" sap2010:WorkflowViewState.IdRef="If_18">
                                      <If.Then>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_28">
                                          <Sequence.Variables>
                                            <Variable x:TypeArguments="x:Boolean" Name="approvalRequest" />
                                            <Variable x:TypeArguments="x:Int32" Name="sendApprovalResponseCode" />
                                            <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="checkApprovalResponseDictionary" />
                                            <Variable x:TypeArguments="x:Int32" Name="checkApprovalResponseCode" />
                                            <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="sendApprovalResponseDictionary" />
                                          </Sequence.Variables>
                                          <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;amount&quot;,DictOcrValues(&quot;TOTAL&quot;).Tostring},{&quot;tenantID&quot;,tenantID},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule}}]" ContinueOnError="True" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_1" OutputArguments="[checkApprovalResponseDictionary]" ResponseCode="[checkApprovalResponseCode]" WorkflowFile="[projectPath+&quot;\CheckApproval.xaml&quot;]" />
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_57">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:Boolean">[approvalRequest]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:Boolean">[CType(checkApprovalResponseDictionary("approvalRequest"), Boolean)]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <If Condition="[approvalRequest]" sap2010:WorkflowViewState.IdRef="If_17">
                                            <If.Then>
                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_27">
                                                <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;amount&quot;,DictOcrValues(&quot;TOTAL&quot;).Tostring},{&quot;tenantID&quot;,tenantID},{&quot;division&quot;,DIVISION},{&quot;inbn&quot;,inbnValue},{&quot;invoiceNumber&quot;,DictOcrValues(&quot;INVOICE_RECEIPT_ID&quot;).Tostring},{&quot;poNumber&quot;,DictOcrValues(&quot;PO_NUMBER&quot;).Tostring},{&quot;vendorName&quot;,vendorId},{&quot;approvalWorkflow&quot;,approvalWorkflow}}]" ContinueOnError="True" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_2" OutputArguments="[sendApprovalResponseDictionary]" ResponseCode="[sendApprovalResponseCode]" WorkflowFile="[projectPath+&quot;\SendtoApprovalWorkflow.xaml&quot;]" />
                                                <If Condition="[sendApprovalResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_16">
                                                  <If.Then>
                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_26">
                                                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_4" Line="Supllier invoice is sent for approval since the invoice amount is large" Source="[logfile]" />
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_58">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">["Invoice created and Sent for approval"]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                    </Sequence>
                                                  </If.Then>
                                                </If>
                                              </Sequence>
                                            </If.Then>
                                            <If.Else>
                                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" Response="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_9" StatusCode="[respout1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS455MI/ValidByBatchNo&quot;]">
                                                <iai:IONAPIRequestWizard.Headers>
                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>Accept</x:String>
                                                    </scg:List>
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>application/json</x:String>
                                                    </scg:List>
                                                  </scg:List>
                                                </iai:IONAPIRequestWizard.Headers>
                                                <iai:IONAPIRequestWizard.QueryParameters>
                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>INBN</x:String>
                                                      <x:String>DIVI</x:String>
                                                    </scg:List>
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>inbnValue</x:String>
                                                      <x:String>division</x:String>
                                                    </scg:List>
                                                  </scg:List>
                                                </iai:IONAPIRequestWizard.QueryParameters>
                                              </iai:IONAPIRequestWizard>
                                            </If.Else>
                                          </If>
                                        </Sequence>
                                      </If.Then>
                                      <If.Else>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_42">
                                          <If Condition="[miscValues(&quot;AutomateValidation&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_34">
                                            <If.Then>
                                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_10" Response="[respObj2]" StatusCode="[respout1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS455MI/ValidByBatchNo&quot;]">
                                                <iai:IONAPIRequestWizard.Headers>
                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>Accept</x:String>
                                                    </scg:List>
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>application/json</x:String>
                                                    </scg:List>
                                                  </scg:List>
                                                </iai:IONAPIRequestWizard.Headers>
                                                <iai:IONAPIRequestWizard.QueryParameters>
                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>INBN</x:String>
                                                      <x:String>DIVI</x:String>
                                                    </scg:List>
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>inbnValue</x:String>
                                                      <x:String>division</x:String>
                                                    </scg:List>
                                                  </scg:List>
                                                </iai:IONAPIRequestWizard.QueryParameters>
                                              </iai:IONAPIRequestWizard>
                                            </If.Then>
                                          </If>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_96">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">SUCCESS</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                        </Sequence>
                                      </If.Else>
                                    </If>
                                    <If Condition="[respOut1 = 200]" sap2010:WorkflowViewState.IdRef="If_19">
                                      <If.Then>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_59">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">["Invoice created and validated"]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                      </If.Then>
                                    </If>
                                  </Sequence>
                                </If.Else>
                              </If>
                            </Sequence>
                          </If.Then>
                          <If.Else>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_11">
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_19">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">["Received an IONAPI error while adding Invoice header."]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_20">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_2" Line="[commentStatus]" Source="[logfile]" />
                            </Sequence>
                          </If.Else>
                        </If>
                      </Sequence>
                    </Sequence>
                  </If.Then>
                  <If.Else>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_23">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_49">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[(out0("results")(0)("errorMessage")).ToString]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_50">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_3" Line="[commentStatus]" Source="[logfile]" />
                    </Sequence>
                  </If.Else>
                </If>
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_3">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_2">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching Vendor name for the Vendor Id " + vendorId+"."]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_3">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="[commentStatus]" Source="[logfile]" />
              </Sequence>
            </If.Else>
          </If>
        </Sequence>
      </If.Then>
      <If.Else>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_29">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_67">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["Invoice Number " + DictOcrValues("INVOICE_RECEIPT_ID").Tostring + " already exists."]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_68">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
            </Assign.Value>
          </Assign>
        </Sequence>
      </If.Else>
    </If>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_166" sap:VirtualizedContainerService.HintSize="2154.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_38" sap:VirtualizedContainerService.HintSize="2154.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_112" sap:VirtualizedContainerService.HintSize="680,62" />
      <sap2010:ViewStateData Id="Assign_113" sap:VirtualizedContainerService.HintSize="680,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_22" sap:VirtualizedContainerService.HintSize="680,22" />
      <sap2010:ViewStateData Id="Assign_114" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_47" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_115" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_48" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_32" sap:VirtualizedContainerService.HintSize="554,340" />
      <sap2010:ViewStateData Id="If_33" sap:VirtualizedContainerService.HintSize="680,494" />
      <sap2010:ViewStateData Id="Sequence_49" sap:VirtualizedContainerService.HintSize="2154.66666666667,884">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_45" sap:VirtualizedContainerService.HintSize="2155,60" />
      <sap2010:ViewStateData Id="Assign_65" sap:VirtualizedContainerService.HintSize="2154.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_165" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_57" sap:VirtualizedContainerService.HintSize="2154.66666666667,214" />
      <sap2010:ViewStateData Id="InvokeWorkflow_3" sap:VirtualizedContainerService.HintSize="2154.66666666667,22" />
      <sap2010:ViewStateData Id="Assign_66" sap:VirtualizedContainerService.HintSize="2154.66666666667,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_2" sap:VirtualizedContainerService.HintSize="1622,22" />
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="1310,62" />
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="976,62" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="976,62" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="976,62" />
      <sap2010:ViewStateData Id="Assign_196" sap:VirtualizedContainerService.HintSize="976,62" />
      <sap2010:ViewStateData Id="Assign_200" sap:VirtualizedContainerService.HintSize="976,62" />
      <sap2010:ViewStateData Id="Assign_7" sap:VirtualizedContainerService.HintSize="976,62" />
      <sap2010:ViewStateData Id="Assign_51" sap:VirtualizedContainerService.HintSize="976,62" />
      <sap2010:ViewStateData Id="Assign_37" sap:VirtualizedContainerService.HintSize="976,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_8" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_168" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_59" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="Assign_169" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Sequence_72" sap:VirtualizedContainerService.HintSize="486,504">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_11" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_73" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_60" sap:VirtualizedContainerService.HintSize="464,340" />
      <sap2010:ViewStateData Id="If_61" sap:VirtualizedContainerService.HintSize="976,658" />
      <sap2010:ViewStateData Id="Sequence_4" sap:VirtualizedContainerService.HintSize="998,1598">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_43" sap:VirtualizedContainerService.HintSize="702,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_6" sap:VirtualizedContainerService.HintSize="702,22" />
      <sap2010:ViewStateData Id="Assign_39" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_40" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_19" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_41" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_20" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_10" sap:VirtualizedContainerService.HintSize="554,340" />
      <sap2010:ViewStateData Id="Sequence_21" sap:VirtualizedContainerService.HintSize="576,566">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_11" sap:VirtualizedContainerService.HintSize="702,720" />
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="702,62" />
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="702,62" />
      <sap2010:ViewStateData Id="Assign_64" sap:VirtualizedContainerService.HintSize="702,62" />
      <sap2010:ViewStateData Id="Assign_70" sap:VirtualizedContainerService.HintSize="702,62" />
      <sap2010:ViewStateData Id="Assign_110" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_111" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_1" sap:VirtualizedContainerService.HintSize="404.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_1" sap:VirtualizedContainerService.HintSize="702,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_201" sap:VirtualizedContainerService.HintSize="702,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_40" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_41" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_72" sap:VirtualizedContainerService.HintSize="464,214" />
      <sap2010:ViewStateData Id="Sequence_84" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_42" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_43" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_73" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Sequence_85" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_44" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_45" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_74" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Sequence_86" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_46" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_47" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_75" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Sequence_87" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_1" sap:VirtualizedContainerService.HintSize="702,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_28" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_36" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_65" sap:VirtualizedContainerService.HintSize="464,214" />
      <sap2010:ViewStateData Id="Sequence_77" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_30" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_37" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_66" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Sequence_79" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_67" sap:VirtualizedContainerService.HintSize="464,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_32" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_38" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_69" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Sequence_81" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_34" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_39" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_70" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Sequence_83" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_71" sap:VirtualizedContainerService.HintSize="464,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_68" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_4" sap:VirtualizedContainerService.HintSize="702,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_12" sap:VirtualizedContainerService.HintSize="1535,60" />
      <sap2010:ViewStateData Id="Assign_13" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_14" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_15" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_8" sap:VirtualizedContainerService.HintSize="264,384">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_16" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_17" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_18" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_6" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_9" sap:VirtualizedContainerService.HintSize="264,446">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="1535,594" />
      <sap2010:ViewStateData Id="Assign_194" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_195" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_88" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_90" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_91" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_92" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_89" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_93" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_94" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_28" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_4" sap:VirtualizedContainerService.HintSize="553,22" />
      <sap2010:ViewStateData Id="Assign_72" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_74" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_75" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_5" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_37" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_78" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_79" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_7" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_38" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_26" sap:VirtualizedContainerService.HintSize="553,494">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_40" sap:VirtualizedContainerService.HintSize="575,1780">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_12" sap:VirtualizedContainerService.HintSize="605,1928" />
      <sap2010:ViewStateData Id="Append_Line_8" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_4" sap:VirtualizedContainerService.HintSize="926.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_143" sap:VirtualizedContainerService.HintSize="566.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_144" sap:VirtualizedContainerService.HintSize="418.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_171" sap:VirtualizedContainerService.HintSize="418.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_146" sap:VirtualizedContainerService.HintSize="418.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_172" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_173" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_174" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_175" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_176" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_6" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Sequence_56" sap:VirtualizedContainerService.HintSize="264,768">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_41" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_147" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_2" sap:VirtualizedContainerService.HintSize="404.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_2" sap:VirtualizedContainerService.HintSize="418.666666666667,298" />
      <sap2010:ViewStateData Id="Sequence_57" sap:VirtualizedContainerService.HintSize="440.666666666667,728">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_42" sap:VirtualizedContainerService.HintSize="566.666666666667,882">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_58" sap:VirtualizedContainerService.HintSize="588.666666666667,1108">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_43" sap:VirtualizedContainerService.HintSize="926.666666666667,1262" />
      <sap2010:ViewStateData Id="InvokeWorkflow_5" sap:VirtualizedContainerService.HintSize="778.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_148" sap:VirtualizedContainerService.HintSize="630.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_149" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_150" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_151" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_152" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_177" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_178" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_179" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_180" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_181" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_12" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Sequence_59" sap:VirtualizedContainerService.HintSize="264,768">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_44" sap:VirtualizedContainerService.HintSize="464,922">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_153" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_3" sap:VirtualizedContainerService.HintSize="468.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_3" sap:VirtualizedContainerService.HintSize="482.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_60" sap:VirtualizedContainerService.HintSize="504.666666666667,584.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_45" sap:VirtualizedContainerService.HintSize="630.666666666667,738.666666666667" />
      <sap2010:ViewStateData Id="Sequence_61" sap:VirtualizedContainerService.HintSize="652.666666666667,964.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_46" sap:VirtualizedContainerService.HintSize="778.666666666667,1118.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_62" sap:VirtualizedContainerService.HintSize="800.666666666667,1304.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_47" sap:VirtualizedContainerService.HintSize="926.666666666667,1458.66666666667" />
      <sap2010:ViewStateData Id="Sequence_63" sap:VirtualizedContainerService.HintSize="948.666666666667,2946.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_48" sap:VirtualizedContainerService.HintSize="1074.66666666667,3100.66666666667" />
      <sap2010:ViewStateData Id="If_40" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_6" sap:VirtualizedContainerService.HintSize="760,22" />
      <sap2010:ViewStateData Id="Assign_154" sap:VirtualizedContainerService.HintSize="630.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_155" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_156" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_157" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_189" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_190" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_191" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_192" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_193" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_18" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Sequence_64" sap:VirtualizedContainerService.HintSize="264,768">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_49" sap:VirtualizedContainerService.HintSize="464,922">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_158" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_4" sap:VirtualizedContainerService.HintSize="468.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_4" sap:VirtualizedContainerService.HintSize="482.666666666667,1160" />
      <sap2010:ViewStateData Id="Sequence_65" sap:VirtualizedContainerService.HintSize="504.666666666667,1590">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_50" sap:VirtualizedContainerService.HintSize="630.666666666667,1744">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_66" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_51" sap:VirtualizedContainerService.HintSize="760,214" />
      <sap2010:ViewStateData Id="InvokeWorkflow_7" sap:VirtualizedContainerService.HintSize="612,22" />
      <sap2010:ViewStateData Id="Assign_159" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_160" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_161" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_162" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_163" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_184" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_185" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_186" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_187" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_188" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_24" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Sequence_67" sap:VirtualizedContainerService.HintSize="264,768">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_52" sap:VirtualizedContainerService.HintSize="464,922">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_164" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_5" sap:VirtualizedContainerService.HintSize="468.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_5" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_68" sap:VirtualizedContainerService.HintSize="264,584.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_53" sap:VirtualizedContainerService.HintSize="464,738.666666666667" />
      <sap2010:ViewStateData Id="Sequence_69" sap:VirtualizedContainerService.HintSize="486,964.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_54" sap:VirtualizedContainerService.HintSize="612,1118.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_70" sap:VirtualizedContainerService.HintSize="634,1304.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_55" sap:VirtualizedContainerService.HintSize="760,1458.66666666667" />
      <sap2010:ViewStateData Id="Sequence_71" sap:VirtualizedContainerService.HintSize="782,1898.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_56" sap:VirtualizedContainerService.HintSize="908,2052.66666666667" />
      <sap2010:ViewStateData Id="If_39" sap:VirtualizedContainerService.HintSize="1134,2206.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_182" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_183" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_116" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_117" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_118" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_119" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_120" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_121" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_122" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_123" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_23" sap:VirtualizedContainerService.HintSize="554,22" />
      <sap2010:ViewStateData Id="Assign_124" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_125" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_126" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_11" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_50" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_127" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_128" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_12" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_51" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_36" sap:VirtualizedContainerService.HintSize="554,504" />
      <sap2010:ViewStateData Id="Sequence_52" sap:VirtualizedContainerService.HintSize="576,1812">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_11" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_129" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_130" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_131" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_132" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_133" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_134" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_135" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_136" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_24" sap:VirtualizedContainerService.HintSize="554,22" />
      <sap2010:ViewStateData Id="Assign_137" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_138" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_139" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_13" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_54" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_140" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_141" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_14" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_55" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_37" sap:VirtualizedContainerService.HintSize="554,504" />
      <sap2010:ViewStateData Id="Sequence_53" sap:VirtualizedContainerService.HintSize="576,1608">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_35" sap:VirtualizedContainerService.HintSize="1134,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_44" sap:VirtualizedContainerService.HintSize="1156,2423.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_30" sap:VirtualizedContainerService.HintSize="1382,2577.33333333333" />
      <sap2010:ViewStateData Id="Sequence_43" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_29" sap:VirtualizedContainerService.HintSize="830,2076" />
      <sap2010:ViewStateData Id="InvokeWorkflow_10" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Assign_95" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_142" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_20" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_25" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_38" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_1" sap:VirtualizedContainerService.HintSize="192,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_28" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_2" sap:VirtualizedContainerService.HintSize="830,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_197" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_198" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_26" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_27" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_62" sap:VirtualizedContainerService.HintSize="464,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_3" sap:VirtualizedContainerService.HintSize="242,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_11" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_74" sap:VirtualizedContainerService.HintSize="264,344">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_199" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_12" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_75" sap:VirtualizedContainerService.HintSize="264,246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_63" sap:VirtualizedContainerService.HintSize="464,394">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_64" sap:VirtualizedContainerService.HintSize="830,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_12" sap:VirtualizedContainerService.HintSize="852,2389">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_6" sap:VirtualizedContainerService.HintSize="1535,2537" />
      <sap2010:ViewStateData Id="Assign_52" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_1" sap:VirtualizedContainerService.HintSize="711,22" />
      <sap2010:ViewStateData Id="Assign_57" sap:VirtualizedContainerService.HintSize="711,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_2" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Append_Line_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_58" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_26" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_16" sap:VirtualizedContainerService.HintSize="464,402" />
      <sap2010:ViewStateData Id="Sequence_27" sap:VirtualizedContainerService.HintSize="486,588">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_9" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_17" sap:VirtualizedContainerService.HintSize="711,736" />
      <sap2010:ViewStateData Id="Sequence_28" sap:VirtualizedContainerService.HintSize="733,1022">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_10" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_34" sap:VirtualizedContainerService.HintSize="464,214" />
      <sap2010:ViewStateData Id="Assign_96" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Sequence_42" sap:VirtualizedContainerService.HintSize="486,440">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_18" sap:VirtualizedContainerService.HintSize="1246,1170">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_59" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_19" sap:VirtualizedContainerService.HintSize="1246,216" />
      <sap2010:ViewStateData Id="Sequence_25" sap:VirtualizedContainerService.HintSize="1268,1550">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_15" sap:VirtualizedContainerService.HintSize="1535,1698" />
      <sap2010:ViewStateData Id="Sequence_10" sap:VirtualizedContainerService.HintSize="1557,5133">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_19" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_20" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_11" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_5" sap:VirtualizedContainerService.HintSize="702,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="998,1894">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_6" sap:VirtualizedContainerService.HintSize="1020,3656">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_49" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_50" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_23" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="1310,3810" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="1332,4036">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="1622,4190">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_30" sap:VirtualizedContainerService.HintSize="1644,4376">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_67" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_68" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_29" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_20" sap:VirtualizedContainerService.HintSize="2154.66666666667,4530" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="2176.66666666667,6402">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="2216.66666666667,6482" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>