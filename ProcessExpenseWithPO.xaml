﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="vendorName" Type="OutArgument(x:String)" />
    <x:Property Name="company" Type="OutArgument(x:String)" />
    <x:Property Name="division" Type="OutArgument(x:String)" />
    <x:Property Name="authUser" Type="InArgument(x:String)" />
    <x:Property Name="commentStatus" Type="OutArgument(x:String)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="colemanAPI" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
    <x:Property Name="status" Type="OutArgument(x:String)" />
    <x:Property Name="vendorId" Type="OutArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="correlationID" Type="OutArgument(x:String)" />
    <x:Property Name="DictOcrValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.Activities.Workflow</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Net.Http</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.Activities.Workflow</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.Net.Http</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_53">
    <Sequence.Variables>
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj1" />
      <Variable x:TypeArguments="njl:JToken" Name="out1" />
      <Variable x:TypeArguments="x:String" Name="req1" />
      <Variable x:TypeArguments="x:String" Name="pono" />
      <Variable x:TypeArguments="x:Int32" Name="count1" />
      <Variable x:TypeArguments="x:String" Name="req2" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj2" />
      <Variable x:TypeArguments="njl:JToken" Name="out2" />
      <Variable x:TypeArguments="scg:List(s:String[])" Name="M3TotalTableRows" />
      <Variable x:TypeArguments="x:Int32" Name="i" />
      <Variable x:TypeArguments="s:String[]" Name="M3Values" />
      <Variable x:TypeArguments="x:String" Name="cono" />
      <Variable x:TypeArguments="x:String" Name="cucd" />
      <Variable x:TypeArguments="x:String" Name="SupplierNo" />
      <Variable x:TypeArguments="x:String" Name="APIString" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode1" />
      <Variable x:TypeArguments="x:String" Name="pyme" />
      <Variable x:TypeArguments="njl:JToken" Name="out5" />
    </Sequence.Variables>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_1">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[pono]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[DictOcrValues("PO_NUMBER").Tostring]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_2">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">
          <Literal x:TypeArguments="x:String" Value="" />
        </InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_3">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[vendorName]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">
          <Literal x:TypeArguments="x:String" Value="" />
        </InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_4">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[company]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">
          <Literal x:TypeArguments="x:String" Value="" />
        </InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_5">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[division]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">
          <Literal x:TypeArguments="x:String" Value="" />
        </InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_6">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[req1]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">["IACONO,IADIVI,IASUNO,IACUCD,IATEPY,IAPYME from MPHEAD where IAPUNO = " + pono]</InArgument>
      </Assign.Value>
    </Assign>
    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="ExportMI for comp, div IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_1" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/EXPORTMI/Select?SEPC=~&amp;HDRS=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
      <iai:IONAPIRequestWizard.QueryParameters>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>QERY</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>req1</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.QueryParameters>
    </iai:IONAPIRequestWizard>
    <If Condition="[StatusCode1 = 200]" DisplayName="If" sap2010:WorkflowViewState.IdRef="If_40">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_51">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_7">
            <Assign.To>
              <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj1.ReadAsText)]</InArgument>
            </Assign.Value>
          </Assign>
          <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_39">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_1">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_8">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">PONOTAVAILABLE</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_9">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["PO number is not available"]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="[commentStatus]" Source="[logFile]" />
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_50">
                <Sequence.Variables>
                  <Variable x:TypeArguments="njl:JToken" Name="httpOut" />
                  <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="ColemanOutPut" />
                  <Variable x:TypeArguments="x:Int32" Name="ColemanRespCode" />
                  <Variable x:TypeArguments="x:String" Name="inbnValue" />
                  <Variable x:TypeArguments="x:Int32" Name="StatusCode2" />
                  <Variable x:TypeArguments="x:String" Name="tepy" />
                  <Variable x:TypeArguments="x:Int32" Name="StatusCode3" />
                  <Variable x:TypeArguments="iru:ResponseObject" Name="RespObj3" />
                  <Variable x:TypeArguments="njl:JToken" Name="out3" />
                </Sequence.Variables>
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_2" Line="Extracted the company and division." Source="[logfile]" />
                <Assign sap2010:WorkflowViewState.IdRef="Assign_10">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[division]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(1).ToString()]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_11">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[company]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(0).ToString()]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_12">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(2).ToString()]</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="False" sap2010:WorkflowViewState.IdRef="If_2">
                  <If.Then>
                    <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;)(0)(&quot;REPL&quot;).ToString().split(&quot;~&quot;C)(4).ToString().Length = 2]" sap2010:WorkflowViewState.IdRef="If_1">
                      <If.Then>
                        <Assign DisplayName="Assign tepy" sap2010:WorkflowViewState.IdRef="Assign_13">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[tepy]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">["0" + Convert.ToInt32(out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(4).ToString(),10).ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                      </If.Then>
                      <If.Else>
                        <Assign DisplayName="Assign tepy" sap2010:WorkflowViewState.IdRef="Assign_14">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[tepy]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(4).ToString()]</InArgument>
                          </Assign.Value>
                        </Assign>
                      </If.Else>
                    </If>
                  </If.Then>
                </If>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_15">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[tepy]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(4).ToString()]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_16">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[pyme]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(5).ToString()]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_17">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[cucd]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(3).ToString()]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_18">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["F2PNLI,F2REPN,F2SCOC,F2RPQA,F2TRDT,F2ITNO,F2CONO,F2CUCD,F2DIVI,F2SUNO,F2IVQA,F2PUNO,F2SUDO,F2RCAC,F2RPQT,F2SCOP,F2SERA,F2IVQT,F2ICAC from FGRECL00 where F2DIVI = " + DIVISION+" AND F2PUNO = " + pono + " and F2IMST != 9 "]</InArgument>
                  </Assign.Value>
                </Assign>
                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for vendor name" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_2" Response="[respObj3]" StatusCode="[StatusCode3]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/CRS620MI/GetBasicData&quot;]">
                  <iai:IONAPIRequestWizard.Headers>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="0" />
                      <scg:List x:TypeArguments="x:String" Capacity="0" />
                    </scg:List>
                  </iai:IONAPIRequestWizard.Headers>
                  <iai:IONAPIRequestWizard.QueryParameters>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="8">
                        <x:String>extendedresult</x:String>
                        <x:String>format</x:String>
                        <x:String>righttrim</x:String>
                        <x:String>excludeempty</x:String>
                        <x:String>dateformat</x:String>
                        <x:String>SUNO</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="8">
                        <x:String>false</x:String>
                        <x:String>PRETTY</x:String>
                        <x:String>true</x:String>
                        <x:String>false</x:String>
                        <x:String>YMD8</x:String>
                        <x:String>vendorId</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.QueryParameters>
                </iai:IONAPIRequestWizard>
                <If Condition="[StatusCode3 = 200]" sap2010:WorkflowViewState.IdRef="If_5">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_5">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_19">
                        <Assign.To>
                          <OutArgument x:TypeArguments="njl:JToken">[out3]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj3.ReadAsText)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <If Condition="[out3(&quot;results&quot;)(0)(&quot;records&quot;).ToString &lt;&gt; &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_4">
                        <If.Then>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_3">
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_20">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[vendorName]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[out3("results")(0)("records")(0)("SUNM").ToString]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <If Condition="[authUser.ToUpper() = &quot;SUPPLIERAUTHUSER&quot;]" sap2010:WorkflowViewState.IdRef="If_3">
                              <If.Then>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_2">
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_21">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[authUser]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[out3("results")(0)("records")(0)("RESP").ToString]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </Sequence>
                              </If.Then>
                            </If>
                          </Sequence>
                        </If.Then>
                        <If.Else>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_4">
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_22">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">["Vendor name is not obtained for the Supplier Number " + vendorId+"."]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_23">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_3" Line="[commentStatus]" Source="[logfile]" />
                          </Sequence>
                        </If.Else>
                      </If>
                    </Sequence>
                  </If.Then>
                  <If.Else>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_6">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_24">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching Vendor name for the Vendor Id " + vendorId+"."]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_25">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_4" Line="[commentStatus]" Source="[logfile]" />
                    </Sequence>
                  </If.Else>
                </If>
                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Export MI lines IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_3" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/EXPORTMI/Select?HDRS=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
                  <iai:IONAPIRequestWizard.QueryParameters>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>QERY</x:String>
                        <x:String>SEPC</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>req2</x:String>
                        <x:String>~</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.QueryParameters>
                </iai:IONAPIRequestWizard>
                <If Condition="[StatusCode2 = 200]" sap2010:WorkflowViewState.IdRef="If_38">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_48">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_26">
                        <Assign.To>
                          <OutArgument x:TypeArguments="njl:JToken">[out2]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <If Condition="[out2(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_37">
                        <If.Then>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_7">
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_27">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">PO lines not received</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_28">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">PONOTRECEIVED</InArgument>
                              </Assign.Value>
                            </Assign>
                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_5" Line="[commentStatus]" Source="[logfile]" />
                          </Sequence>
                        </If.Then>
                        <If.Else>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_47">
                            <Sequence.Variables>
                              <Variable x:TypeArguments="x:Int32" Name="x" />
                            </Sequence.Variables>
                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_6" Line="[&quot;PO lines for the Purchase order number &quot; + pono + &quot; is extracted.&quot;]" Source="[logfile]" />
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_29">
                              <Assign.To>
                                <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="scg:List(s:String[])">[new list(OF string()) ()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_30">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Int32">[x]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Int32">1</InArgument>
                              </Assign.Value>
                            </Assign>
                            <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_1" Values="[out2(&quot;results&quot;)(0)(&quot;records&quot;)]">
                              <ActivityAction x:TypeArguments="njl:JToken">
                                <ActivityAction.Argument>
                                  <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
                                </ActivityAction.Argument>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_12">
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_11">
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_31">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="s:String[]">[M3Values]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="s:String[]">[New String(6) {}]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_32">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[m3Values(3)]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">[(Convert.toDecimal(item("REPL").ToString().split("~"C)(3).ToString()) - Convert.toDecimal(item("REPL").ToString().split("~"C)(10).ToString())).ToString()]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <If Condition="[CInt(m3Values(3)).ToString &gt; &quot;0&quot;]" sap2010:WorkflowViewState.IdRef="If_9">
                                      <If.Then>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_10">
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_33">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[m3Values(0)]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(0).ToString()]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_34">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[m3Values(1)]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(1).ToString()]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_35">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[m3Values(2)]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(2).ToString()]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_36">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[m3Values(4)]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(4).ToString()]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_37">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[m3Values(5)]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(5).ToString()]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_38">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[SupplierNo]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(9).ToString()]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_39">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[cono]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(6).ToString()]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_1" MethodName="Add">
                                            <InvokeMethod.TargetObject>
                                              <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</InArgument>
                                            </InvokeMethod.TargetObject>
                                            <InArgument x:TypeArguments="s:String[]">[m3Values]</InArgument>
                                          </InvokeMethod>
                                          <If Condition="[x=1]" sap2010:WorkflowViewState.IdRef="If_8">
                                            <If.Then>
                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_8">
                                                <If Condition="[m3Values(3).Contains(&quot;.&quot;)]" sap2010:WorkflowViewState.IdRef="If_6">
                                                  <If.Then>
                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_40">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="x:String">[m3Values(3)]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="x:String">[m3Values(3).SubString(0,m3Values(3).IndexOf("."))]</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                  </If.Then>
                                                </If>
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_41">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[APIString]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String">["[["+m3Values(0)+","+m3Values(1)+","+m3Values(2)+","+m3Values(3)+","+DictOcrValues("SUBTOTAL").Tostring+"]"]</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                              </Sequence>
                                            </If.Then>
                                            <If.Else>
                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_9">
                                                <If Condition="[m3Values(3).Contains(&quot;.&quot;)]" sap2010:WorkflowViewState.IdRef="If_7">
                                                  <If.Then>
                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_42">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="x:String">[m3Values(3)]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="x:String">[m3Values(3).SubString(0,m3Values(3).IndexOf("."))]</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                  </If.Then>
                                                </If>
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_43">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[APIString]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String">[APIString+",["+m3Values(0)+","+m3Values(1)+","+m3Values(2)+","+m3Values(3)+","+DictOcrValues("SUBTOTAL").Tostring+"]"]</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                              </Sequence>
                                            </If.Else>
                                          </If>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_44">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:Int32">[x]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:Int32">[x+1]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                        </Sequence>
                                      </If.Then>
                                    </If>
                                  </Sequence>
                                </Sequence>
                              </ActivityAction>
                            </ForEach>
                            <If Condition="[M3TotalTableRows.Count &lt; 1]" sap2010:WorkflowViewState.IdRef="If_36">
                              <If.Then>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_13">
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_45">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">["PO lines already invoiced"]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_46">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">ALLPOLINESINVOICED</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_7" Line="[commentStatus]" Source="[logFile]" />
                                </Sequence>
                              </If.Then>
                              <If.Else>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_46">
                                  <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;DictOcrValues&quot;,DictOcrValues},{&quot;colemanAPI&quot;,colemanAPI},{&quot;APIString&quot;,APIString},{&quot;logfile&quot;,logfile}}]" ContinueOnError="True" DisplayName="Coleman Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_1" OutputArguments="[ColemanOutPut]" ResponseCode="[ColemanRespCode]" WorkflowFile="[projectPath+&quot;\CallColeman.xaml&quot;]" />
                                  <If Condition="[ColemanRespCode = 200]" sap2010:WorkflowViewState.IdRef="If_35">
                                    <If.Then>
                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_44">
                                        <Sequence.Variables>
                                          <Variable x:TypeArguments="x:String" Name="variable1" />
                                          <Variable x:TypeArguments="x:String" Name="vat" />
                                        </Sequence.Variables>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_47">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="njl:JToken">[httpOut]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(ColemanOutPut("httpOut").ToString)]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_48">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">[ColemanOutPut("comments").ToString()]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_49">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">[ColemanOutPut("Status").ToString()]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <If Condition="[Status = &quot;Success&quot; AND NOT httpOut.ToString.Contains(&quot;Not Allocated&quot;)]" sap2010:WorkflowViewState.IdRef="If_34">
                                          <If.Then>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_42">
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_50">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[inbnValue]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">
                                                    <Literal x:TypeArguments="x:String" Value="" />
                                                  </InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_51">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[vat]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">
                                                    <Literal x:TypeArguments="x:String" Value="" />
                                                  </InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_41">
                                                <Sequence.Variables>
                                                  <Variable x:TypeArguments="x:String" Name="SupAcho" />
                                                  <Variable x:TypeArguments="x:Int32" Name="StatusCode4" />
                                                  <Variable x:TypeArguments="iru:ResponseObject" Name="respObj4" />
                                                  <Variable x:TypeArguments="njl:JToken" Name="out4" />
                                                  <Variable x:TypeArguments="x:String" Name="ivdate" />
                                                  <Variable x:TypeArguments="x:String" Name="sino" />
                                                  <Variable x:TypeArguments="x:String" Name="cuam" />
                                                  <Variable x:TypeArguments="x:Int32" Name="StatusCode5" />
                                                </Sequence.Variables>
                                                <Assign DisplayName="Assign SupAcho" sap2010:WorkflowViewState.IdRef="Assign_52">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[SupAcho]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String">["ACHO:"+supplierNo]</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for bkid" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_4" Response="[respObj4]" StatusCode="[StatusCode4]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/CRS692MI/SearchBnkAcc&quot;]">
                                                  <iai:IONAPIRequestWizard.QueryParameters>
                                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                      <scg:List x:TypeArguments="x:String" Capacity="8">
                                                        <x:String>SQRY</x:String>
                                                        <x:String>dateformat</x:String>
                                                        <x:String>excludeempty</x:String>
                                                        <x:String>righttrim</x:String>
                                                        <x:String>format</x:String>
                                                        <x:String>extendedresult</x:String>
                                                      </scg:List>
                                                      <scg:List x:TypeArguments="x:String" Capacity="8">
                                                        <x:String>SupAcho</x:String>
                                                        <x:String>YMD8</x:String>
                                                        <x:String>false</x:String>
                                                        <x:String>true</x:String>
                                                        <x:String>PRETTY</x:String>
                                                        <x:String>false</x:String>
                                                      </scg:List>
                                                    </scg:List>
                                                  </iai:IONAPIRequestWizard.QueryParameters>
                                                </iai:IONAPIRequestWizard>
                                                <If Condition="[StatusCode4 = 200]" sap2010:WorkflowViewState.IdRef="If_33">
                                                  <If.Then>
                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_39">
                                                      <Sequence.Variables>
                                                        <Variable x:TypeArguments="x:String" Name="bkid" />
                                                        <Variable x:TypeArguments="iru:ResponseObject" Name="respObj5" />
                                                      </Sequence.Variables>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_53">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="njl:JToken">[out4]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj4.ReadAsText)]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <If Condition="[out4(&quot;results&quot;)(0)(&quot;records&quot;).ToString &lt;&gt; &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_10">
                                                        <If.Then>
                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_14">
                                                            <Assign DisplayName="Assign bkid" sap2010:WorkflowViewState.IdRef="Assign_54">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="x:String">[bkid]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="x:String">[(out4("results")(0)("records")(0)("BKID")).ToString]</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                          </Sequence>
                                                        </If.Then>
                                                        <If.Else>
                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_15">
                                                            <Assign DisplayName="Assign bkid" sap2010:WorkflowViewState.IdRef="Assign_55">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="x:String">[bkid]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="x:String">
                                                                  <Literal x:TypeArguments="x:String" Value="" />
                                                                </InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                          </Sequence>
                                                        </If.Else>
                                                      </If>
                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_38">
                                                        <Sequence.Variables>
                                                          <Variable x:TypeArguments="x:Boolean" Name="InvoiceAlreadyExists" />
                                                        </Sequence.Variables>
                                                        <Assign DisplayName="Assign ivdate" sap2010:WorkflowViewState.IdRef="Assign_56">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:String">[ivdate]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:String">[DateTime.ParseExact(DictOcrValues("INVOICE_RECEIPT_DATE").Tostring, "dd/MM/yyyy", Nothing).ToString("yyyyMMdd")]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <Assign DisplayName="Assign sino" sap2010:WorkflowViewState.IdRef="Assign_57">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:String">[sino]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:String">[DictOcrValues("INVOICE_RECEIPT_ID").Tostring]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <Assign DisplayName="Assign cuam" sap2010:WorkflowViewState.IdRef="Assign_58">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:String">[cuam]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:String">[DictOcrValues("TOTAL").Tostring]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <Assign DisplayName="Assign cuam" sap2010:WorkflowViewState.IdRef="Assign_59">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:String">[cuam]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:String">[cuam.Replace(",","")]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_119">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:String">[DictOcrValues("INVOICE_RECEIPT_ID").Tostring + "-" + division + "-" + vendorID + "-" + System.DateTime.Now.ToString("yyyyMMddHHmmssfff").tostring]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_5" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                                          <iai:IONAPIRequestWizard.QueryParameters>
                                                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                              <scg:List x:TypeArguments="x:String" Capacity="32">
                                                                <x:String>SUNO</x:String>
                                                                <x:String>IVDT</x:String>
                                                                <x:String>DIVI</x:String>
                                                                <x:String>SINO</x:String>
                                                                <x:String>CUCD</x:String>
                                                                <x:String>TEPY</x:String>
                                                                <x:String>PYME</x:String>
                                                                <x:String>CUAM</x:String>
                                                                <x:String>IMCD</x:String>
                                                                <x:String>CRTP</x:String>
                                                                <x:String>dateformat</x:String>
                                                                <x:String>excludeempty</x:String>
                                                                <x:String>righttrim</x:String>
                                                                <x:String>format</x:String>
                                                                <x:String>extendedresult</x:String>
                                                                <x:String>APCD</x:String>
                                                                <x:String>CORI</x:String>
                                                              </scg:List>
                                                              <scg:List x:TypeArguments="x:String" Capacity="32">
                                                                <x:String>SupplierNo</x:String>
                                                                <x:String>ivdate</x:String>
                                                                <x:String>division</x:String>
                                                                <x:String>sino</x:String>
                                                                <x:String>cucd</x:String>
                                                                <x:String>tepy</x:String>
                                                                <x:String>pyme</x:String>
                                                                <x:String>cuam</x:String>
                                                                <x:String>1</x:String>
                                                                <x:String>1</x:String>
                                                                <x:String>YMD8</x:String>
                                                                <x:String>false</x:String>
                                                                <x:String>true</x:String>
                                                                <x:String>PRETTY</x:String>
                                                                <x:String>false</x:String>
                                                                <x:String>authUser</x:String>
                                                                <x:String>correlationID</x:String>
                                                              </scg:List>
                                                            </scg:List>
                                                          </iai:IONAPIRequestWizard.QueryParameters>
                                                        </iai:IONAPIRequestWizard>
                                                        <If Condition="[StatusCode5 = 200  and respObj5.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" sap2010:WorkflowViewState.IdRef="If_11">
                                                          <If.Then>
                                                            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_6" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                                              <iai:IONAPIRequestWizard.QueryParameters>
                                                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                  <scg:List x:TypeArguments="x:String" Capacity="32">
                                                                    <x:String>SUNO</x:String>
                                                                    <x:String>IVDT</x:String>
                                                                    <x:String>DIVI</x:String>
                                                                    <x:String>SINO</x:String>
                                                                    <x:String>CUCD</x:String>
                                                                    <x:String>TEPY</x:String>
                                                                    <x:String>PYME</x:String>
                                                                    <x:String>CUAM</x:String>
                                                                    <x:String>IMCD</x:String>
                                                                    <x:String>CRTP</x:String>
                                                                    <x:String>dateformat</x:String>
                                                                    <x:String>excludeempty</x:String>
                                                                    <x:String>righttrim</x:String>
                                                                    <x:String>format</x:String>
                                                                    <x:String>extendedresult</x:String>
                                                                    <x:String>APCD</x:String>
                                                                    <x:String>BKID</x:String>
                                                                    <x:String>CORI</x:String>
                                                                  </scg:List>
                                                                  <scg:List x:TypeArguments="x:String" Capacity="32">
                                                                    <x:String>SupplierNo</x:String>
                                                                    <x:String>ivdate</x:String>
                                                                    <x:String>division</x:String>
                                                                    <x:String>sino</x:String>
                                                                    <x:String>cucd</x:String>
                                                                    <x:String>tepy</x:String>
                                                                    <x:String>pyme</x:String>
                                                                    <x:String>cuam</x:String>
                                                                    <x:String>1</x:String>
                                                                    <x:String>1</x:String>
                                                                    <x:String>YMD8</x:String>
                                                                    <x:String>false</x:String>
                                                                    <x:String>true</x:String>
                                                                    <x:String>PRETTY</x:String>
                                                                    <x:String>false</x:String>
                                                                    <x:String>authUser</x:String>
                                                                    <x:String>bkid</x:String>
                                                                    <x:String>correlationID</x:String>
                                                                  </scg:List>
                                                                </scg:List>
                                                              </iai:IONAPIRequestWizard.QueryParameters>
                                                            </iai:IONAPIRequestWizard>
                                                          </If.Then>
                                                        </If>
                                                        <If Condition="[StatusCode5 = 200]" sap2010:WorkflowViewState.IdRef="If_32">
                                                          <If.Then>
                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_36">
                                                              <Sequence.Variables>
                                                                <Variable x:TypeArguments="x:Int32" Name="respout1" />
                                                                <Variable x:TypeArguments="x:String" Name="additionalChargeExcp" />
                                                              </Sequence.Variables>
                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_60">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="njl:JToken">[out5]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj5.ReadAsText)]</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                              <If Condition="[out5(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_12">
                                                                <If.Then>
                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_16">
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_61">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:Boolean">[InvoiceAlreadyExists]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_62">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">[(out5("results")(0)("errorMessage")).ToString]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_63">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                  </Sequence>
                                                                </If.Then>
                                                                <If.Else>
                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_17">
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_64">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:Boolean">[InvoiceAlreadyExists]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_65">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[inbnValue]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">[(out5("results")(0)("records")(0)("INBN")).ToString]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_66">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">["Invoice Header Created"]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                  </Sequence>
                                                                </If.Else>
                                                              </If>
                                                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_8" Line="[commentStatus]" Source="[logfile]" />
                                                              <If Condition="[NOT InvoiceAlreadyExists]" sap2010:WorkflowViewState.IdRef="If_28">
                                                                <If.Then>
                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_33">
                                                                    <Sequence.Variables>
                                                                      <Variable x:TypeArguments="x:Int32" Name="respout" />
                                                                      <Variable x:TypeArguments="scg:List(x:String)" Name="vatList" />
                                                                      <Variable x:TypeArguments="x:String" Name="TaxCode" />
                                                                      <Variable x:TypeArguments="x:String" Name="charge" />
                                                                      <Variable x:TypeArguments="x:String" Name="discount" />
                                                                      <Variable x:TypeArguments="x:Int32" Name="k" />
                                                                    </Sequence.Variables>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_115">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[vat]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">
                                                                          <Literal x:TypeArguments="x:String" Value="" />
                                                                        </InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <If Condition="[DictOcrValues(&quot;VAT_AMOUNT&quot;).Tostring &lt;&gt; &quot;&quot; AND CInt(DictOcrValues(&quot;VAT_AMOUNT&quot;).Tostring).ToString &lt;&gt; &quot;0&quot;]" sap2010:WorkflowViewState.IdRef="If_42">
                                                                      <If.Then>
                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_112">
                                                                          <Assign.To>
                                                                            <OutArgument x:TypeArguments="x:String">[vat]</OutArgument>
                                                                          </Assign.To>
                                                                          <Assign.Value>
                                                                            <InArgument x:TypeArguments="x:String">[DictOcrValues("VAT_AMOUNT").Tostring]</InArgument>
                                                                          </Assign.Value>
                                                                        </Assign>
                                                                      </If.Then>
                                                                    </If>
                                                                    <If Condition="[DictOcrValues(&quot;SHIPPING_AND_HANDLING_AMOUNT&quot;).Tostring &lt;&gt; &quot;&quot; AND CInt(DictOcrValues(&quot;SHIPPING_AND_HANDLING_AMOUNT&quot;).Tostring).ToString &lt;&gt; &quot;0&quot;]" sap2010:WorkflowViewState.IdRef="If_44">
                                                                      <If.Then>
                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_114">
                                                                          <Assign.To>
                                                                            <OutArgument x:TypeArguments="x:String">[vat]</OutArgument>
                                                                          </Assign.To>
                                                                          <Assign.Value>
                                                                            <InArgument x:TypeArguments="x:String">[(Double.Parse(vat) + Double.Parse(DictOcrValues("SHIPPING_AND_HANDLING_AMOUNT").Tostring)).ToString]</InArgument>
                                                                          </Assign.Value>
                                                                        </Assign>
                                                                      </If.Then>
                                                                    </If>
                                                                    <If Condition="[DictOcrValues(&quot;Invoice_Level_Discount_AMOUNT&quot;).Tostring &lt;&gt; &quot;&quot; AND CInt(DictOcrValues(&quot;Invoice_Level_Discount_AMOUNT&quot;).Tostring).ToString &lt;&gt; &quot;0&quot;]" sap2010:WorkflowViewState.IdRef="If_43">
                                                                      <If.Then>
                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_113">
                                                                          <Assign.To>
                                                                            <OutArgument x:TypeArguments="x:String">[vat]</OutArgument>
                                                                          </Assign.To>
                                                                          <Assign.Value>
                                                                            <InArgument x:TypeArguments="x:String">[(Double.Parse(vat) - Double.Parse(DictOcrValues("Invoice_Level_Discount_AMOUNT").Tostring)).ToString]</InArgument>
                                                                          </Assign.Value>
                                                                        </Assign>
                                                                      </If.Then>
                                                                    </If>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_116">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:Int32">[k]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:Int32">0</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_3" Values="[httpOut]">
                                                                      <ActivityAction x:TypeArguments="njl:JToken">
                                                                        <ActivityAction.Argument>
                                                                          <DelegateInArgument x:TypeArguments="njl:JToken" Name="out" />
                                                                        </ActivityAction.Argument>
                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_23">
                                                                          <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_2" Values="[M3TotalTableRows]">
                                                                            <ActivityAction x:TypeArguments="s:String[]">
                                                                              <ActivityAction.Argument>
                                                                                <DelegateInArgument x:TypeArguments="s:String[]" Name="rows" />
                                                                              </ActivityAction.Argument>
                                                                              <If Condition="[out(&quot;line_number&quot;).ToString=rows(0) And out(&quot;rcd_number&quot;).ToString=rows(1)]" sap2010:WorkflowViewState.IdRef="If_49">
                                                                                <If.Then>
                                                                                  <Sequence DisplayName="Add lines Sequence" sap2010:WorkflowViewState.IdRef="Sequence_57">
                                                                                    <Sequence.Variables>
                                                                                      <Variable x:TypeArguments="x:String" Name="puno" />
                                                                                      <Variable x:TypeArguments="x:String" Name="inbn" />
                                                                                      <Variable x:TypeArguments="x:String" Name="itno" />
                                                                                      <Variable x:TypeArguments="x:String" Name="ppun" />
                                                                                      <Variable x:TypeArguments="x:String" Name="puun" />
                                                                                      <Variable x:TypeArguments="njl:JToken" Name="out4" />
                                                                                      <Variable x:TypeArguments="njl:JToken" Name="out3" />
                                                                                      <Variable x:TypeArguments="x:String" Name="repn" />
                                                                                      <Variable x:TypeArguments="x:String" Name="pnli" />
                                                                                      <Variable x:TypeArguments="x:String" Name="grpr" />
                                                                                      <Variable x:TypeArguments="njl:JToken" Name="out7" />
                                                                                      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj7" />
                                                                                      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj4" />
                                                                                      <Variable x:TypeArguments="x:String" Name="ivqa" />
                                                                                      <Variable x:TypeArguments="x:Int32" Name="StatusCode6" />
                                                                                      <Variable x:TypeArguments="iru:ResponseObject" Name="RespObj6" />
                                                                                      <Variable x:TypeArguments="njl:JToken" Name="out6" />
                                                                                      <Variable x:TypeArguments="x:Int32" Name="StatusCode7" />
                                                                                    </Sequence.Variables>
                                                                                    <Assign DisplayName="Assign inbn" sap2010:WorkflowViewState.IdRef="Assign_120">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:String">[inbn]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:String">[inbnValue]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_121">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="scg:List(x:String)">[vatList]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)()]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_122">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:String">[puno]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:String">[DictOcrValues("PO_NUMBER").Tostring]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_123">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:String">[pnli]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:String">[rows(0)]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <Assign DisplayName="Assign itno" sap2010:WorkflowViewState.IdRef="Assign_124">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:String">[itno]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:String">[rows(5)]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_125">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:String">[repn]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:String">[rows(1)]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_126">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:String">[ivqa]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:String">[rows(3)]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_127">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:String">[grpr]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:String">[rows(2)]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <If Condition="[k=0]" sap2010:WorkflowViewState.IdRef="If_46">
                                                                                      <If.Then>
                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_128">
                                                                                          <Assign.To>
                                                                                            <OutArgument x:TypeArguments="x:String">[grpr]</OutArgument>
                                                                                          </Assign.To>
                                                                                          <Assign.Value>
                                                                                            <InArgument x:TypeArguments="x:String">[(Double.Parse(rows(2)) + Double.Parse(vat)/Double.Parse(ivqa)).ToString]</InArgument>
                                                                                          </Assign.Value>
                                                                                        </Assign>
                                                                                      </If.Then>
                                                                                    </If>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_129">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:Int32">[k]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:Int32">[k+1]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for ppun, puun" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_13" Response="[respObj7]" StatusCode="[StatusCode7]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/MMS200MI/GetItmBasic&quot;]">
                                                                                      <iai:IONAPIRequestWizard.QueryParameters>
                                                                                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                          <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                            <x:String>ITNO</x:String>
                                                                                            <x:String>dateformat</x:String>
                                                                                            <x:String>excludeempty</x:String>
                                                                                            <x:String>righttrim</x:String>
                                                                                            <x:String>format</x:String>
                                                                                            <x:String>extendedresult</x:String>
                                                                                          </scg:List>
                                                                                          <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                            <x:String>itno</x:String>
                                                                                            <x:String>YMD8</x:String>
                                                                                            <x:String>false</x:String>
                                                                                            <x:String>true</x:String>
                                                                                            <x:String>PRETTY</x:String>
                                                                                            <x:String>false</x:String>
                                                                                          </scg:List>
                                                                                        </scg:List>
                                                                                      </iai:IONAPIRequestWizard.QueryParameters>
                                                                                    </iai:IONAPIRequestWizard>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_130">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="njl:JToken">[out7]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj7.ReadAsText)]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <If Condition="[out7(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_48">
                                                                                      <If.Then>
                                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_54">
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_131">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:String">["Purchase price UOM and purchase order UOM are not available and the lines are not allocated to the given amount."]</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_132">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_30" Line="[commentStatus]" Source="[logfile]" />
                                                                                        </Sequence>
                                                                                      </If.Then>
                                                                                      <If.Else>
                                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_56">
                                                                                          <Sequence.Variables>
                                                                                            <Variable x:TypeArguments="njl:JToken" Name="out8" />
                                                                                            <Variable x:TypeArguments="x:Int32" Name="StatusCode8" />
                                                                                            <Variable x:TypeArguments="iru:ResponseObject" Name="respObj8" />
                                                                                          </Sequence.Variables>
                                                                                          <Assign DisplayName="Assign ppun" sap2010:WorkflowViewState.IdRef="Assign_133">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:String">[ppun]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:String">[out7("results")(0)("records")(0)("PPUN").ToString]</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                          <Assign DisplayName="Assign puun" sap2010:WorkflowViewState.IdRef="Assign_134">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:String">[puun]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:String">[out7("results")(0)("records")(0)("PUUN").ToString]</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_135">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:String">[puno]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:String">[DictOcrValues("PO_NUMBER").Tostring]</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_14" Response="[respObj8]" StatusCode="[StatusCode8]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                                                            <iai:IONAPIRequestWizard.QueryParameters>
                                                                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                                                  <x:String>INBN</x:String>
                                                                                                  <x:String>RDTP</x:String>
                                                                                                  <x:String>DIVI</x:String>
                                                                                                  <x:String>PPUN</x:String>
                                                                                                  <x:String>PUUN</x:String>
                                                                                                  <x:String>GRPR</x:String>
                                                                                                  <x:String>ITNO</x:String>
                                                                                                  <x:String>IVQA</x:String>
                                                                                                  <x:String>PNLI</x:String>
                                                                                                  <x:String>PUNO</x:String>
                                                                                                  <x:String>RELP</x:String>
                                                                                                  <x:String>REPN</x:String>
                                                                                                </scg:List>
                                                                                                <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                                                  <x:String>inbn</x:String>
                                                                                                  <x:String>1</x:String>
                                                                                                  <x:String>division</x:String>
                                                                                                  <x:String>ppun</x:String>
                                                                                                  <x:String>puun</x:String>
                                                                                                  <x:String>grpr</x:String>
                                                                                                  <x:String>itno</x:String>
                                                                                                  <x:String>ivqa</x:String>
                                                                                                  <x:String>pnli</x:String>
                                                                                                  <x:String>puno</x:String>
                                                                                                  <x:String>1</x:String>
                                                                                                  <x:String>repn</x:String>
                                                                                                </scg:List>
                                                                                              </scg:List>
                                                                                            </iai:IONAPIRequestWizard.QueryParameters>
                                                                                          </iai:IONAPIRequestWizard>
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_136">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="njl:JToken">[out8]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj8.ReadAsText)]</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                          <If Condition="[out8(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_47">
                                                                                            <If.Else>
                                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_55">
                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_137">
                                                                                                  <Assign.To>
                                                                                                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                  </Assign.To>
                                                                                                  <Assign.Value>
                                                                                                    <InArgument x:TypeArguments="x:String">Invoice Lines Created</InArgument>
                                                                                                  </Assign.Value>
                                                                                                </Assign>
                                                                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_31" Line="[commentStatus]" Source="[logfile]" />
                                                                                              </Sequence>
                                                                                            </If.Else>
                                                                                          </If>
                                                                                        </Sequence>
                                                                                      </If.Else>
                                                                                    </If>
                                                                                  </Sequence>
                                                                                </If.Then>
                                                                              </If>
                                                                            </ActivityAction>
                                                                          </ForEach>
                                                                        </Sequence>
                                                                      </ActivityAction>
                                                                    </ForEach>
                                                                  </Sequence>
                                                                </If.Then>
                                                              </If>
                                                              <If Condition="[inbnValue = &quot;&quot;]" DisplayName="If validation" sap2010:WorkflowViewState.IdRef="If_31">
                                                                <If.Then>
                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_95">
                                                                    <Assign.To>
                                                                      <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                    </Assign.To>
                                                                    <Assign.Value>
                                                                      <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                                                                    </Assign.Value>
                                                                  </Assign>
                                                                </If.Then>
                                                                <If.Else>
                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_35">
                                                                    <Sequence.Variables>
                                                                      <Variable x:TypeArguments="x:String" Name="SuccessMoved" />
                                                                    </Sequence.Variables>
                                                                    <If Condition="[additionalChargeExcp = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_30">
                                                                      <If.Then>
                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_34">
                                                                          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" Response="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_12" StatusCode="[respout1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS455MI/ValidByBatchNo&quot;]">
                                                                            <iai:IONAPIRequestWizard.QueryParameters>
                                                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                  <x:String>INBN</x:String>
                                                                                  <x:String>DIVI</x:String>
                                                                                </scg:List>
                                                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                  <x:String>inbnValue</x:String>
                                                                                  <x:String>division</x:String>
                                                                                </scg:List>
                                                                              </scg:List>
                                                                            </iai:IONAPIRequestWizard.QueryParameters>
                                                                          </iai:IONAPIRequestWizard>
                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_96">
                                                                            <Assign.To>
                                                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                            </Assign.To>
                                                                            <Assign.Value>
                                                                              <InArgument x:TypeArguments="x:String">["Invoice created and validated"]</InArgument>
                                                                            </Assign.Value>
                                                                          </Assign>
                                                                          <If Condition="[vat = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_29">
                                                                            <If.Else>
                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_97">
                                                                                <Assign.To>
                                                                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                </Assign.To>
                                                                                <Assign.Value>
                                                                                  <InArgument x:TypeArguments="x:String">["Invoice created and validated. Vat line is available."]</InArgument>
                                                                                </Assign.Value>
                                                                              </Assign>
                                                                            </If.Else>
                                                                          </If>
                                                                        </Sequence>
                                                                      </If.Then>
                                                                      <If.Else>
                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_98">
                                                                          <Assign.To>
                                                                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                          </Assign.To>
                                                                          <Assign.Value>
                                                                            <InArgument x:TypeArguments="x:String">["Invoice created. " + additionalChargeExcp]</InArgument>
                                                                          </Assign.Value>
                                                                        </Assign>
                                                                      </If.Else>
                                                                    </If>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_99">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">["SUCCESS"]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                  </Sequence>
                                                                </If.Else>
                                                              </If>
                                                            </Sequence>
                                                          </If.Then>
                                                          <If.Else>
                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_37">
                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_100">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="x:String">["Received an IONAPI error while adding Invoice header."]</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_101">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_24" Line="[commentStatus]" Source="[logfile]" />
                                                            </Sequence>
                                                          </If.Else>
                                                        </If>
                                                      </Sequence>
                                                    </Sequence>
                                                  </If.Then>
                                                  <If.Else>
                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_40">
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_102">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching Banking details for the Supplier Number " + SupplierNo +"."]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_103">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_25" Line="[commentStatus]" Source="[logfile]" />
                                                    </Sequence>
                                                  </If.Else>
                                                </If>
                                              </Sequence>
                                            </Sequence>
                                          </If.Then>
                                          <If.Else>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_43">
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_104">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">["Optimizer - Lines are not allocated"]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_105">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_26" Line="[commentStatus]" Source="[logfile]" />
                                            </Sequence>
                                          </If.Else>
                                        </If>
                                      </Sequence>
                                    </If.Then>
                                    <If.Else>
                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_45">
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_106">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">["Error occured with Optimizer "]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_107">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_27" Line="[commentStatus]" Source="[logfile]" />
                                      </Sequence>
                                    </If.Else>
                                  </If>
                                </Sequence>
                              </If.Else>
                            </If>
                          </Sequence>
                        </If.Else>
                      </If>
                    </Sequence>
                  </If.Then>
                  <If.Else>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_49">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_108">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching lines for the PO " + pono +"."]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_109">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_28" Line="[commentStatus]" Source="[logfile]" />
                    </Sequence>
                  </If.Else>
                </If>
              </Sequence>
            </If.Else>
          </If>
        </Sequence>
      </If.Then>
      <If.Else>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_52">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_110">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching company number and division for the PO " + pono +"."]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_111">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
            </Assign.Value>
          </Assign>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_29" Line="[commentStatus]" Source="[logfile]" />
        </Sequence>
      </If.Else>
    </If>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_1" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_7" sap:VirtualizedContainerService.HintSize="2760,62" />
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_2" sap:VirtualizedContainerService.HintSize="2448,22" />
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="2448,62" />
      <sap2010:ViewStateData Id="Assign_11" sap:VirtualizedContainerService.HintSize="2448,62" />
      <sap2010:ViewStateData Id="Assign_12" sap:VirtualizedContainerService.HintSize="2448,62" />
      <sap2010:ViewStateData Id="Assign_13" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_14" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="510,216" />
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="2448,370" />
      <sap2010:ViewStateData Id="Assign_15" sap:VirtualizedContainerService.HintSize="2448,62" />
      <sap2010:ViewStateData Id="Assign_16" sap:VirtualizedContainerService.HintSize="2448,62" />
      <sap2010:ViewStateData Id="Assign_17" sap:VirtualizedContainerService.HintSize="2448,62" />
      <sap2010:ViewStateData Id="Assign_18" sap:VirtualizedContainerService.HintSize="2448,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_2" sap:VirtualizedContainerService.HintSize="2448,22" />
      <sap2010:ViewStateData Id="Assign_19" sap:VirtualizedContainerService.HintSize="776,62" />
      <sap2010:ViewStateData Id="Assign_20" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_21" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="464,340" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="486,566">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_22" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_23" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_4" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="776,720" />
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="798,946">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_24" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_25" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_6" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_5" sap:VirtualizedContainerService.HintSize="2448,1100" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_3" sap:VirtualizedContainerService.HintSize="2448,22" />
      <sap2010:ViewStateData Id="Assign_26" sap:VirtualizedContainerService.HintSize="2136,62" />
      <sap2010:ViewStateData Id="Assign_27" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_28" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_5" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_6" sap:VirtualizedContainerService.HintSize="1824,22" />
      <sap2010:ViewStateData Id="Assign_29" sap:VirtualizedContainerService.HintSize="1824,62" />
      <sap2010:ViewStateData Id="Assign_30" sap:VirtualizedContainerService.HintSize="1824,62" />
      <sap2010:ViewStateData Id="Assign_31" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_32" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_33" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_34" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_35" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_36" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_37" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_38" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_39" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_1" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Assign_40" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_6" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="Assign_41" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Sequence_8" sap:VirtualizedContainerService.HintSize="486,442">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_42" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_7" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="Assign_43" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Sequence_9" sap:VirtualizedContainerService.HintSize="486,442">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_8" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_44" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_10" sap:VirtualizedContainerService.HintSize="264,1166.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_9" sap:VirtualizedContainerService.HintSize="464,1320.66666666667" />
      <sap2010:ViewStateData Id="Sequence_11" sap:VirtualizedContainerService.HintSize="486,1648.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_12" sap:VirtualizedContainerService.HintSize="508,1772.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_1" sap:VirtualizedContainerService.HintSize="1824,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_45" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_46" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_7" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_13" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_1" sap:VirtualizedContainerService.HintSize="1512,22" />
      <sap2010:ViewStateData Id="Assign_47" sap:VirtualizedContainerService.HintSize="1200,62" />
      <sap2010:ViewStateData Id="Assign_48" sap:VirtualizedContainerService.HintSize="1200,62" />
      <sap2010:ViewStateData Id="Assign_49" sap:VirtualizedContainerService.HintSize="1200,62" />
      <sap2010:ViewStateData Id="Assign_50" sap:VirtualizedContainerService.HintSize="888,62" />
      <sap2010:ViewStateData Id="Assign_51" sap:VirtualizedContainerService.HintSize="888,62" />
      <sap2010:ViewStateData Id="Assign_52" sap:VirtualizedContainerService.HintSize="866,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_4" sap:VirtualizedContainerService.HintSize="866,22" />
      <sap2010:ViewStateData Id="Assign_53" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_54" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_14" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_55" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_15" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_10" sap:VirtualizedContainerService.HintSize="554,340" />
      <sap2010:ViewStateData Id="Assign_56" sap:VirtualizedContainerService.HintSize="490,62" />
      <sap2010:ViewStateData Id="Assign_57" sap:VirtualizedContainerService.HintSize="490,62" />
      <sap2010:ViewStateData Id="Assign_58" sap:VirtualizedContainerService.HintSize="490,62" />
      <sap2010:ViewStateData Id="Assign_59" sap:VirtualizedContainerService.HintSize="490,62" />
      <sap2010:ViewStateData Id="Assign_119" sap:VirtualizedContainerService.HintSize="490,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_5" sap:VirtualizedContainerService.HintSize="490,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_6" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_11" sap:VirtualizedContainerService.HintSize="490,214" />
      <sap2010:ViewStateData Id="Assign_60" sap:VirtualizedContainerService.HintSize="1177.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_61" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_62" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_63" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_16" sap:VirtualizedContainerService.HintSize="264,390">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_64" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_65" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_66" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_17" sap:VirtualizedContainerService.HintSize="264,390">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_12" sap:VirtualizedContainerService.HintSize="1177.33333333333,544" />
      <sap2010:ViewStateData Id="Append_Line_8" sap:VirtualizedContainerService.HintSize="1177.33333333333,22" />
      <sap2010:ViewStateData Id="Assign_115" sap:VirtualizedContainerService.HintSize="547.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_112" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_42" sap:VirtualizedContainerService.HintSize="547.333333333333,216" />
      <sap2010:ViewStateData Id="Assign_114" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_44" sap:VirtualizedContainerService.HintSize="547.333333333333,216" />
      <sap2010:ViewStateData Id="Assign_113" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_43" sap:VirtualizedContainerService.HintSize="547.333333333333,216" />
      <sap2010:ViewStateData Id="Assign_116" sap:VirtualizedContainerService.HintSize="547.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_120" sap:VirtualizedContainerService.HintSize="776,62" />
      <sap2010:ViewStateData Id="Assign_121" sap:VirtualizedContainerService.HintSize="776,62" />
      <sap2010:ViewStateData Id="Assign_122" sap:VirtualizedContainerService.HintSize="776,62" />
      <sap2010:ViewStateData Id="Assign_123" sap:VirtualizedContainerService.HintSize="776,62" />
      <sap2010:ViewStateData Id="Assign_124" sap:VirtualizedContainerService.HintSize="776,62" />
      <sap2010:ViewStateData Id="Assign_125" sap:VirtualizedContainerService.HintSize="776,62" />
      <sap2010:ViewStateData Id="Assign_126" sap:VirtualizedContainerService.HintSize="776,62" />
      <sap2010:ViewStateData Id="Assign_127" sap:VirtualizedContainerService.HintSize="776,62" />
      <sap2010:ViewStateData Id="Assign_128" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_46" sap:VirtualizedContainerService.HintSize="776,216" />
      <sap2010:ViewStateData Id="Assign_129" sap:VirtualizedContainerService.HintSize="776,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_13" sap:VirtualizedContainerService.HintSize="776,22" />
      <sap2010:ViewStateData Id="Assign_130" sap:VirtualizedContainerService.HintSize="776,62" />
      <sap2010:ViewStateData Id="Assign_131" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_132" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_30" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_54" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_133" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_134" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_135" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_14" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_136" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_137" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_31" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_55" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_47" sap:VirtualizedContainerService.HintSize="464,402" />
      <sap2010:ViewStateData Id="Sequence_56" sap:VirtualizedContainerService.HintSize="486,996">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_48" sap:VirtualizedContainerService.HintSize="776,1150" />
      <sap2010:ViewStateData Id="Sequence_57" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_49" sap:VirtualizedContainerService.HintSize="464,214" />
      <sap2010:ViewStateData Id="ForEach`1_2" sap:VirtualizedContainerService.HintSize="494.666666666667,366.666666666667" />
      <sap2010:ViewStateData Id="Sequence_23" sap:VirtualizedContainerService.HintSize="516.666666666667,490.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_3" sap:VirtualizedContainerService.HintSize="547.333333333333,643.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_33" sap:VirtualizedContainerService.HintSize="569.333333333333,1739.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_28" sap:VirtualizedContainerService.HintSize="1177.33333333333,1893.33333333333" />
      <sap2010:ViewStateData Id="Assign_95" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_12" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_96" sap:VirtualizedContainerService.HintSize="464,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_97" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="If_29" sap:VirtualizedContainerService.HintSize="464,213.333333333333" />
      <sap2010:ViewStateData Id="Sequence_34" sap:VirtualizedContainerService.HintSize="486,500.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_98" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="If_30" sap:VirtualizedContainerService.HintSize="754,652.666666666667" />
      <sap2010:ViewStateData Id="Assign_99" sap:VirtualizedContainerService.HintSize="754,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_35" sap:VirtualizedContainerService.HintSize="776,878">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_31" sap:VirtualizedContainerService.HintSize="1177.33333333333,1030" />
      <sap2010:ViewStateData Id="Sequence_36" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_100" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_101" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_24" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_37" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_32" sap:VirtualizedContainerService.HintSize="490,504" />
      <sap2010:ViewStateData Id="Sequence_38" sap:VirtualizedContainerService.HintSize="554,1454">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_39" sap:VirtualizedContainerService.HintSize="576,2060">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_102" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_103" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_25" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_40" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_33" sap:VirtualizedContainerService.HintSize="866,2214" />
      <sap2010:ViewStateData Id="Sequence_41" sap:VirtualizedContainerService.HintSize="888,2502">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_42" sap:VirtualizedContainerService.HintSize="910,2830">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_104" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_105" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_26" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_43" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_34" sap:VirtualizedContainerService.HintSize="1200,2984" />
      <sap2010:ViewStateData Id="Sequence_44" sap:VirtualizedContainerService.HintSize="1222,3414">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_106" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_107" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_27" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_45" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_35" sap:VirtualizedContainerService.HintSize="1512,3568" />
      <sap2010:ViewStateData Id="Sequence_46" sap:VirtualizedContainerService.HintSize="1534,3754">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_36" sap:VirtualizedContainerService.HintSize="1824,3908" />
      <sap2010:ViewStateData Id="Sequence_47" sap:VirtualizedContainerService.HintSize="1846,4390.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_37" sap:VirtualizedContainerService.HintSize="2136,4544.66666666667" />
      <sap2010:ViewStateData Id="Sequence_48" sap:VirtualizedContainerService.HintSize="2158,4770.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_108" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_109" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_28" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_49" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_38" sap:VirtualizedContainerService.HintSize="2448,4924.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_50" sap:VirtualizedContainerService.HintSize="2470,7498.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_39" sap:VirtualizedContainerService.HintSize="2760,7652.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_51" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_110" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_111" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_29" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_52" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_40" sap:VirtualizedContainerService.HintSize="464,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_53" sap:VirtualizedContainerService.HintSize="486,1012">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="526,1212" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>