﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="colemanAPI" Type="InArgument(x:String)" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
    <x:Property Name="distributionType" Type="InArgument(x:String)" />
    <x:Property Name="userIdentifier" Type="InArgument(x:String)" />
    <x:Property Name="emailSubject" Type="InArgument(x:String)" />
    <x:Property Name="emailReceivedTime" Type="InArgument(x:String)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="manualEntry" Type="InArgument(x:Boolean)" />
    <x:Property Name="documentPath" Type="InArgument(x:String)" />
    <x:Property Name="configurationFolder" Type="InArgument(x:String)" />
    <x:Property Name="status" Type="OutArgument(x:String)" />
    <x:Property Name="statusComments" Type="OutArgument(x:String)" />
    <x:Property Name="ocrResults" Type="InArgument(x:String)" />
    <x:Property Name="enableMessageBoxes" Type="InArgument(x:Boolean)" />
    <x:Property Name="vendorName" Type="InArgument(x:String)" />
    <x:Property Name="company" Type="InArgument(x:String)" />
    <x:Property Name="datalakeAPILogicalId" Type="InArgument(x:String)" />
    <x:Property Name="chargeCode" Type="InArgument(x:String)" />
    <x:Property Name="discountCode" Type="InArgument(x:String)" />
    <x:Property Name="authUser" Type="InArgument(x:String)" />
    <x:Property Name="imsAPIUrl" Type="InArgument(x:String)" />
    <x:Property Name="failureCount" Type="InArgument(x:Int32)" />
    <x:Property Name="notificationFailureCount" Type="InArgument(x:String)" />
    <x:Property Name="vatCodeConfig" Type="InArgument(x:String)" />
    <x:Property Name="poDiscountsHandlingConfig" Type="InArgument(x:Boolean)" />
    <x:Property Name="matchVendorItemCode" Type="InArgument(x:Boolean)" />
    <x:Property Name="extractFromWidgetDatalake" Type="InArgument(x:Boolean)" />
    <x:Property Name="includeDatalake" Type="InArgument(x:Boolean)" />
    <x:Property Name="invoiceType" Type="InArgument(x:String)" />
    <x:Property Name="approvalRequired" Type="InArgument(x:Boolean)" />
    <x:Property Name="approvalWorkflow" Type="InArgument(x:String)" />
    <x:Property Name="checkAmountBussinessRule" Type="InArgument(x:String)" />
    <x:Property Name="vendorNames" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="deliveryNumbers" Type="InArgument(scg:List(x:String))" />
    <x:Property Name="GLCode" Type="InArgument(x:String)" />
    <x:Property Name="division" Type="InArgument(x:String)" />
    <x:Property Name="vendorId" Type="InArgument(x:String)" />
    <x:Property Name="approvalList" Type="InOutArgument(scg:List(x:String))" />
    <x:Property Name="APResp" Type="InOutArgument(x:String)" />
    <x:Property Name="ListocrLineValues" Type="InArgument(scg:List(scg:Dictionary(x:String, x:Object)))" />
    <x:Property Name="DictOcrValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="processId" Type="OutArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>System</x:String>
      <x:String>System.Globalization</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System.IO</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>System.Linq</x:String>
      <x:String>Microsoft.VisualBasic</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="ProcessPOInvoice" sap2010:WorkflowViewState.IdRef="Sequence_1">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="convertedDate" />
      <Variable x:TypeArguments="x:Int32" Name="poInvoiceResponseCode" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="poInvoiceResponseDictionary" />
      <Variable x:TypeArguments="njl:JToken" Name="ocrText" />
      <Variable x:TypeArguments="x:Boolean" Name="processInvoice" />
      <Variable x:TypeArguments="x:String" Name="statusDisp" />
      <Variable x:TypeArguments="x:String" Name="PID" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="idmResponseDictionary" />
      <Variable x:TypeArguments="scg:List(s:String[])" Name="Attribute_List" />
      <Variable x:TypeArguments="x:String" Name="fileName" />
      <Variable x:TypeArguments="x:Int32" Name="attriResp" />
      <Variable x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="LinesDict" />
    </Sequence.Variables>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_18">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[notificationFailureCount]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">-</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_78">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(x:String)">[approvalList]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(x:String)">[new list(Of string)]</InArgument>
      </Assign.Value>
    </Assign>
    <If Condition="[invoiceType = &quot;DELIVERYNOTE&quot;]" sap2010:WorkflowViewState.IdRef="If_28">
      <If.Then>
        <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;DictOcrValues&quot;,DictOcrValues},{&quot;colemanAPI&quot;,colemanAPI},{&quot;logFile&quot;,logFile},{&quot;projectPath&quot;,projectPath},{&quot;chargeCode&quot;,chargeCode},{&quot;discountCode&quot;,discountCode},{&quot;authUser&quot;,authUser},{&quot;APResp&quot;,APResp},{&quot;division&quot;,division},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;poDiscountsHandlingConfig&quot;,poDiscountsHandlingConfig},{&quot;matchVendorItemCode&quot;,matchVendorItemCode},{&quot;documentPath&quot;,documentPath},{&quot;approvalRequired&quot;,approvalRequired},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;miscValues&quot;,miscValues},{&quot;vendorId&quot;,vendorId}}]" ContinueOnError="True" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_23" OutputArguments="[poInvoiceResponseDictionary]" ResponseCode="[poInvoiceResponseCode]" WorkflowFile="[projectPath+&quot;\ProcessDeliveryNote.xaml&quot;]" />
      </If.Then>
      <If.Else>
        <If Condition="[invoiceType = &quot;POINVOICE&quot;]" sap2010:WorkflowViewState.IdRef="If_32">
          <If.Then>
            <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;DictOcrValues&quot;,DictOcrValues},{&quot;colemanAPI&quot;,colemanAPI},{&quot;logFile&quot;,logFile},{&quot;projectPath&quot;,projectPath},{&quot;chargeCode&quot;,chargeCode},{&quot;discountCode&quot;,discountCode},{&quot;division&quot;,division},{&quot;authUser&quot;,authUser},{&quot;APResp&quot;,APResp},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;poDiscountsHandlingConfig&quot;,poDiscountsHandlingConfig},{&quot;matchVendorItemCode&quot;,matchVendorItemCode},{&quot;approvalRequired&quot;,approvalRequired},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;miscValues&quot;,miscValues},{&quot;ListocrLineValues&quot;,ListocrLineValues},{&quot;vendorId&quot;,vendorId},{&quot;includeDatalake&quot;,includeDatalake}}]" ContinueOnError="True" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_37" OutputArguments="[poInvoiceResponseDictionary]" ResponseCode="[poInvoiceResponseCode]" WorkflowFile="[projectPath+&quot;\ProcessPOInvoiceAPI_Ind.xaml&quot;]" />
          </If.Then>
          <If.Else>
            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_26">
              <If Condition="[invoiceType = &quot;POEXPENSE&quot;]" sap2010:WorkflowViewState.IdRef="If_31">
                <If.Then>
                  <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;DictOcrValues&quot;,DictOcrValues},{&quot;colemanAPI&quot;,colemanAPI},{&quot;logFile&quot;,logFile},{&quot;projectPath&quot;,projectPath},{&quot;authUser&quot;,authUser},{&quot;miscValues&quot;,miscValues}}]" ContinueOnError="True" DisplayName="Expense PO Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_21" OutputArguments="[poInvoiceResponseDictionary]" ResponseCode="[poInvoiceResponseCode]" WorkflowFile="[projectPath+&quot;\ProcessExpenseWithPO.xaml&quot;]" />
                </If.Then>
                <If.Else>
                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_25">
                    <If Condition="[invoiceType = &quot;EXPENSE&quot;]" sap2010:WorkflowViewState.IdRef="If_30">
                      <If.Then>
                        <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;DictOcrValues&quot;,DictOcrValues},{&quot;logFile&quot;,logFile},{&quot;authUser&quot;,authUser},{&quot;APResp&quot;,APResp},{&quot;vendorId&quot;,vendorId},{&quot;approvalRequired&quot;,approvalRequired},{&quot;projectPath&quot;,projectPath},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;ListocrLineValues&quot;,ListocrLineValues},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;miscValues&quot;,miscValues},{&quot;GLCode&quot;,GLCode},{&quot;division&quot;,division}}]" ContinueOnError="True" DisplayName="Expense Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_22" OutputArguments="[poInvoiceResponseDictionary]" ResponseCode="[poInvoiceResponseCode]" WorkflowFile="[projectPath+&quot;\ProcessExpenseInvoice - Copy.xaml&quot;]" />
                      </If.Then>
                    </If>
                  </Sequence>
                </If.Else>
              </If>
            </Sequence>
          </If.Else>
        </If>
      </If.Else>
    </If>
    <If Condition="[poInvoiceResponseCode =200]" sap2010:WorkflowViewState.IdRef="If_6">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_11">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_82">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[APResp]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">
                <Literal x:TypeArguments="x:String" Value="" />
              </InArgument>
            </Assign.Value>
          </Assign>
          <If Condition="[division &lt;&gt; &quot;&quot; AND company &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_54">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_47">
                <Sequence.Variables>
                  <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="APRespRespDict" />
                  <Variable x:TypeArguments="x:Int32" Name="APRespRespCode" />
                </Sequence.Variables>
                <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;division&quot;,division},{&quot;company&quot;,company},{&quot;VendorID&quot;,vendorID}}]" ContinueOnError="True" DisplayName="AP resp Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_49" OutputArguments="[APRespRespDict]" ResponseCode="[APRespRespCode]" WorkflowFile="[projectPath+&quot;\APResp.xaml&quot;]" />
                <If Condition="[APRespRespCode = 200]" sap2010:WorkflowViewState.IdRef="If_53">
                  <If.Then>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_81">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[APResp]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[CType(APRespRespDict("APResp"), String)]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </If.Then>
                </If>
              </Sequence>
            </If.Then>
          </If>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_7">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[status]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[CType(poInvoiceResponseDictionary("status"), String)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_8">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[statusComments]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[CType(poInvoiceResponseDictionary("commentStatus"), String)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_14">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[vendorName]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[CType(poInvoiceResponseDictionary("vendorName"), String)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_79">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(x:String)">[approvalList]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(x:String)">[CType(poInvoiceResponseDictionary("approvalList"), List(Of String))]</InArgument>
            </Assign.Value>
          </Assign>
          <If Condition="[vendorId = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_46">
            <If.Then>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_15">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[CType(poInvoiceResponseDictionary("vendorId"), String)]</InArgument>
                </Assign.Value>
              </Assign>
            </If.Then>
          </If>
          <If Condition="[company = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_44">
            <If.Then>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_17">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[company]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[CType(poInvoiceResponseDictionary("company"), String)]</InArgument>
                </Assign.Value>
              </Assign>
            </If.Then>
          </If>
          <If Condition="[division = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_43">
            <If.Then>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_25">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[division]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[CType(poInvoiceResponseDictionary("division"), String)]</InArgument>
                </Assign.Value>
              </Assign>
            </If.Then>
          </If>
          <If Condition="[vendorName = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_11">
            <If.Then>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_16">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[vendorName]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[DictOcrValues("VENDOR_NAME").ToString]</InArgument>
                </Assign.Value>
              </Assign>
            </If.Then>
          </If>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_22">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[vendorName]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[vendorName.Replace(",","")]</InArgument>
            </Assign.Value>
          </Assign>
          <If Condition="[enableMessageBoxes]" sap2010:WorkflowViewState.IdRef="If_10">
            <If.Then>
              <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_2" Selection="OK" Text="[&quot;Status : &quot;+status+&quot; Comments :&quot;+statusComments]" />
            </If.Then>
          </If>
          <If Condition="[extractFromWidgetDatalake]" sap2010:WorkflowViewState.IdRef="If_18">
            <If.Then>
              <If Condition="[NOT includeDatalake]" sap2010:WorkflowViewState.IdRef="If_19">
                <If.Then>
                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_17">
                    <If Condition="[status = &quot;PONOTRECEIVED&quot;]" sap2010:WorkflowViewState.IdRef="If_20">
                      <If.Then>
                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_30">
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_35">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:Int32">[failureCount]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:Int32">1</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_39">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[statusDisp]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">Pending Process</InArgument>
                            </Assign.Value>
                          </Assign>
                        </Sequence>
                      </If.Then>
                      <If.Else>
                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_31">
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_34">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:Int32">[failureCount]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:Int32">0</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_38">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[statusDisp]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[Status]</InArgument>
                            </Assign.Value>
                          </Assign>
                        </Sequence>
                      </If.Else>
                    </If>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_29">
                      <If Condition="[statusComments.Contains(&quot;Bank account identity must be entered&quot;)]" sap2010:WorkflowViewState.IdRef="If_42">
                        <If.Then>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_48">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[status]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">NEEDSVERIFICATION</InArgument>
                            </Assign.Value>
                          </Assign>
                        </If.Then>
                      </If>
                    </Sequence>
                  </Sequence>
                </If.Then>
              </If>
            </If.Then>
          </If>
          <If Condition="[Status = &quot;SUCCESS&quot;]" sap2010:WorkflowViewState.IdRef="If_38">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_34">
                <Sequence.Variables>
                  <Variable x:TypeArguments="x:String" Name="correlationID" />
                  <Variable x:TypeArguments="x:String" Name="inYear" />
                  <Variable x:TypeArguments="x:String" Name="AYear" />
                </Sequence.Variables>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_45">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[CType(poInvoiceResponseDictionary("correlationID"), String)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_52">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[AYear]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[CType(poInvoiceResponseDictionary("AYear"), String)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_53">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[InYear]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[CType(poInvoiceResponseDictionary("InYear"), String)]</InArgument>
                  </Assign.Value>
                </Assign>
                <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;documentPath&quot;,documentPath},{&quot;poNumber&quot;,DictOcrValues(&quot;PO_NUMBER&quot;).ToString},{&quot;invoiceNumber&quot;,DictOcrValues(&quot;INVOICE_RECEIPT_ID&quot;).ToString},{&quot;companyNumber&quot;,company},{&quot;supplierNo&quot;,vendorId},{&quot;documentType&quot;,&quot;M3_SupplierInvoice&quot;},{&quot;logFile&quot;,logFile},{&quot;tenantID&quot;,tenantID},{&quot;division&quot;,division},{&quot;correlationID&quot;,correlationID},{&quot;InYear&quot;,InYear},{&quot;AYear&quot;,AYear}}]" ContinueOnError="True" DisplayName="IDM" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_32" WorkflowFile="[projectPath+&quot;\SendToIDM.xaml&quot;]" />
              </Sequence>
            </If.Then>
          </If>
        </Sequence>
      </If.Then>
      <If.Else>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_13">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_9">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[status]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_10">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[statusComments]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["Exception occured while processing the request"]</InArgument>
            </Assign.Value>
          </Assign>
        </Sequence>
      </If.Else>
    </If>
    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_6" Line="[&quot;Status : &quot;+status+&quot; Comments :&quot;+statusComments]" Source="[logFile]" />
    <If Condition="[extractFromWidgetDatalake AND NOT includeDatalake]" sap2010:WorkflowViewState.IdRef="If_51">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_46">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_65">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[fileName]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[Path.GetFileName(documentPath).Replace("'","").Replace("""","")]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_66">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[processId]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[fileName.substring(0,fileName.indexOf("."))+System.DateTime.Now.ToString("yyyyMMddHHmmssfff").tostring]</InArgument>
            </Assign.Value>
          </Assign>
          <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;documentPath&quot;,documentPath},{&quot;documentType&quot;,&quot;RPAExceptionHandling&quot;},{&quot;logFile&quot;,logFile},{&quot;tenantID&quot;,tenantID},{&quot;processID&quot;,processId}}]" ContinueOnError="True" DisplayName="Widget IDM" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_42" OutputArguments="[idmResponseDictionary]" WorkflowFile="[projectPath+&quot;\SendToWidgetIDM.xaml&quot;]" />
          <Assign sap2010:WorkflowViewState.IdRef="Assign_67">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[PID]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[CType(idmResponseDictionary("PID"), String)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_68">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(s:String[])">[Attribute_List]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(s:String[])" xml:space="preserve">[New List(Of String())() From {
    ({"", "Subtotal_Amount", DictOcrValues("SUBTOTAL").ToString, ""}),
    ({"", "PO_Number", DictOcrValues("PO_NUMBER").ToString, ""}),
    ({"", "Invoice_Type", invoiceType, ""}),
    ({"", "Invoice_Date", DictOcrValues("INVOICE_RECEIPT_DATE").ToString, ""}),
    ({"", "Additional_Attribute_1", "", ""}),
    ({"", "Updated_by_User", "N", ""}),
    ({"", "Last_Run_Time", System.DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"), ""}),
    ({"", "Invoice_Number", DictOcrValues("INVOICE_RECEIPT_ID").ToString, ""}),
    ({"", "Vendor_Name", DictOcrValues("VENDOR_NAME").ToString, ""}),
    ({"", "Vendor_Address", DictOcrValues("VENDOR_ADDRESS").ToString, ""}),
    ({"", "Vendor_Phone", DictOcrValues("VENDOR_PHONE").ToString, ""}),
    ({"", "Email_Subject", emailSubject, ""}),
    ({"", "Company", company, ""}),
    ({"", "DeliveryNote_Number", DictOcrValues("DELIVERY_NOTE_DATA").ToString, ""}),
    ({"", "Email_Received_Time", emailReceivedTime, ""}),
    ({"", "Discount_Amount", DictOcrValues("Invoice_Level_Discount_AMOUNT").ToString, ""}),
    ({"", "Status", Status, ""}),
    ({"", "Failure_Count", "", ""}),
    ({"", "Comments", statusComments, ""}),
    ({"", "Vendor_ID", vendorId, ""}),
    ({"", "Shipping_Charges", DictOcrValues("SHIPPING_AND_HANDLING_AMOUNT").ToString, ""}),
    ({"", "Tax_Amount", DictOcrValues("VAT_PERCENTAGE").ToString + "|" + DictOcrValues("VAT_AMOUNT").ToString, ""}),
    ({"", "Total_Amount", DictOcrValues("TOTAL").ToString, ""}),
    ({"", "File_Name", fileName, ""}),
    ({"", "Additional_Attribute_3", "", ""}),
    ({"", "Additional_Attribute_2", "", ""}),
    ({"", "GLCode", GLCode, ""}),
    ({"", "Division", division, ""}),
    ({"", "PID", PID, ""}),
    ({"","ProcessID",ProcessId,""}),
    ({"","APResp",APResp,""}),
    ({"","FileType",Right(fileName,3),""})
}]</InArgument>
            </Assign.Value>
          </Assign>
          <iaw:InvokeWorkflow OutputArguments="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;logicalId&quot;,datalakeAPILogicalId},{&quot;documentName&quot;,&quot;RPA_Process_Attributes&quot;},{&quot;AttributeList&quot;,Attribute_List},{&quot;Operation&quot;,&quot;insert&quot;},{&quot;Process_ID&quot;,processId},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;logFile&quot;,logFile}}]" ContinueOnError="False" DisplayName="Invoke SentToInvoiceProcessingResults_Attributes" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_43" ResponseCode="[attriResp]" WorkflowFile="[projectPath+&quot;\SentToInvoiceProcessingResults_Attributes.xaml&quot;]" />
          <If Condition="[attriResp = 200]" sap2010:WorkflowViewState.IdRef="If_49">
            <If.Then>
              <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;documentName&quot;,&quot;RPA_Process_Header&quot;},{&quot;logicalId&quot;,datalakeAPILogicalId},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;tenantID&quot;,tenantID},{&quot;Status_Display&quot;,Status},{&quot;RPA_Process_ID&quot;,processId},{&quot;Status&quot;,Status},{&quot;Comments&quot;,Statuscomments},{&quot;Last_Run_Time&quot;,System.DateTime.Now.ToString(&quot;yyyy-MM-ddTHH:mm:ss.fffZ&quot;)},{&quot;Process_Type&quot;,&quot;INVOICE PROCESSING&quot;},{&quot;Name&quot;,&quot;&quot;},{&quot;ERP&quot;,&quot;M3&quot;},{&quot;Variation_ID&quot;,&quot;1&quot;},{&quot;Updated_By&quot;,&quot;N&quot;},{&quot;Additional1&quot;,&quot;&quot;},{&quot;Additional2&quot;,&quot;&quot;},{&quot;failureCount&quot;,failureCount.ToString},{&quot;logFile&quot;,logFile}}]" ContinueOnError="True" DisplayName="Invoke SentToInvoiceProcessingResults_Headers" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_44" WorkflowFile="[projectPath+&quot;\SentToInvoiceProcessingResults_Headers.xaml&quot;]" />
            </If.Then>
          </If>
          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_45">
            <Sequence.Variables>
              <Variable x:TypeArguments="s:String[]" Name="strArr" />
              <Variable x:TypeArguments="scg:List(s:String[])" Name="Line_List" />
              <Variable x:TypeArguments="x:Int32" Name="y" />
            </Sequence.Variables>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_69">
              <Assign.To>
                <OutArgument x:TypeArguments="scg:List(s:String[])">[Line_List]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="scg:List(s:String[])">[New List(Of String())]</InArgument>
              </Assign.Value>
            </Assign>
            <If Condition="[ListocrLineValues.count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_50">
              <If.Then>
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_43">
                  <ForEach x:TypeArguments="scg:Dictionary(x:String, x:Object)" DisplayName="ForEach&lt;Dictionary&lt;String,Object&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_4" Values="[ListocrLineValues]">
                    <ActivityAction x:TypeArguments="scg:Dictionary(x:String, x:Object)">
                      <ActivityAction.Argument>
                        <DelegateInArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="item1" />
                      </ActivityAction.Argument>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_42">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_83">
                          <Assign.To>
                            <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary(Of String,Object)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_84">
                          <Assign.To>
                            <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[item1]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_70">
                          <Assign.To>
                            <OutArgument x:TypeArguments="s:String[]">[strArr]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="s:String[]">[New String(2) {"","",""}]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_71">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[strArr(0)]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">
                              <Literal x:TypeArguments="x:String" Value="" />
                            </InArgument>
                          </Assign.Value>
                        </Assign>
                        <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_1">
                          <iad:CommentOut.Activities>
                            <Assign DisplayName="Replace DN with PO if DN Empty" sap2010:WorkflowViewState.IdRef="Assign_86">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Object">[LinesDict("DELIVERY_NOTE_NUMBER")]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(LinesDict("DELIVERY_NOTE_NUMBER").ToString = "" Or LinesDict("DELIVERY_NOTE_NUMBER").ToString = "''", 
   LinesDict("PO_Number").ToString, 
   LinesDict("DELIVERY_NOTE_NUMBER").ToString)]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </iad:CommentOut.Activities>
                        </iad:CommentOut>
                        <Assign DisplayName="Replace DN with Null if ''" sap2010:WorkflowViewState.IdRef="Assign_87">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Object">[LinesDict("DELIVERY_NOTE_NUMBER")]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(LinesDict("DELIVERY_NOTE_NUMBER").ToString = "" Or LinesDict("DELIVERY_NOTE_NUMBER").ToString = "''", 
   "", 
   LinesDict("DELIVERY_NOTE_NUMBER").ToString)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign DisplayName="Replace PO with null if ''" sap2010:WorkflowViewState.IdRef="Assign_88">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Object">[LinesDict("PO_Number")]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Object" xml:space="preserve">[If(LinesDict("PO_Number").ToString = "" Or LinesDict("PO_Number").ToString = "''", 
   "", 
   LinesDict("PO_Number").ToString)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <If Condition="[LinesDict(&quot;DESCRIPTION&quot;).ToString = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_52">
                          <If.Then>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_76">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[strArr(1)]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[LinesDict("SUPPLIER_ITEM_CODE").ToString+"("+LinesDict("DELIVERY_NOTE_NUMBER").ToString+","+LinesDict("PO_Number").ToString+",)"+"||"+LinesDict("DESCRIPTION").ToString + "||" + LinesDict("QUANTITY").ToString + "||" + LinesDict("UNIT_PRICE").ToString + "||" + LinesDict("LINE_AMOUNT").ToString + "||InValid"]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </If.Then>
                          <If.Else>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_77">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[strArr(1)]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[LinesDict("SUPPLIER_ITEM_CODE").ToString+"("+LinesDict("DELIVERY_NOTE_NUMBER").ToString+","+LinesDict("PO_Number").ToString+",)"+"||"+LinesDict("DESCRIPTION").ToString + "||" + LinesDict("QUANTITY").ToString + "||" + LinesDict("UNIT_PRICE").ToString + "||" + LinesDict("LINE_AMOUNT").ToString + "||Valid"]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </If.Else>
                        </If>
                        <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_5" MethodName="Add">
                          <InvokeMethod.TargetObject>
                            <InArgument x:TypeArguments="scg:List(s:String[])">[Line_List]</InArgument>
                          </InvokeMethod.TargetObject>
                          <InArgument x:TypeArguments="s:String[]">[strArr]</InArgument>
                        </InvokeMethod>
                      </Sequence>
                    </ActivityAction>
                  </ForEach>
                  <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;logicalId&quot;,datalakeAPILogicalId},{&quot;documentName&quot;,&quot;RPA_Process_Lines&quot;},{&quot;AttributeList&quot;,Line_List},{&quot;Operation&quot;,&quot;insert&quot;},{&quot;Process_ID&quot;,processId},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;logFile&quot;,logFile}}]" ContinueOnError="False" DisplayName="Invoke SentToInvoiceProcessingResults_Lines" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_45" WorkflowFile="[projectPath+&quot;\SentToInvoiceProcessingResults_LineData.xaml&quot;]" />
                </Sequence>
              </If.Then>
              <If.Else>
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_44">
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_73">
                    <Assign.To>
                      <OutArgument x:TypeArguments="s:String[]">[strArr]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="s:String[]">[New String(2) {"","",""}]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_74">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[strArr(0)]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">
                        <Literal x:TypeArguments="x:String" Value="" />
                      </InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_75">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[strArr(1)]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">||||||||||InValid</InArgument>
                    </Assign.Value>
                  </Assign>
                  <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_6" MethodName="Add">
                    <InvokeMethod.TargetObject>
                      <InArgument x:TypeArguments="scg:List(s:String[])">[Line_List]</InArgument>
                    </InvokeMethod.TargetObject>
                    <InArgument x:TypeArguments="s:String[]">[strArr]</InArgument>
                  </InvokeMethod>
                </Sequence>
              </If.Else>
            </If>
          </Sequence>
          <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_2">
            <iad:CommentOut.Activities>
              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_5" Line="---------------------------------------------------------------------------------------------" Source="[logFile]" />
            </iad:CommentOut.Activities>
          </iad:CommentOut>
        </Sequence>
      </If.Then>
    </If>
    <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;status&quot;,status},{&quot;statusComments&quot;,statusComments},{&quot;distributionType&quot;,distributionType},{&quot;userIdentifier&quot;,userIdentifier},{&quot;emailSubject&quot;,emailSubject},{&quot;emailReceivedTime&quot;,emailReceivedTime},{&quot;tenantID&quot;,tenantID},{&quot;invoiceNumber&quot;,DictOcrValues(&quot;INVOICE_RECEIPT_ID&quot;).ToString},{&quot;poNumber&quot;,DictOcrValues(&quot;PO_NUMBER&quot;).ToString},{&quot;subTotal&quot;,DictOcrValues(&quot;SUBTOTAL&quot;).ToString},{&quot;total&quot;,DictOcrValues(&quot;TOTAL&quot;).ToString},{&quot;drillbackLink&quot;,&quot;LogicalId=lid://infor.m3.m3&amp;program=APS450&amp;fieldNames=W2OBKV,&quot;+DictOcrValues(&quot;INVOICE_RECEIPT_ID&quot;).ToString+&quot;,W3OBKV,,W1OBKV,&quot;+ division+&quot;&amp;includeStartPanel=True&amp;source=MForms&amp;requirePanel=True&amp;sortingOrder=2&amp;view=STD02-01&amp;tableName=FAPIBH&amp;keys=E5CONO,&quot;+company+&quot;,E5DIVI,&quot;+division+&quot;,E5INBN,+&amp;parameters=XXSINO,&quot;+DictOcrValues(&quot;INVOICE_RECEIPT_ID&quot;).ToString+&quot;&amp;startpanel=B&quot;},{&quot;invoiceDate&quot;,DictOcrValues(&quot;INVOICE_RECEIPT_DATE&quot;).ToString},{&quot;tax&quot;,DictOcrValues(&quot;VAT_AMOUNT&quot;).ToString},{&quot;charges&quot;,DictOcrValues(&quot;SHIPPING_AND_HANDLING_AMOUNT&quot;).ToString},{&quot;discount&quot;,DictOcrValues(&quot;Invoice_Level_Discount_AMOUNT&quot;).ToString},{&quot;deliveryNote&quot;,DictOcrValues(&quot;DELIVERY_NOTE_DATA&quot;).ToString} ,{&quot;miscValues&quot;,miscValues},{&quot;APResp&quot;,APResp}}]" ContinueOnError="True" DisplayName="Send Notification" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_47" WorkflowFile="[projectPath+&quot;\SendNotification.xaml&quot;]" />
    <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;documentPath&quot;,documentPath},{&quot;configurationFolder&quot;,configurationFolder},{&quot;status&quot;,status}}]" ContinueOnError="True" DisplayName="Move to Success/Failure" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_48" WorkflowFile="[projectpath+&quot;\&quot;+&quot;MoveFileToSuccessFailureFolder.xaml&quot;]" />
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_18" sap:VirtualizedContainerService.HintSize="1183,60" />
      <sap2010:ViewStateData Id="Assign_78" sap:VirtualizedContainerService.HintSize="1183,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_23" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_37" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_21" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_22" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_30" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Sequence_25" sap:VirtualizedContainerService.HintSize="486,332">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_31" sap:VirtualizedContainerService.HintSize="711,480" />
      <sap2010:ViewStateData Id="Sequence_26" sap:VirtualizedContainerService.HintSize="733,604">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_32" sap:VirtualizedContainerService.HintSize="958,752" />
      <sap2010:ViewStateData Id="If_28" sap:VirtualizedContainerService.HintSize="1183,900">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_82" sap:VirtualizedContainerService.HintSize="828,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_49" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_81" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_53" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="Sequence_47" sap:VirtualizedContainerService.HintSize="486,402">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_54" sap:VirtualizedContainerService.HintSize="828,556" />
      <sap2010:ViewStateData Id="Assign_7" sap:VirtualizedContainerService.HintSize="828,62" />
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="828,62" />
      <sap2010:ViewStateData Id="Assign_14" sap:VirtualizedContainerService.HintSize="828,62" />
      <sap2010:ViewStateData Id="Assign_79" sap:VirtualizedContainerService.HintSize="828,62" />
      <sap2010:ViewStateData Id="Assign_15" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_46" sap:VirtualizedContainerService.HintSize="828,216" />
      <sap2010:ViewStateData Id="Assign_17" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_44" sap:VirtualizedContainerService.HintSize="828,216" />
      <sap2010:ViewStateData Id="Assign_25" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_43" sap:VirtualizedContainerService.HintSize="828,216" />
      <sap2010:ViewStateData Id="Assign_16" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_11" sap:VirtualizedContainerService.HintSize="828,216" />
      <sap2010:ViewStateData Id="Assign_22" sap:VirtualizedContainerService.HintSize="828,62" />
      <sap2010:ViewStateData Id="MessageBox_2" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_10" sap:VirtualizedContainerService.HintSize="828,214" />
      <sap2010:ViewStateData Id="Assign_35" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_39" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_30" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_34" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_38" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_31" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_20" sap:VirtualizedContainerService.HintSize="554,442" />
      <sap2010:ViewStateData Id="Assign_48" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_42" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="Sequence_29" sap:VirtualizedContainerService.HintSize="554,340">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_17" sap:VirtualizedContainerService.HintSize="576,946">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_19" sap:VirtualizedContainerService.HintSize="702,1100" />
      <sap2010:ViewStateData Id="If_18" sap:VirtualizedContainerService.HintSize="828,1254" />
      <sap2010:ViewStateData Id="Assign_45" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_52" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_53" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_32" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_34" sap:VirtualizedContainerService.HintSize="264,452">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_38" sap:VirtualizedContainerService.HintSize="828,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_11" sap:VirtualizedContainerService.HintSize="850,3956.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_13" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_6" sap:VirtualizedContainerService.HintSize="1183,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_6" sap:VirtualizedContainerService.HintSize="1183,22" />
      <sap2010:ViewStateData Id="Assign_65" sap:VirtualizedContainerService.HintSize="894,60" />
      <sap2010:ViewStateData Id="Assign_66" sap:VirtualizedContainerService.HintSize="894,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_42" sap:VirtualizedContainerService.HintSize="894,22" />
      <sap2010:ViewStateData Id="Assign_67" sap:VirtualizedContainerService.HintSize="894,60" />
      <sap2010:ViewStateData Id="Assign_68" sap:VirtualizedContainerService.HintSize="894,64" />
      <sap2010:ViewStateData Id="InvokeWorkflow_43" sap:VirtualizedContainerService.HintSize="894,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_44" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_49" sap:VirtualizedContainerService.HintSize="894,208" />
      <sap2010:ViewStateData Id="Assign_69" sap:VirtualizedContainerService.HintSize="872,60" />
      <sap2010:ViewStateData Id="Assign_83" sap:VirtualizedContainerService.HintSize="509,60" />
      <sap2010:ViewStateData Id="Assign_84" sap:VirtualizedContainerService.HintSize="509,60" />
      <sap2010:ViewStateData Id="Assign_70" sap:VirtualizedContainerService.HintSize="509,60" />
      <sap2010:ViewStateData Id="Assign_71" sap:VirtualizedContainerService.HintSize="509,60" />
      <sap2010:ViewStateData Id="Assign_86" sap:VirtualizedContainerService.HintSize="242,64" />
      <sap2010:ViewStateData Id="CommentOut_1" sap:VirtualizedContainerService.HintSize="509,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_87" sap:VirtualizedContainerService.HintSize="509,64" />
      <sap2010:ViewStateData Id="Assign_88" sap:VirtualizedContainerService.HintSize="509,64" />
      <sap2010:ViewStateData Id="Assign_76" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_77" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_52" sap:VirtualizedContainerService.HintSize="509,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeMethod_5" sap:VirtualizedContainerService.HintSize="509,128" />
      <sap2010:ViewStateData Id="Sequence_42" sap:VirtualizedContainerService.HintSize="531,1206">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_4" sap:VirtualizedContainerService.HintSize="561,1354" />
      <sap2010:ViewStateData Id="InvokeWorkflow_45" sap:VirtualizedContainerService.HintSize="561,22" />
      <sap2010:ViewStateData Id="Sequence_43" sap:VirtualizedContainerService.HintSize="583,1540">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_73" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_74" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_75" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="InvokeMethod_6" sap:VirtualizedContainerService.HintSize="242,128" />
      <sap2010:ViewStateData Id="Sequence_44" sap:VirtualizedContainerService.HintSize="264,552">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_50" sap:VirtualizedContainerService.HintSize="872,1688">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_45" sap:VirtualizedContainerService.HintSize="894,1912">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_2" sap:VirtualizedContainerService.HintSize="894,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_46" sap:VirtualizedContainerService.HintSize="916,2910">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_51" sap:VirtualizedContainerService.HintSize="1183,3058" />
      <sap2010:ViewStateData Id="InvokeWorkflow_47" sap:VirtualizedContainerService.HintSize="1183,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_48" sap:VirtualizedContainerService.HintSize="1183,22" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="1205,4599">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="1245,4679" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>