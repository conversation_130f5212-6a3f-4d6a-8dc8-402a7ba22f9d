﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:sl="clr-namespace:System.Linq;assembly=System.Core"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="colemanAPI" Type="InArgument(x:String)" />
    <x:Property Name="commentStatus" Type="OutArgument(x:String)" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="status" Type="OutArgument(x:String)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="vendorId" Type="InOutArgument(x:String)" />
    <x:Property Name="vendorName" Type="OutArgument(x:String)" />
    <x:Property Name="company" Type="InOutArgument(x:String)" />
    <x:Property Name="chargeCode" Type="InArgument(x:String)" />
    <x:Property Name="discountCode" Type="InArgument(x:String)" />
    <x:Property Name="authUser" Type="InArgument(x:String)" />
    <x:Property Name="division" Type="InOutArgument(x:String)" />
    <x:Property Name="vatCodeConfig" Type="InArgument(x:String)" />
    <x:Property Name="poDiscountsHandlingConfig" Type="InArgument(x:Boolean)" />
    <x:Property Name="ocrText" Type="InArgument(njl:JToken)" />
    <x:Property Name="matchVendorItemCode" Type="InArgument(x:Boolean)" />
    <x:Property Name="documentPath" Type="InArgument(x:String)" />
    <x:Property Name="approvalRequired" Type="InArgument(x:Boolean)" />
    <x:Property Name="approvalWorkflow" Type="InArgument(x:String)" />
    <x:Property Name="checkAmountBussinessRule" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="correlationID" Type="OutArgument(x:String)" />
    <x:Property Name="InYear" Type="OutArgument(x:String)" />
    <x:Property Name="AYear" Type="OutArgument(x:String)" />
    <x:Property Name="approvalList" Type="OutArgument(scg:List(x:String))" />
    <x:Property Name="APResp" Type="InArgument(x:String)" />
    <x:Property Name="DictOcrValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="ListocrLineValues" Type="InArgument(scg:List(scg:Dictionary(x:String, x:Object)))" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.Text.RegularExpressions</x:String>
      <x:String>System.Globalization</x:String>
      <x:String>Microsoft.VisualBasic</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
      <AssemblyReference>System.Collections.Immutable</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="ProcessPOInvoiceAPISequence" sap2010:WorkflowViewState.IdRef="Sequence_17">
    <Sequence.Variables>
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj1" />
      <Variable x:TypeArguments="njl:JToken" Name="out1" />
      <Variable x:TypeArguments="x:String" Name="req1" />
      <Variable x:TypeArguments="x:String" Name="pono" />
      <Variable x:TypeArguments="x:Int32" Name="count1" />
      <Variable x:TypeArguments="x:String" Name="req2" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj2" />
      <Variable x:TypeArguments="njl:JToken" Name="out2" />
      <Variable x:TypeArguments="scg:List(s:String[])" Name="M3TotalTableRows" />
      <Variable x:TypeArguments="x:Int32" Name="i" />
      <Variable x:TypeArguments="s:String[]" Name="M3Values" />
      <Variable x:TypeArguments="x:String" Name="cono" />
      <Variable x:TypeArguments="x:String" Name="cucd" />
      <Variable x:TypeArguments="x:String" Name="SupplierNo" />
      <Variable x:TypeArguments="x:String" Name="APIString" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode1" />
      <Variable x:TypeArguments="x:String" Name="pyme" />
      <Variable x:TypeArguments="scg:List(x:String)" Name="invoicesItemNumbers" />
      <Variable x:TypeArguments="x:Boolean" Name="allLinesReceived" />
      <Variable x:TypeArguments="scg:IEnumerable(sl:IGrouping(x:String, s:String[]))" Name="transDateGroups" />
      <Variable x:TypeArguments="scg:Dictionary(x:String, scg:List(s:String[]))" Name="transDateDictionary" />
      <Variable x:TypeArguments="x:Int32" Name="POocrWorkflowStatus" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="POocrWorkflowOutput" />
      <Variable x:TypeArguments="scg:List(x:String)" Name="exportResult" />
      <Variable x:TypeArguments="x:String" Name="tepy" />
      <Variable x:TypeArguments="x:String" Name="bkid" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="ColemanOutPut" />
      <Variable x:TypeArguments="x:Int32" Name="ColemanRespCode" />
      <Variable x:TypeArguments="njl:JToken" Name="httpOut" />
      <Variable x:TypeArguments="x:String" Name="ivdate" />
      <Variable x:TypeArguments="x:String" Name="inbnValue" />
      <Variable x:TypeArguments="x:Boolean" Name="InvoiceAlreadyExists" />
      <Variable x:TypeArguments="x:Boolean" Name="chargeExists" />
      <Variable x:TypeArguments="scg:List(x:String)" Name="PONumbers" />
      <Variable x:TypeArguments="scg:List(s:String[])" Name="M3TotalTableRows1" />
      <Variable x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="LinesDict" />
      <Variable x:TypeArguments="x:String" Name="countryCode" />
      <Variable x:TypeArguments="x:String" Name="discountTerms" />
    </Sequence.Variables>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_893">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(x:String)">[approvalList]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(x:String)">[new list(Of string)]</InArgument>
      </Assign.Value>
    </Assign>
    <Sequence DisplayName="Fiscal year Sequence" sap2010:WorkflowViewState.IdRef="Sequence_497">
      <Sequence.Variables>
        <Variable x:TypeArguments="iru:ResponseObject" Name="respForFiscalYear" />
        <Variable x:TypeArguments="x:Int32" Name="respForFiscalYearCode" />
      </Sequence.Variables>
      <Assign DisplayName="Assign ivdate" sap2010:WorkflowViewState.IdRef="Assign_855">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[ivdate]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">[DateTime.ParseExact(DictOcrValues("INVOICE_RECEIPT_DATE").ToString,"dd/MM/yyyy",Nothing).ToString("yyyyMMdd")]</InArgument>
        </Assign.Value>
      </Assign>
      <Assign sap2010:WorkflowViewState.IdRef="Assign_856">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[InYear]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">[ivdate.SubString(0,4)]</InArgument>
        </Assign.Value>
      </Assign>
      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Get Fiscal year IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_23" Response="[respForFiscalYear]" StatusCode="[respForFiscalYearCode]" Url="[tenantID+&quot;M3/m3api-rest/v2/execute/CRS900MI/GetSysCalDate&quot;]">
        <iai:IONAPIRequestWizard.Headers>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>Accept</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>application/json</x:String>
            </scg:List>
          </scg:List>
        </iai:IONAPIRequestWizard.Headers>
        <iai:IONAPIRequestWizard.QueryParameters>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>YMD8</x:String>
              <x:String>DIVI</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>ivdate</x:String>
              <x:String>division</x:String>
            </scg:List>
          </scg:List>
        </iai:IONAPIRequestWizard.QueryParameters>
      </iai:IONAPIRequestWizard>
      <If Condition="[respForFiscalYearCode = 200]" sap2010:WorkflowViewState.IdRef="If_434">
        <If.Then>
          <If Condition="[respForFiscalYear.ReadAsJson(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_433">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_495">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_857">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[AYear]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[ivdate.SubString(0,4)]</InArgument>
                  </Assign.Value>
                </Assign>
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_496">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_858">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[AYear]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[respForFiscalYear.ReadAsJson("results")(0)("records")(0)("CYP1").ToString.Substring(0,4)]</InArgument>
                  </Assign.Value>
                </Assign>
              </Sequence>
            </If.Else>
          </If>
        </If.Then>
      </If>
    </Sequence>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_1">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[pono]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[DictOcrValues("PO_NUMBER").ToString]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_630">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(x:String)">[PONumbers]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_631">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(x:String)">[PONumbers]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(x:String)">[DictOcrValues("PO_NUMBER").ToString.split(","c).ToList]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_685">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[pono]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[PONUmbers(0)]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_136">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[vendorId]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_686">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows1]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(s:String[])">[new list(Of String())]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_687">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(s:String[])">[new list(Of String())]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_137">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[vendorName]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[DictOcrValues("VENDOR_NAME").ToString]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_602">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[division]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[division]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_603">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[company]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[miscValues("company").ToString]</InArgument>
      </Assign.Value>
    </Assign>
    <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;pono&quot;,pono}}]" ContinueOnError="True" DisplayName="PO details Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_10" OutputArguments="[POocrWorkflowOutput]" ResponseCode="[POocrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\ExportMI.xaml&quot;]" />
    <If Condition="[POocrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_270">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_287">
          <Sequence.Variables>
            <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="vendorOcrWorkflowOutput" />
            <Variable x:TypeArguments="x:Int32" Name="vendorOcrWorkflowStatus" />
            <Variable x:TypeArguments="scg:List(x:String)" Name="vendorResult" />
          </Sequence.Variables>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_528">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(x:String)">[exportResult]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(x:String)">[CType(POocrWorkflowOutput("result"), List(Of String))]</InArgument>
            </Assign.Value>
          </Assign>
          <If Condition="[exportResult.count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_273">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_310">
                <Sequence.Variables>
                  <Variable x:TypeArguments="x:Int32" Name="invoiceExistsOcrWorkflowStatus" />
                  <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="invoiceExistsOcrWorkflowOutput" />
                  <Variable x:TypeArguments="x:String" Name="colAmount" />
                </Sequence.Variables>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_859">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[exportResult(2)]</InArgument>
                  </Assign.Value>
                </Assign>
                <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;SupInvoiceNo&quot;,DictOcrValues(&quot;INVOICE_RECEIPT_ID&quot;).ToString},{&quot;division&quot;,division},{&quot;AYear&quot;,AYear},{&quot;vendorID&quot;,vendorId}}]" ContinueOnError="True" DisplayName="VerifyInvoiceExists Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_17" OutputArguments="[invoiceExistsOcrWorkflowOutput]" ResponseCode="[invoiceExistsOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\VerifyInvoiceExists.xaml&quot;]" />
                <Assign sap2010:WorkflowViewState.IdRef="Assign_579">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:Boolean">[InvoiceAlreadyExists]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:Boolean">[CType(invoiceExistsOcrWorkflowOutput("invoiceExist"),boolean)]</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[NOT InvoiceAlreadyExists]" sap2010:WorkflowViewState.IdRef="If_286">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_291">
                      <Sequence.Variables>
                        <Variable x:TypeArguments="x:Boolean" Name="DNinM3exists" />
                        <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="AddHeadOcrWorkflowOutput" />
                      </Sequence.Variables>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_529">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[company]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[exportResult(0)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_530">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[division]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[exportResult(1)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_531">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[exportResult(2)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_532">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[cucd]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[exportResult(3)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_533">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[tepy]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[exportResult(4)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_534">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[pyme]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[exportResult(5)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantId&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;vendorId&quot;,vendorId}}]" ContinueOnError="True" DisplayName="Vendor Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_11" OutputArguments="[vendorOcrWorkflowOutput]" ResponseCode="[vendorOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\getvendordetails.xaml&quot;]" />
                      <If Condition="[vendorOcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_271">
                        <If.Then>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_290">
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_294">
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_546">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[CType(vendorOcrWorkflowOutput("Status"), String)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_547">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[CType(vendorOcrWorkflowOutput("commentStatus"), String)]</InArgument>
                                </Assign.Value>
                              </Assign>
                            </Sequence>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_540">
                              <Assign.To>
                                <OutArgument x:TypeArguments="scg:List(x:String)">[vendorResult]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="scg:List(x:String)">[CType(vendorOcrWorkflowOutput("result"), List(Of String))]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_538">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[vendorName]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[vendorResult(0)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_545">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[bkid]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[vendorResult(2)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_931">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[countryCode]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[vendorResult(3)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_1056">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[discountTerms]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[vendorResult(4)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_295">
                              <Sequence.Variables>
                                <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="BROcrWorkflowOutput" />
                                <Variable x:TypeArguments="x:Int32" Name="BROcrWorkflowStatus" />
                                <Variable x:TypeArguments="x:String" Name="tolAmt" />
                                <Variable x:TypeArguments="x:String" Name="tolPercentage" />
                                <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="AddLineOcrWorkflowOutput" />
                                <Variable x:TypeArguments="x:Int32" Name="AddLineOcrWorkflowStatus" />
                                <Variable x:TypeArguments="x:Int32" Name="validateStatus" />
                              </Sequence.Variables>
                              <If Condition="[miscValues(&quot;useBusinessRuleForTelerance&quot;).ToString.ToLower =&quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_303">
                                <If.Then>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_328">
                                    <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;VendorID&quot;,vendorId},{&quot;division&quot;,division},{&quot;businessRule&quot;,miscValues(&quot;businessRuleForTolerance&quot;).ToString}}]" ContinueOnError="True" DisplayName="Percentage &amp; amt Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_13" OutputArguments="[BROcrWorkflowOutput]" ResponseCode="[BROcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\DivisionVendorTolerance.xaml&quot;]" />
                                    <If Condition="[BROcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_274">
                                      <If.Then>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_296">
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_548">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[tolPercentage]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[CType(BROcrWorkflowOutput("percentage"), String)]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_549">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[tolAmt]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[CType(BROcrWorkflowOutput("amount"), String)]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                        </Sequence>
                                      </If.Then>
                                      <If.Else>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_297">
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_550">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">Percentage and tolerance amount is not obtained</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_551">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">VERIFICATION</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_106" Line="[commentStatus]" Source="[logfile]" />
                                        </Sequence>
                                      </If.Else>
                                    </If>
                                  </Sequence>
                                </If.Then>
                                <If.Else>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_329">
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_616">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[tolPercentage]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">100</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_617">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[tolAmt]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">100</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                  </Sequence>
                                </If.Else>
                              </If>
                              <If Condition="[tolPercentage = &quot;&quot; AND tolAmt = &quot;&quot;]" DisplayName="If - Step 1" sap2010:WorkflowViewState.IdRef="If_275">
                                <If.Then>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_298">
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_552">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">Percentage and tolerance amount is not obtained</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_553">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">NEEDSVERIFICATION</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_107" Line="[commentStatus]" Source="[logfile]" />
                                  </Sequence>
                                </If.Then>
                                <If.Else>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_346">
                                    <Sequence.Variables>
                                      <Variable x:TypeArguments="x:String" Name="poList" />
                                      <Variable x:TypeArguments="x:Int32" Name="AddHeadOcrWorkflowStatus" />
                                    </Sequence.Variables>
                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_299">
                                      <Sequence.Variables>
                                        <Variable x:TypeArguments="x:Int32" Name="linesOcrWorkflowStatus" />
                                        <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="linesOcrWorkflowOutput" />
                                        <Variable x:TypeArguments="x:Boolean" Name="poExists" />
                                      </Sequence.Variables>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_878">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:Boolean">[poExists]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_51" Values="[PONumbers]">
                                        <ActivityAction x:TypeArguments="x:String">
                                          <ActivityAction.Argument>
                                            <DelegateInArgument x:TypeArguments="x:String" Name="pono" />
                                          </ActivityAction.Argument>
                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_347">
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_556">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String" xml:space="preserve">["F2PNLI,F2REPN,F2SCOC,F2RPQA,F2TRDT,F2ITNO,F2CONO,F2CUCD,F2DIVI,F2SUNO,F2IVQA,F2PUNO,F2SUDO,F2RCAC,F2RPQT,F2SCOP,F2SERA,F2IVQT,F2ICAC  from FGRECL where F2DIVI = " + DIVISION+" AND F2PUNO = " + pono + " and F2IMST != 9 "]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;miscValues&quot;,miscValues},{&quot;req2&quot;,req2}}]" ContinueOnError="True" DisplayName="Lines extraction Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_12" OutputArguments="[linesOcrWorkflowOutput]" ResponseCode="[linesOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\LinesExtractionWithPO.xaml&quot;]" />
                                            <If Condition="[linesOcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_314">
                                              <If.Then>
                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_348">
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_632">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows1]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="scg:List(s:String[])">[CType(linesOcrWorkflowOutput("M3TotalTableRows"), List(Of String()))]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_633">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:Boolean">[DNinM3exists]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:Boolean">[CType(linesOcrWorkflowOutput("DNinM3exists"), Boolean)]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <If Condition="[M3TotalTableRows1.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_315">
                                                    <If.Then>
                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_354">
                                                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_118" Line="[&quot;Receipt lines for the purchase order &quot; +pono.trim + &quot; have been extracted.&quot;]" Source="[logfile]" />
                                                        <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_53" Values="[M3TotalTableRows1]">
                                                          <ActivityAction x:TypeArguments="s:String[]">
                                                            <ActivityAction.Argument>
                                                              <DelegateInArgument x:TypeArguments="s:String[]" Name="item" />
                                                            </ActivityAction.Argument>
                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_353">
                                                              <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_27" MethodName="Add">
                                                                <InvokeMethod.TargetObject>
                                                                  <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</InArgument>
                                                                </InvokeMethod.TargetObject>
                                                                <InArgument x:TypeArguments="s:String[]">[item]</InArgument>
                                                              </InvokeMethod>
                                                            </Sequence>
                                                          </ActivityAction>
                                                        </ForEach>
                                                      </Sequence>
                                                    </If.Then>
                                                    <If.Else>
                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_503">
                                                        <Sequence.Variables>
                                                          <Variable x:TypeArguments="x:String" Name="po" />
                                                        </Sequence.Variables>
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_863">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:String">[po]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:String">[po + pono.trim + ", "]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <If Condition="[DNinM3exists]" sap2010:WorkflowViewState.IdRef="If_436">
                                                          <If.Then>
                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_499">
                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_864">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="x:String" xml:space="preserve">["Lines already invoiced for po " +  po.Substring(0,po.Length-2)]</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_865">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_150" Line="[&quot;Lines already invoiced for po: &quot; + pono.trim]" Source="[logfile]" />
                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_866">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="x:Boolean">[poExists]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                            </Sequence>
                                                          </If.Then>
                                                          <If.Else>
                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_502">
                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_504">
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_885">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:String" xml:space="preserve">["F2PNLI,F2REPN,F2SCOC,F2RPQA,F2TRDT,F2ITNO,F2CONO,F2CUCD,F2DIVI,F2SUNO,F2IVQA,F2PUNO,F2SUDO,F2RCAC,F2RPQT,F2SCOP,F2SERA,F2IVQT,F2ICAC  from FGRECL where F2DIVI = " + DIVISION+" AND F2PUNO = " + pono]</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                                <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;miscValues&quot;,miscValues},{&quot;req2&quot;,req2}}]" ContinueOnError="True" DisplayName="Lines extraction Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_48" OutputArguments="[linesOcrWorkflowOutput]" ResponseCode="[linesOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\LinesExtractionWithPO.xaml&quot;]" />
                                                                <If Condition="[linesOcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_440">
                                                                  <If.Then>
                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_508">
                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_886">
                                                                        <Assign.To>
                                                                          <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows1]</OutArgument>
                                                                        </Assign.To>
                                                                        <Assign.Value>
                                                                          <InArgument x:TypeArguments="scg:List(s:String[])">[CType(linesOcrWorkflowOutput("M3TotalTableRows"), List(Of String()))]</InArgument>
                                                                        </Assign.Value>
                                                                      </Assign>
                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_887">
                                                                        <Assign.To>
                                                                          <OutArgument x:TypeArguments="x:Boolean">[DNinM3exists]</OutArgument>
                                                                        </Assign.To>
                                                                        <Assign.Value>
                                                                          <InArgument x:TypeArguments="x:Boolean">[CType(linesOcrWorkflowOutput("DNinM3exists"), Boolean)]</InArgument>
                                                                        </Assign.Value>
                                                                      </Assign>
                                                                      <If Condition="[M3TotalTableRows1.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_441">
                                                                        <If.Else>
                                                                          <If Condition="[DNinM3exists]" sap2010:WorkflowViewState.IdRef="If_442">
                                                                            <If.Then>
                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_509">
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_888">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String" xml:space="preserve">["Lines already invoiced for po " +  po.Substring(0,po.Length-2)]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_889">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_154" Line="[&quot;Lines already invoiced for po: &quot; + pono.trim]" Source="[logfile]" />
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_890">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:Boolean">[poExists]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                              </Sequence>
                                                                            </If.Then>
                                                                            <If.Else>
                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_510">
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_875">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">["No receipts available for the given po: " + po.Substring(0,po.Length-2)]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_876">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">PONOTRECEIVED</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_152" Line="[&quot;No receipts available for the given po: &quot; + pono.trim]" Source="[logfile]" />
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_877">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:Boolean">[poExists]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                              </Sequence>
                                                                            </If.Else>
                                                                          </If>
                                                                        </If.Else>
                                                                      </If>
                                                                    </Sequence>
                                                                  </If.Then>
                                                                </If>
                                                              </Sequence>
                                                            </Sequence>
                                                          </If.Else>
                                                        </If>
                                                      </Sequence>
                                                    </If.Else>
                                                  </If>
                                                </Sequence>
                                              </If.Then>
                                              <If.Else>
                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_127" Line="Lines of the PO not extracted." Source="[logfile]" />
                                              </If.Else>
                                            </If>
                                          </Sequence>
                                        </ActivityAction>
                                      </ForEach>
                                      <If Condition="[M3TotalTableRows.Count &gt; 0 and poExists]" DisplayName="If  Step 2" sap2010:WorkflowViewState.IdRef="If_380">
                                        <If.Then>
                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_427">
                                            <Sequence.Variables>
                                              <Variable x:TypeArguments="x:Int32" Name="x" />
                                              <Variable x:TypeArguments="iru:ResponseObject" Name="poLinesResponseObject" />
                                              <Variable x:TypeArguments="scg:List(x:String)" Name="notReceivedItems" />
                                              <Variable x:TypeArguments="x:Boolean" Name="optimizerSuccess" />
                                            </Sequence.Variables>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_688">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="scg:List(x:String)">[notReceivedItems]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)()]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_879">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="scg:List(x:String)">[invoicesItemNumbers]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)()]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <If Condition="[matchVendorItemCode]" DisplayName="match Vendor item code If" sap2010:WorkflowViewState.IdRef="If_355">
                                              <If.Then>
                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_403">
                                                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_11" Response="[poLinesResponseObject]" Url="[TenantID +&quot;M3/m3api-rest/v2/execute/PPS200MI/LstLine?maxrecs=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;returncols=PUNO%2CITNO%2CSITE%2CORQA%2CRVQA&amp;format=PRETTY&amp;extendedresult=false&quot;]">
                                                    <iai:IONAPIRequestWizard.Headers>
                                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                                          <x:String>Accept</x:String>
                                                        </scg:List>
                                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                                          <x:String>application/json</x:String>
                                                        </scg:List>
                                                      </scg:List>
                                                    </iai:IONAPIRequestWizard.Headers>
                                                    <iai:IONAPIRequestWizard.QueryParameters>
                                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                                          <x:String>PUNO</x:String>
                                                        </scg:List>
                                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                                          <x:String>pono</x:String>
                                                        </scg:List>
                                                      </scg:List>
                                                    </iai:IONAPIRequestWizard.QueryParameters>
                                                  </iai:IONAPIRequestWizard>
                                                  <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_62" Values="[poLinesResponseObject.readasjson(&quot;results&quot;)(0)(&quot;records&quot;)]">
                                                    <ActivityAction x:TypeArguments="njl:JToken">
                                                      <ActivityAction.Argument>
                                                        <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
                                                      </ActivityAction.Argument>
                                                      <ForEach x:TypeArguments="scg:Dictionary(x:String, x:Object)" DisplayName="ForEach&lt;Dictionary&lt;String,Object&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_82" Values="[ListocrLineValues]">
                                                        <ActivityAction x:TypeArguments="scg:Dictionary(x:String, x:Object)">
                                                          <ActivityAction.Argument>
                                                            <DelegateInArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="lines" />
                                                          </ActivityAction.Argument>
                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_529">
                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_924">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary(Of String,Object)]</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_925">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[lines]</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                            <If Condition="[LinesDict(&quot;SUPPLIER_ITEM_CODE&quot;).ToString.ToLower.contains(&quot; &quot;+item(&quot;ITNO&quot;).tostring.tolower+&quot; &quot;) OR LinesDict(&quot;SUPPLIER_ITEM_CODE&quot;).ToString.ToLower.contains(&quot; &quot;+item(&quot;SITE&quot;).tostring.tolower+&quot; &quot;)]" sap2010:WorkflowViewState.IdRef="If_455">
                                                              <If.Then>
                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_525">
                                                                  <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_58" MethodName="Add">
                                                                    <InvokeMethod.TargetObject>
                                                                      <InArgument x:TypeArguments="scg:List(x:String)">[invoicesItemNumbers]</InArgument>
                                                                    </InvokeMethod.TargetObject>
                                                                    <InArgument x:TypeArguments="x:String">[item("ITNO").tostring]</InArgument>
                                                                  </InvokeMethod>
                                                                  <If Condition="[Integer.Parse(item(&quot;ORQA&quot;).tostring) &gt; Integer.Parse(item(&quot;RVQA&quot;).tostring)]" sap2010:WorkflowViewState.IdRef="If_452">
                                                                    <If.Then>
                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_524">
                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_926">
                                                                          <Assign.To>
                                                                            <OutArgument x:TypeArguments="x:Boolean">[allLinesReceived]</OutArgument>
                                                                          </Assign.To>
                                                                          <Assign.Value>
                                                                            <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                                                          </Assign.Value>
                                                                        </Assign>
                                                                        <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_59" MethodName="Add">
                                                                          <InvokeMethod.TargetObject>
                                                                            <InArgument x:TypeArguments="scg:List(x:String)">[notReceivedItems]</InArgument>
                                                                          </InvokeMethod.TargetObject>
                                                                          <InArgument x:TypeArguments="x:String">[item("ITNO").tostring]</InArgument>
                                                                        </InvokeMethod>
                                                                      </Sequence>
                                                                    </If.Then>
                                                                  </If>
                                                                </Sequence>
                                                              </If.Then>
                                                              <If.Else>
                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_528">
                                                                  <Sequence.Variables>
                                                                    <Variable x:TypeArguments="x:String" Name="itemNumberWithPrefix" />
                                                                    <Variable x:TypeArguments="x:String" Name="itemSupNumberWithPrefix" />
                                                                  </Sequence.Variables>
                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_927">
                                                                    <Assign.To>
                                                                      <OutArgument x:TypeArguments="x:String">[itemSupNumberWithPrefix]</OutArgument>
                                                                    </Assign.To>
                                                                    <Assign.Value>
                                                                      <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(item("SITE").tostring, "(\d+)", " $1 ")]</InArgument>
                                                                    </Assign.Value>
                                                                  </Assign>
                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_928">
                                                                    <Assign.To>
                                                                      <OutArgument x:TypeArguments="x:String">[itemNumberWithPrefix]</OutArgument>
                                                                    </Assign.To>
                                                                    <Assign.Value>
                                                                      <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(item("ITNO").tostring, "(\d+)", " $1 ")]</InArgument>
                                                                    </Assign.Value>
                                                                  </Assign>
                                                                  <If Condition="[LinesDict(&quot;SUPPLIER_ITEM_CODE&quot;).ToString.tolower.contains(&quot; &quot;+itemNumberWithPrefix.trim.tolower+&quot; &quot;) OR LinesDict(&quot;SUPPLIER_ITEM_CODE&quot;).ToString.tolower.contains(&quot; &quot;+itemSupNumberWithPrefix.trim.tolower+&quot; &quot;)]" sap2010:WorkflowViewState.IdRef="If_454">
                                                                    <If.Then>
                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_527">
                                                                        <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_60" MethodName="Add">
                                                                          <InvokeMethod.TargetObject>
                                                                            <InArgument x:TypeArguments="scg:List(x:String)">[invoicesItemNumbers]</InArgument>
                                                                          </InvokeMethod.TargetObject>
                                                                          <InArgument x:TypeArguments="x:String">[item("ITNO").tostring]</InArgument>
                                                                        </InvokeMethod>
                                                                        <If Condition="[Integer.Parse(item(&quot;ORQA&quot;).tostring) &gt; Integer.Parse(item(&quot;RVQA&quot;).tostring)]" sap2010:WorkflowViewState.IdRef="If_453">
                                                                          <If.Then>
                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_526">
                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_929">
                                                                                <Assign.To>
                                                                                  <OutArgument x:TypeArguments="x:Boolean">[allLinesReceived]</OutArgument>
                                                                                </Assign.To>
                                                                                <Assign.Value>
                                                                                  <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                                                                </Assign.Value>
                                                                              </Assign>
                                                                              <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_61" MethodName="Add">
                                                                                <InvokeMethod.TargetObject>
                                                                                  <InArgument x:TypeArguments="scg:List(x:String)">[notReceivedItems]</InArgument>
                                                                                </InvokeMethod.TargetObject>
                                                                                <InArgument x:TypeArguments="x:String">[item("ITNO").tostring]</InArgument>
                                                                              </InvokeMethod>
                                                                            </Sequence>
                                                                          </If.Then>
                                                                        </If>
                                                                      </Sequence>
                                                                    </If.Then>
                                                                  </If>
                                                                </Sequence>
                                                              </If.Else>
                                                            </If>
                                                          </Sequence>
                                                        </ActivityAction>
                                                      </ForEach>
                                                    </ActivityAction>
                                                  </ForEach>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_693">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows1]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="scg:List(s:String[])">[new list(OF string()) ()]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_63" Values="[M3TotalTableRows]">
                                                    <ActivityAction x:TypeArguments="s:String[]">
                                                      <ActivityAction.Argument>
                                                        <DelegateInArgument x:TypeArguments="s:String[]" Name="m3Values" />
                                                      </ActivityAction.Argument>
                                                      <If Condition="[matchVendorItemCode and invoicesItemNumbers.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_354">
                                                        <If.Then>
                                                          <If Condition="[invoicesItemNumbers.contains(m3Values(5))]" sap2010:WorkflowViewState.IdRef="If_353">
                                                            <If.Then>
                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_401">
                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_400">
                                                                  <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_39" MethodName="Add">
                                                                    <InvokeMethod.TargetObject>
                                                                      <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows1]</InArgument>
                                                                    </InvokeMethod.TargetObject>
                                                                    <InArgument x:TypeArguments="s:String[]">[m3Values]</InArgument>
                                                                  </InvokeMethod>
                                                                </Sequence>
                                                              </Sequence>
                                                            </If.Then>
                                                          </If>
                                                        </If.Then>
                                                        <If.Else>
                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_402">
                                                            <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_40" MethodName="Add">
                                                              <InvokeMethod.TargetObject>
                                                                <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows1]</InArgument>
                                                              </InvokeMethod.TargetObject>
                                                              <InArgument x:TypeArguments="s:String[]">[m3Values]</InArgument>
                                                            </InvokeMethod>
                                                          </Sequence>
                                                        </If.Else>
                                                      </If>
                                                    </ActivityAction>
                                                  </ForEach>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_694">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows1]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                </Sequence>
                                              </If.Then>
                                            </If>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_749">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="scg:IEnumerable(sl:IGrouping(x:String, s:String[]))">[transDateGroups]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="scg:IEnumerable(sl:IGrouping(x:String, s:String[]))">[M3TotalTableRows.GroupBy(Function(item) item(4))]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_750">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(s:String[]))">[transDateDictionary]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(s:String[]))">[new Dictionary(Of String,list(OF string()))]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <If Condition="[miscValues(&quot;groupByTransDate&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_386">
                                              <If.Then>
                                                <ForEach x:TypeArguments="sl:IGrouping(x:String, s:String[])" DisplayName="ForEach&lt;IGrouping&lt;String,String[]&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_68" Values="[transDateGroups]">
                                                  <ActivityAction x:TypeArguments="sl:IGrouping(x:String, s:String[])">
                                                    <ActivityAction.Argument>
                                                      <DelegateInArgument x:TypeArguments="sl:IGrouping(x:String, s:String[])" Name="group" />
                                                    </ActivityAction.Argument>
                                                    <If Condition="[M3TotalTableRows.Count &gt; 0 and matchVendorItemCode]" sap2010:WorkflowViewState.IdRef="If_523">
                                                      <If.Then>
                                                        <If Condition="[group.ToList().Count &gt;=  ListocrLineValues.Count]" sap2010:WorkflowViewState.IdRef="If_522">
                                                          <If.Then>
                                                            <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_62" MethodName="Add">
                                                              <InvokeMethod.TargetObject>
                                                                <InArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(s:String[]))">[transDateDictionary]</InArgument>
                                                              </InvokeMethod.TargetObject>
                                                              <InArgument x:TypeArguments="x:String">[group.Key]</InArgument>
                                                              <InArgument x:TypeArguments="scg:List(s:String[])">[group.ToList]</InArgument>
                                                            </InvokeMethod>
                                                          </If.Then>
                                                        </If>
                                                      </If.Then>
                                                      <If.Else>
                                                        <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_63" MethodName="Add">
                                                          <InvokeMethod.TargetObject>
                                                            <InArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(s:String[]))">[transDateDictionary]</InArgument>
                                                          </InvokeMethod.TargetObject>
                                                          <InArgument x:TypeArguments="x:String">[group.Key]</InArgument>
                                                          <InArgument x:TypeArguments="scg:List(s:String[])">[group.ToList]</InArgument>
                                                        </InvokeMethod>
                                                      </If.Else>
                                                    </If>
                                                  </ActivityAction>
                                                </ForEach>
                                              </If.Then>
                                              <If.Else>
                                                <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_44" MethodName="Add">
                                                  <InvokeMethod.TargetObject>
                                                    <InArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(s:String[]))">[transDateDictionary]</InArgument>
                                                  </InvokeMethod.TargetObject>
                                                  <InArgument x:TypeArguments="x:String">[transDateGroups(0).key]</InArgument>
                                                  <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</InArgument>
                                                </InvokeMethod>
                                              </If.Else>
                                            </If>
                                            <If Condition="[transDateDictionary.count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_387">
                                              <If.Then>
                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_436">
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_751">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:Boolean">[optimizerSuccess]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <ForEach x:TypeArguments="scg:KeyValuePair(x:String, scg:List(s:String[]))" DisplayName="ForEach&lt;KeyValuePair&lt;String,List&lt;String[]&gt;&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_70" Values="[transDateDictionary]">
                                                    <ActivityAction x:TypeArguments="scg:KeyValuePair(x:String, scg:List(s:String[]))">
                                                      <ActivityAction.Argument>
                                                        <DelegateInArgument x:TypeArguments="scg:KeyValuePair(x:String, scg:List(s:String[]))" Name="group" />
                                                      </ActivityAction.Argument>
                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_437">
                                                        <Sequence.Variables>
                                                          <Variable x:TypeArguments="x:Decimal" Name="qty" />
                                                        </Sequence.Variables>
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_752">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="scg:List(s:String[])">[group.value]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <If Condition="[not optimizerSuccess]" sap2010:WorkflowViewState.IdRef="If_388">
                                                          <If.Then>
                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_438">
                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_695">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="x:Int32">[x]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="x:Int32">1</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                              <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_64" Values="[M3TotalTableRows]">
                                                                <ActivityAction x:TypeArguments="s:String[]">
                                                                  <ActivityAction.Argument>
                                                                    <DelegateInArgument x:TypeArguments="s:String[]" Name="m3Values" />
                                                                  </ActivityAction.Argument>
                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_404">
                                                                    <If Condition="[x=1]" sap2010:WorkflowViewState.IdRef="If_356">
                                                                      <If.Then>
                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_696">
                                                                          <Assign.To>
                                                                            <OutArgument x:TypeArguments="x:String">[APIString]</OutArgument>
                                                                          </Assign.To>
                                                                          <Assign.Value>
                                                                            <InArgument x:TypeArguments="x:String">["[["+m3Values(0)+","+m3Values(1)+","+m3Values(2)+","+m3Values(3)+","+DictOcrValues("TOTAL").ToString+","+ tolPercentage +"," + tolAmt +"]"]</InArgument>
                                                                          </Assign.Value>
                                                                        </Assign>
                                                                      </If.Then>
                                                                      <If.Else>
                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_697">
                                                                          <Assign.To>
                                                                            <OutArgument x:TypeArguments="x:String">[APIString]</OutArgument>
                                                                          </Assign.To>
                                                                          <Assign.Value>
                                                                            <InArgument x:TypeArguments="x:String">[APIString+",["+m3Values(0)+","+m3Values(1)+","+m3Values(2)+","+m3Values(3)+","+DictOcrValues("TOTAL").ToString+","+ tolPercentage +"," + tolAmt +"]"]</InArgument>
                                                                          </Assign.Value>
                                                                        </Assign>
                                                                      </If.Else>
                                                                    </If>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_698">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:Int32">[x]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:Int32">[x+1]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                  </Sequence>
                                                                </ActivityAction>
                                                              </ForEach>
                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_753">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="x:Boolean">[chargeExists]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                              <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;DictOcrValues&quot;,DictOcrValues},{&quot;colemanAPI&quot;,colemanAPI},{&quot;APIString&quot;,APIString},{&quot;logfile&quot;,logfile}}]" ContinueOnError="True" DisplayName="Coleman Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_38" OutputArguments="[ColemanOutPut]" ResponseCode="[ColemanRespCode]" WorkflowFile="[projectPath+&quot;\CallColeman.xaml&quot;]" />
                                                              <If Condition="[ColemanRespCode = 200]" sap2010:WorkflowViewState.IdRef="If_413">
                                                                <If.Then>
                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_463">
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_754">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">[ColemanOutPut("comments").ToString()]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_755">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">[ColemanOutPut("Status").ToString()]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_756">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="njl:JToken">[httpOut]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(ColemanOutPut("httpOut").ToString)]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <If Condition="[Status = &quot;Success&quot;]" sap2010:WorkflowViewState.IdRef="If_412">
                                                                      <If.Then>
                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_462">
                                                                          <If Condition="[httpOut.ToString.Contains(&quot;Not Allocated&quot;)]" sap2010:WorkflowViewState.IdRef="If_394">
                                                                            <If.Then>
                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_444">
                                                                                <If Condition="[CInt(DictOcrValues(&quot;SHIPPING_AND_HANDLING_AMOUNT&quot;).ToString) &lt;&gt; 0 OR CInt(DictOcrValues(&quot;VAT_AMOUNT&quot;).ToString) &lt;&gt; 0]" sap2010:WorkflowViewState.IdRef="If_393">
                                                                                  <If.Then>
                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_443">
                                                                                      <Sequence.Variables>
                                                                                        <Variable x:TypeArguments="x:String" Name="colamt" />
                                                                                      </Sequence.Variables>
                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_757">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:String">[colamt]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:String">[(Convert.toDecimal(DictOcrValues("TOTAL").ToString) - Convert.toDecimal(DictOcrValues("SHIPPING_AND_HANDLING_AMOUNT").ToString) - Convert.toDecimal(DictOcrValues("VAT_AMOUNT").ToString)).ToString()]</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_758">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:Int32">[x]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:Int32">1</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                      <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_71" Values="[M3TotalTableRows]">
                                                                                        <ActivityAction x:TypeArguments="s:String[]">
                                                                                          <ActivityAction.Argument>
                                                                                            <DelegateInArgument x:TypeArguments="s:String[]" Name="item" />
                                                                                          </ActivityAction.Argument>
                                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_441">
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_759">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="s:String[]">[m3Values]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="s:String[]">[item]</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                            <If Condition="[x=1]" sap2010:WorkflowViewState.IdRef="If_389">
                                                                                              <If.Then>
                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_439">
                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_760">
                                                                                                    <Assign.To>
                                                                                                      <OutArgument x:TypeArguments="x:String">[APIString]</OutArgument>
                                                                                                    </Assign.To>
                                                                                                    <Assign.Value>
                                                                                                      <InArgument x:TypeArguments="x:String">["[["+m3Values(0)+","+m3Values(1)+","+m3Values(2)+","+m3Values(3)+","+colamt+","+ tolPercentage +"," + tolAmt +"]"]</InArgument>
                                                                                                    </Assign.Value>
                                                                                                  </Assign>
                                                                                                </Sequence>
                                                                                              </If.Then>
                                                                                              <If.Else>
                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_440">
                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_761">
                                                                                                    <Assign.To>
                                                                                                      <OutArgument x:TypeArguments="x:String">[APIString]</OutArgument>
                                                                                                    </Assign.To>
                                                                                                    <Assign.Value>
                                                                                                      <InArgument x:TypeArguments="x:String">[APIString+",["+m3Values(0)+","+m3Values(1)+","+m3Values(2)+","+m3Values(3)+","+colamt+","+ tolPercentage +"," + tolAmt +"]"]</InArgument>
                                                                                                    </Assign.Value>
                                                                                                  </Assign>
                                                                                                </Sequence>
                                                                                              </If.Else>
                                                                                            </If>
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_762">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="x:Int32">[x]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="x:Int32">[x+1]</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                          </Sequence>
                                                                                        </ActivityAction>
                                                                                      </ForEach>
                                                                                      <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;DictOcrValues&quot;,DictOcrValues},{&quot;colemanAPI&quot;,colemanAPI},{&quot;APIString&quot;,APIString},{&quot;logfile&quot;,logfile}}]" ContinueOnError="True" DisplayName="Coleman Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_39" OutputArguments="[ColemanOutPut]" ResponseCode="[ColemanRespCode]" WorkflowFile="[projectPath+&quot;\CallColeman.xaml&quot;]" />
                                                                                      <If Condition="[ColemanRespCode = 200]" sap2010:WorkflowViewState.IdRef="If_392">
                                                                                        <If.Then>
                                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_442">
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_763">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="njl:JToken">[httpOut]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(ColemanOutPut("httpOut").ToString)]</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_764">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="x:String">[ColemanOutPut("comments").ToString()]</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_765">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="x:String">[ColemanOutPut("Status").ToString()]</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                            <If Condition="[Status = &quot;Success&quot;]" sap2010:WorkflowViewState.IdRef="If_391">
                                                                                              <If.Then>
                                                                                                <If Condition="[NOT httpOut.ToString.Contains(&quot;Not Allocated&quot;)]" sap2010:WorkflowViewState.IdRef="If_390">
                                                                                                  <If.Then>
                                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_473">
                                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_766">
                                                                                                        <Assign.To>
                                                                                                          <OutArgument x:TypeArguments="x:Boolean">[chargeExists]</OutArgument>
                                                                                                        </Assign.To>
                                                                                                        <Assign.Value>
                                                                                                          <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                                                                                        </Assign.Value>
                                                                                                      </Assign>
                                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_812">
                                                                                                        <Assign.To>
                                                                                                          <OutArgument x:TypeArguments="x:String">[colAmount]</OutArgument>
                                                                                                        </Assign.To>
                                                                                                        <Assign.Value>
                                                                                                          <InArgument x:TypeArguments="x:String">[colAmt]</InArgument>
                                                                                                        </Assign.Value>
                                                                                                      </Assign>
                                                                                                    </Sequence>
                                                                                                  </If.Then>
                                                                                                </If>
                                                                                              </If.Then>
                                                                                            </If>
                                                                                          </Sequence>
                                                                                        </If.Then>
                                                                                      </If>
                                                                                    </Sequence>
                                                                                  </If.Then>
                                                                                </If>
                                                                              </Sequence>
                                                                            </If.Then>
                                                                            <If.Else>
                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_445">
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_813">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[colAmount]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">[DictOcrValues("TOTAL").ToString]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                              </Sequence>
                                                                            </If.Else>
                                                                          </If>
                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_767">
                                                                            <Assign.To>
                                                                              <OutArgument x:TypeArguments="njl:JToken">[httpOut]</OutArgument>
                                                                            </Assign.To>
                                                                            <Assign.Value>
                                                                              <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(ColemanOutPut("httpOut").ToString)]</InArgument>
                                                                            </Assign.Value>
                                                                          </Assign>
                                                                          <If Condition="[NOT (httpOut.ToString.Contains(&quot;Not Allocated&quot;) AND miscValues(&quot;createInvoiceIrrespectiveTolerance&quot;).ToString.ToLower = &quot;false&quot;)]" sap2010:WorkflowViewState.IdRef="If_411">
                                                                            <If.Then>
                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_456">
                                                                                <Sequence.Variables>
                                                                                  <Variable x:TypeArguments="x:String" Name="sino" />
                                                                                </Sequence.Variables>
                                                                                <Assign DisplayName="Assign ivdate" sap2010:WorkflowViewState.IdRef="Assign_768">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[ivdate]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">[DateTime.ParseExact(DictOcrValues("INVOICE_RECEIPT_DATE").ToString,"dd/MM/yyyy",Nothing).ToString("yyyyMMdd")]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_769">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">[System.DateTime.Now.ToString("yyyyMMddHHmmssfff").tostring + " -"+ DictOcrValues("INVOICE_RECEIPT_ID").ToString + "-" + division + "-" + vendorID]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_1">
                                                                                  <TryCatch.Try>
                                                                                    <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_770">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:String">[correlationID.Substring(0,35)]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                  </TryCatch.Try>
                                                                                  <TryCatch.Catches>
                                                                                    <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_1">
                                                                                      <ActivityAction x:TypeArguments="s:Exception">
                                                                                        <ActivityAction.Argument>
                                                                                          <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                        </ActivityAction.Argument>
                                                                                        <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_814">
                                                                                          <Assign.To>
                                                                                            <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                                                                                          </Assign.To>
                                                                                          <Assign.Value>
                                                                                            <InArgument x:TypeArguments="x:String">[correlationID]</InArgument>
                                                                                          </Assign.Value>
                                                                                        </Assign>
                                                                                      </ActivityAction>
                                                                                    </Catch>
                                                                                  </TryCatch.Catches>
                                                                                </TryCatch>
                                                                                <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_797">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:Boolean">[optimizerSuccess]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <If Condition="[miscValues(&quot;BusinessRuleAPResp&quot;).ToString.ToUpper() &lt;&gt; &quot;NA&quot;]" sap2010:WorkflowViewState.IdRef="If_445">
                                                                                  <If.Then>
                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_516">
                                                                                      <Sequence.Variables>
                                                                                        <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="APRespRespDict" />
                                                                                        <Variable x:TypeArguments="x:Int32" Name="APRespRespCode" />
                                                                                      </Sequence.Variables>
                                                                                      <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;division&quot;,division},{&quot;company&quot;,company},{&quot;VendorID&quot;,vendorID}}]" ContinueOnError="True" DisplayName="AP resp Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_49" OutputArguments="[APRespRespDict]" ResponseCode="[APRespRespCode]" WorkflowFile="[projectPath+&quot;\APResp.xaml&quot;]" />
                                                                                      <If Condition="[APRespRespCode = 200]" sap2010:WorkflowViewState.IdRef="If_446">
                                                                                        <If.Then>
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_914">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:String">[APResp]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:String">[CType(APRespRespDict("APResp"), String)]</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                        </If.Then>
                                                                                      </If>
                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_913">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:String">[authUser]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:String">[APResp]</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                    </Sequence>
                                                                                  </If.Then>
                                                                                  <If.Else>
                                                                                    <If Condition="[authUser.ToUpper() = &quot;SUPPLIERAUTHUSER&quot;]" sap2010:WorkflowViewState.IdRef="If_444">
                                                                                      <If.Then>
                                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_515">
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_912">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:String">[authUser]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:String">[vendorResult(1)]</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                        </Sequence>
                                                                                      </If.Then>
                                                                                    </If>
                                                                                  </If.Else>
                                                                                </If>
                                                                                <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;bkid&quot;,bkid},{&quot;authUser&quot;,authUser},{&quot;cuam&quot;,DictOcrValues(&quot;TOTAL&quot;).ToString.Replace(&quot;,&quot;,&quot;&quot;)},{&quot;pyme&quot;,pyme},{&quot;tepy&quot;,tepy},{&quot;cucd&quot;,cucd},{&quot;sino&quot;,DictOcrValues(&quot;INVOICE_RECEIPT_ID&quot;).ToString},{&quot;division&quot;,division},{&quot;ivdate&quot;,ivdate},{&quot;SupplierNo&quot;,VendorID},{&quot;correlationID&quot;,correlationID},{&quot;discountTerms&quot;,discountTerms},{&quot;miscValues&quot;,miscValues}}]" ContinueOnError="True" DisplayName="Add Head Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_40" OutputArguments="[AddHeadOcrWorkflowOutput]" ResponseCode="[AddHeadOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\AddHead.xaml&quot;]" />
                                                                                <If Condition="[AddHeadOcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_407">
                                                                                  <If.Then>
                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_455">
                                                                                      <Sequence.Variables>
                                                                                        <Variable x:TypeArguments="scg:List(s:String[])" Name="M3TotalTableRowsAndColeman" />
                                                                                        <Variable x:TypeArguments="x:String" Name="vat" />
                                                                                      </Sequence.Variables>
                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_771">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:String">[inbnValue]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:String">[CType(AddHeadOcrWorkflowOutput("inbnValue"), String)]</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_772">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:String">[CType(AddHeadOcrWorkflowOutput("Status"), String)]</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_773">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:String">[CType(AddHeadOcrWorkflowOutput("commentStatus"), String)]</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                      <If Condition="[inbnValue &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_406">
                                                                                        <If.Then>
                                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_454">
                                                                                            <Sequence.Variables>
                                                                                              <Variable x:TypeArguments="iru:ResponseObject" Name="resp" />
                                                                                              <Variable x:TypeArguments="x:String" Name="diffamt" />
                                                                                              <Variable x:TypeArguments="x:String" Name="includeDistribution" />
                                                                                              <Variable x:TypeArguments="x:Boolean" Name="chFound" />
                                                                                              <Variable x:TypeArguments="x:Decimal" Name="lineAmt" />
                                                                                              <Variable x:TypeArguments="x:String" Name="chargeDiff" />
                                                                                            </Sequence.Variables>
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_774">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRowsAndColeman]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="scg:List(s:String[])">[new list(OF string())]</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_829">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="x:Decimal">[lineAmt]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="x:Decimal">[0]</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_830">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="x:Decimal">[qty]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="x:Decimal">[0]</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                            <If Condition="[httpOut.ToString.Contains(&quot;Not Allocated&quot;)]" sap2010:WorkflowViewState.IdRef="If_424">
                                                                                              <If.Then>
                                                                                                <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_75" Values="[M3TotalTableRows]">
                                                                                                  <ActivityAction x:TypeArguments="s:String[]">
                                                                                                    <ActivityAction.Argument>
                                                                                                      <DelegateInArgument x:TypeArguments="s:String[]" Name="rows" />
                                                                                                    </ActivityAction.Argument>
                                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_480">
                                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_823">
                                                                                                        <Assign.To>
                                                                                                          <OutArgument x:TypeArguments="x:Decimal">[lineAmt]</OutArgument>
                                                                                                        </Assign.To>
                                                                                                        <Assign.Value>
                                                                                                          <InArgument x:TypeArguments="x:Decimal">[lineAmt +convert.ToDecimal(rows(2))*convert.ToDecimal(rows(3))]</InArgument>
                                                                                                        </Assign.Value>
                                                                                                      </Assign>
                                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_824">
                                                                                                        <Assign.To>
                                                                                                          <OutArgument x:TypeArguments="x:Decimal">[qty]</OutArgument>
                                                                                                        </Assign.To>
                                                                                                        <Assign.Value>
                                                                                                          <InArgument x:TypeArguments="x:Decimal">[qty + convert.ToDecimal(rows(3))]</InArgument>
                                                                                                        </Assign.Value>
                                                                                                      </Assign>
                                                                                                      <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_46" MethodName="Add">
                                                                                                        <InvokeMethod.TargetObject>
                                                                                                          <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRowsAndColeman]</InArgument>
                                                                                                        </InvokeMethod.TargetObject>
                                                                                                        <InArgument x:TypeArguments="s:String[]">[rows]</InArgument>
                                                                                                      </InvokeMethod>
                                                                                                    </Sequence>
                                                                                                  </ActivityAction>
                                                                                                </ForEach>
                                                                                              </If.Then>
                                                                                              <If.Else>
                                                                                                <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_79" Values="[httpOut]">
                                                                                                  <ActivityAction x:TypeArguments="njl:JToken">
                                                                                                    <ActivityAction.Argument>
                                                                                                      <DelegateInArgument x:TypeArguments="njl:JToken" Name="out" />
                                                                                                    </ActivityAction.Argument>
                                                                                                    <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_78" Values="[M3TotalTableRows]">
                                                                                                      <ActivityAction x:TypeArguments="s:String[]">
                                                                                                        <ActivityAction.Argument>
                                                                                                          <DelegateInArgument x:TypeArguments="s:String[]" Name="rows" />
                                                                                                        </ActivityAction.Argument>
                                                                                                        <If Condition="[out(&quot;line_number&quot;).ToString=rows(0) And out(&quot;rcd_number&quot;).ToString=rows(1)]" sap2010:WorkflowViewState.IdRef="If_425">
                                                                                                          <If.Then>
                                                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_482">
                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_827">
                                                                                                                <Assign.To>
                                                                                                                  <OutArgument x:TypeArguments="x:Decimal">[lineAmt]</OutArgument>
                                                                                                                </Assign.To>
                                                                                                                <Assign.Value>
                                                                                                                  <InArgument x:TypeArguments="x:Decimal">[lineAmt +convert.ToDecimal(rows(2))*convert.ToDecimal(rows(3))]</InArgument>
                                                                                                                </Assign.Value>
                                                                                                              </Assign>
                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_828">
                                                                                                                <Assign.To>
                                                                                                                  <OutArgument x:TypeArguments="x:Decimal">[qty]</OutArgument>
                                                                                                                </Assign.To>
                                                                                                                <Assign.Value>
                                                                                                                  <InArgument x:TypeArguments="x:Decimal">[qty + convert.ToDecimal(rows(3))]</InArgument>
                                                                                                                </Assign.Value>
                                                                                                              </Assign>
                                                                                                              <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_48" MethodName="Add">
                                                                                                                <InvokeMethod.TargetObject>
                                                                                                                  <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRowsAndColeman]</InArgument>
                                                                                                                </InvokeMethod.TargetObject>
                                                                                                                <InArgument x:TypeArguments="s:String[]">[rows]</InArgument>
                                                                                                              </InvokeMethod>
                                                                                                            </Sequence>
                                                                                                          </If.Then>
                                                                                                        </If>
                                                                                                      </ActivityAction>
                                                                                                    </ForEach>
                                                                                                  </ActivityAction>
                                                                                                </ForEach>
                                                                                              </If.Else>
                                                                                            </If>
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_805">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="x:String">[diffamt]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="x:String">[((convert.ToDecimal(colAmount) - lineAmt)/qty).ToString]</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                            <If Condition="[chargeExists]" sap2010:WorkflowViewState.IdRef="If_422">
                                                                                              <If.Then>
                                                                                                <Sequence DisplayName="Amount is matched with subtotal Sequence" sap2010:WorkflowViewState.IdRef="Sequence_477">
                                                                                                  <If Condition="[DictOcrValues(&quot;SHIPPING_AND_HANDLING_AMOUNT&quot;).ToString &lt;&gt; &quot;0&quot;]" sap2010:WorkflowViewState.IdRef="If_419">
                                                                                                    <If.Then>
                                                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_476">
                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_819">
                                                                                                          <Assign.To>
                                                                                                            <OutArgument x:TypeArguments="x:String">[diffamt]</OutArgument>
                                                                                                          </Assign.To>
                                                                                                          <Assign.Value>
                                                                                                            <InArgument x:TypeArguments="x:String">[((convert.ToDecimal(DictOcrValues("TOTAL").ToString) - convert.ToDecimal(DictOcrValues("SHIPPING_AND_HANDLING_AMOUNT").ToString) - lineAmt)/qty).ToString]</InArgument>
                                                                                                          </Assign.Value>
                                                                                                        </Assign>
                                                                                                      </Sequence>
                                                                                                    </If.Then>
                                                                                                  </If>
                                                                                                </Sequence>
                                                                                              </If.Then>
                                                                                              <If.Else>
                                                                                                <Sequence DisplayName="Total Amount is within Tol" sap2010:WorkflowViewState.IdRef="Sequence_479">
                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_820">
                                                                                                    <Assign.To>
                                                                                                      <OutArgument x:TypeArguments="x:String">[diffamt]</OutArgument>
                                                                                                    </Assign.To>
                                                                                                    <Assign.Value>
                                                                                                      <InArgument x:TypeArguments="x:String">[((convert.ToDecimal(colAmount) - lineAmt)/qty).ToString]</InArgument>
                                                                                                    </Assign.Value>
                                                                                                  </Assign>
                                                                                                  <If Condition="[CInt(DictOcrValues(&quot;SHIPPING_AND_HANDLING_AMOUNT&quot;).ToString) &lt;&gt; 0]" DisplayName="Matched with total but there is charge" sap2010:WorkflowViewState.IdRef="If_421">
                                                                                                    <If.Then>
                                                                                                      <If Condition="[Convert.ToDecimal(DictOcrValues(&quot;SHIPPING_AND_HANDLING_AMOUNT&quot;).ToString) - convert.ToDecimal(diffAmt) * Convert.ToDecimal(qty) &lt; 1]" DisplayName="If diff amount = charge in invoice" sap2010:WorkflowViewState.IdRef="If_420">
                                                                                                        <If.Then>
                                                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_478">
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_821">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:String">[diffamt]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:String">0</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_822">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:Boolean">[ChargeExists]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                          </Sequence>
                                                                                                        </If.Then>
                                                                                                      </If>
                                                                                                    </If.Then>
                                                                                                  </If>
                                                                                                </Sequence>
                                                                                              </If.Else>
                                                                                            </If>
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_806">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="x:String">[includeDistribution]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="x:String">[miscValues("includeDistribution").ToString]</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                            <If Condition="[httpOut.ToString.Contains(&quot;Not Allocated&quot;)]" sap2010:WorkflowViewState.IdRef="If_427">
                                                                                              <If.Then>
                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_483">
                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_831">
                                                                                                    <Assign.To>
                                                                                                      <OutArgument x:TypeArguments="x:String">[diffamt]</OutArgument>
                                                                                                    </Assign.To>
                                                                                                    <Assign.Value>
                                                                                                      <InArgument x:TypeArguments="x:String">[((convert.ToDecimal(DictOcrValues("TOTAL").ToString) - convert.ToDecimal(DictOcrValues("SHIPPING_AND_HANDLING_AMOUNT").ToString) - convert.ToDecimal(DictOcrValues("VAT_AMOUNT").ToString) - lineAmt)/qty).ToString]</InArgument>
                                                                                                    </Assign.Value>
                                                                                                  </Assign>
                                                                                                  <If Condition="[CInt(DictOcrValues(&quot;SHIPPING_AND_HANDLING_AMOUNT&quot;).ToString) &lt;&gt; 0]" sap2010:WorkflowViewState.IdRef="If_426">
                                                                                                    <If.Then>
                                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_832">
                                                                                                        <Assign.To>
                                                                                                          <OutArgument x:TypeArguments="x:Boolean">[ChargeExists]</OutArgument>
                                                                                                        </Assign.To>
                                                                                                        <Assign.Value>
                                                                                                          <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                                                                                        </Assign.Value>
                                                                                                      </Assign>
                                                                                                    </If.Then>
                                                                                                  </If>
                                                                                                </Sequence>
                                                                                              </If.Then>
                                                                                            </If>
                                                                                            <If Condition="[diffamt &lt;&gt; &quot;0&quot; AND includeDistribution.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_418">
                                                                                              <If.Then>
                                                                                                <ForEach x:TypeArguments="x:Int32" DisplayName="ForEach&lt;Int32&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_74" Values="[Enumerable.Range(0,M3TotalTableRowsAndColeman.count)]">
                                                                                                  <ActivityAction x:TypeArguments="x:Int32">
                                                                                                    <ActivityAction.Argument>
                                                                                                      <DelegateInArgument x:TypeArguments="x:Int32" Name="i" />
                                                                                                    </ActivityAction.Argument>
                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_1054">
                                                                                                      <Assign.To>
                                                                                                        <OutArgument x:TypeArguments="x:String">[M3TotalTableRowsAndColeman(i)(2)]</OutArgument>
                                                                                                      </Assign.To>
                                                                                                      <Assign.Value>
                                                                                                        <InArgument x:TypeArguments="x:String">[(convert.ToDecimal(M3TotalTableRowsAndColeman(i)(2)) + convert.Todecimal(diffamt)).ToString]</InArgument>
                                                                                                      </Assign.Value>
                                                                                                    </Assign>
                                                                                                  </ActivityAction>
                                                                                                </ForEach>
                                                                                              </If.Then>
                                                                                            </If>
                                                                                            <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;M3TotalTableRows&quot;,M3TotalTableRowsAndColeman},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;division&quot;,division},{&quot;inbnValue&quot;,inbnValue}}]" ContinueOnError="True" DisplayName="Add Lines Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_41" OutputArguments="[AddLineOcrWorkflowOutput]" ResponseCode="[AddLineOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\AddLine.xaml&quot;]" />
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_883">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="x:String">[chargeDiff]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="x:String">0</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                            <If Condition="[DictOcrValues(&quot;SHIPPING_AND_HANDLING_AMOUNT&quot;).ToString &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_397">
                                                                                              <If.Then>
                                                                                                <If Condition="[chargeExists AND CInt(DictOcrValues(&quot;SHIPPING_AND_HANDLING_AMOUNT&quot;).ToString) &lt;&gt; 0]" sap2010:WorkflowViewState.IdRef="If_396">
                                                                                                  <If.Then>
                                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_507">
                                                                                                      <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;M3TotalTableRows&quot;,M3TotalTableRowsAndColeman},{&quot;division&quot;,division},{&quot;inbnValue&quot;,inbnValue},{&quot;charge&quot;,DictOcrValues(&quot;SHIPPING_AND_HANDLING_AMOUNT&quot;).ToString},{&quot;chargeCode&quot;,chargeCode}}]" ContinueOnError="True" DisplayName="Add Charges Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_42" OutputArguments="[AddLineOcrWorkflowOutput]" ResponseCode="[AddLineOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\AddCharge.xaml&quot;]" />
                                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_884">
                                                                                                        <Assign.To>
                                                                                                          <OutArgument x:TypeArguments="x:String">[chargeDiff]</OutArgument>
                                                                                                        </Assign.To>
                                                                                                        <Assign.Value>
                                                                                                          <InArgument x:TypeArguments="x:String">[Ctype(AddLineOcrWorkflowOutput("chargeDiff"),String)]</InArgument>
                                                                                                        </Assign.Value>
                                                                                                      </Assign>
                                                                                                    </Sequence>
                                                                                                  </If.Then>
                                                                                                </If>
                                                                                              </If.Then>
                                                                                            </If>
                                                                                            <If Condition="[DictOcrValues(&quot;VAT_PERCENTAGE&quot;).Tostring.contains(&quot;%&quot;)]" DisplayName="If Updated" sap2010:WorkflowViewState.IdRef="If_521">
                                                                                              <If.Then>
                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_610">
                                                                                                  <Sequence.Variables>
                                                                                                    <Variable x:TypeArguments="iru:ResponseObject" Name="respout1" />
                                                                                                    <Variable x:TypeArguments="x:String" Name="vatCode" />
                                                                                                  </Sequence.Variables>
                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_1052">
                                                                                                    <Assign.To>
                                                                                                      <OutArgument x:TypeArguments="x:String">[vat]</OutArgument>
                                                                                                    </Assign.To>
                                                                                                    <Assign.Value>
                                                                                                      <InArgument x:TypeArguments="x:String">[DictOcrValues("VAT_AMOUNT").Tostring]</InArgument>
                                                                                                    </Assign.Value>
                                                                                                  </Assign>
                                                                                                  <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_2">
                                                                                                    <iad:CommentOut.Activities>
                                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_1053">
                                                                                                        <Assign.To>
                                                                                                          <OutArgument x:TypeArguments="x:String">[vatCode]</OutArgument>
                                                                                                        </Assign.To>
                                                                                                        <Assign.Value>
                                                                                                          <InArgument x:TypeArguments="x:String">[miscValues("vatCodeConfig").ToString]</InArgument>
                                                                                                        </Assign.Value>
                                                                                                      </Assign>
                                                                                                      <If Condition="[miscValues(&quot;vatCodeConfig&quot;).ToString = &quot;NA&quot;]" sap2010:WorkflowViewState.IdRef="If_520">
                                                                                                        <If.Then>
                                                                                                          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_35" Response="[respout1]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                                                                            <iai:IONAPIRequestWizard.Headers>
                                                                                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                  <x:String>Accept</x:String>
                                                                                                                </scg:List>
                                                                                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                  <x:String>application/json</x:String>
                                                                                                                </scg:List>
                                                                                                              </scg:List>
                                                                                                            </iai:IONAPIRequestWizard.Headers>
                                                                                                            <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                  <x:String>INBN</x:String>
                                                                                                                  <x:String>RDTP</x:String>
                                                                                                                  <x:String>DIVI</x:String>
                                                                                                                  <x:String>GLAM</x:String>
                                                                                                                </scg:List>
                                                                                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                  <x:String>inbnValue</x:String>
                                                                                                                  <x:String>3</x:String>
                                                                                                                  <x:String>division</x:String>
                                                                                                                  <x:String>vat</x:String>
                                                                                                                </scg:List>
                                                                                                              </scg:List>
                                                                                                            </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                          </iai:IONAPIRequestWizard>
                                                                                                        </If.Then>
                                                                                                        <If.Else>
                                                                                                          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_36" Response="[respout1]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                                                                            <iai:IONAPIRequestWizard.Headers>
                                                                                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                  <x:String>Accept</x:String>
                                                                                                                </scg:List>
                                                                                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                  <x:String>application/json</x:String>
                                                                                                                </scg:List>
                                                                                                              </scg:List>
                                                                                                            </iai:IONAPIRequestWizard.Headers>
                                                                                                            <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                                                  <x:String>INBN</x:String>
                                                                                                                  <x:String>RDTP</x:String>
                                                                                                                  <x:String>DIVI</x:String>
                                                                                                                  <x:String>VTA1</x:String>
                                                                                                                  <x:String>VTCD</x:String>
                                                                                                                </scg:List>
                                                                                                                <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                                                  <x:String>inbnValue</x:String>
                                                                                                                  <x:String>3</x:String>
                                                                                                                  <x:String>division</x:String>
                                                                                                                  <x:String>vat</x:String>
                                                                                                                  <x:String>vatCode</x:String>
                                                                                                                </scg:List>
                                                                                                              </scg:List>
                                                                                                            </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                          </iai:IONAPIRequestWizard>
                                                                                                        </If.Else>
                                                                                                      </If>
                                                                                                    </iad:CommentOut.Activities>
                                                                                                  </iad:CommentOut>
                                                                                                  <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;vat&quot;,DictOcrValues(&quot;VAT_AMOUNT&quot;).Tostring},{&quot;VatPercentage&quot;,DictOcrValues(&quot;VAT_PERCENTAGE&quot;).Tostring},{&quot;division&quot;,division},{&quot;tenantID&quot;,tenantID},{&quot;inbnValue&quot;,inbnValue},{&quot;CountryCode&quot;,countryCode},{&quot;AccountStartsWith&quot;,&quot;&quot;},{&quot;logfile&quot;,logfile},{&quot;invoiceType&quot;,&quot;po&quot;}}]" ContinueOnError="True" DisplayName="Invoke vatConfiguration" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_62" WorkflowFile="[projectPath+&quot;\vatConfiguration.xaml&quot;]" />
                                                                                                </Sequence>
                                                                                              </If.Then>
                                                                                              <If.Else>
                                                                                                <If Condition="[(Convert.ToInt32(DictOcrValues(&quot;VAT_AMOUNT&quot;).Tostring)&lt;&gt;0) AND ChargeExists]" sap2010:WorkflowViewState.IdRef="If_524">
                                                                                                  <If.Then>
                                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_611">
                                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_1055">
                                                                                                        <Assign.To>
                                                                                                          <OutArgument x:TypeArguments="x:String">[vat]</OutArgument>
                                                                                                        </Assign.To>
                                                                                                        <Assign.Value>
                                                                                                          <InArgument x:TypeArguments="x:String">[DictOcrValues("VAT_AMOUNT").Tostring]</InArgument>
                                                                                                        </Assign.Value>
                                                                                                      </Assign>
                                                                                                      <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;vat&quot;,DictOcrValues(&quot;VAT_AMOUNT&quot;).Tostring},{&quot;VatPercentage&quot;,DictOcrValues(&quot;VAT_PERCENTAGE&quot;).Tostring},{&quot;division&quot;,division},{&quot;tenantID&quot;,tenantID},{&quot;inbnValue&quot;,inbnValue},{&quot;CountryCode&quot;,countryCode},{&quot;AccountStartsWith&quot;,&quot;&quot;},{&quot;logfile&quot;,logfile},{&quot;invoiceType&quot;,&quot;po&quot;}}]" ContinueOnError="True" DisplayName="Invoke vatConfiguration" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_63" WorkflowFile="[projectPath+&quot;\vatConfiguration.xaml&quot;]" />
                                                                                                    </Sequence>
                                                                                                  </If.Then>
                                                                                                </If>
                                                                                              </If.Else>
                                                                                            </If>
                                                                                            <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_3">
                                                                                              <iad:CommentOut.Activities>
                                                                                                <If Condition="[chargeExists]" sap2010:WorkflowViewState.IdRef="If_398">
                                                                                                  <If.Else>
                                                                                                    <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_1">
                                                                                                      <iad:CommentOut.Activities>
                                                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_446">
                                                                                                          <Sequence.Variables>
                                                                                                            <Variable x:TypeArguments="x:String" Name="vat" />
                                                                                                            <Variable x:TypeArguments="x:String" Name="vatCode" />
                                                                                                          </Sequence.Variables>
                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_775">
                                                                                                            <Assign.To>
                                                                                                              <OutArgument x:TypeArguments="x:String">[vat]</OutArgument>
                                                                                                            </Assign.To>
                                                                                                            <Assign.Value>
                                                                                                              <InArgument x:TypeArguments="x:String">[DictOcrValues("VAT_AMOUNT").ToString]</InArgument>
                                                                                                            </Assign.Value>
                                                                                                          </Assign>
                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_882">
                                                                                                            <Assign.To>
                                                                                                              <OutArgument x:TypeArguments="x:String">[vatCode]</OutArgument>
                                                                                                            </Assign.To>
                                                                                                            <Assign.Value>
                                                                                                              <InArgument x:TypeArguments="x:String">[miscValues("vatCodeConfig").ToString]</InArgument>
                                                                                                            </Assign.Value>
                                                                                                          </Assign>
                                                                                                          <If Condition="[miscValues(&quot;vatCodeConfig&quot;).ToString = &quot;NA&quot;]" sap2010:WorkflowViewState.IdRef="If_438">
                                                                                                            <If.Then>
                                                                                                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_25" Response="[resp]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                                                                                <iai:IONAPIRequestWizard.Headers>
                                                                                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                      <x:String>Accept</x:String>
                                                                                                                    </scg:List>
                                                                                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                      <x:String>application/json</x:String>
                                                                                                                    </scg:List>
                                                                                                                  </scg:List>
                                                                                                                </iai:IONAPIRequestWizard.Headers>
                                                                                                                <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                      <x:String>INBN</x:String>
                                                                                                                      <x:String>RDTP</x:String>
                                                                                                                      <x:String>DIVI</x:String>
                                                                                                                      <x:String>GLAM</x:String>
                                                                                                                    </scg:List>
                                                                                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                      <x:String>inbnValue</x:String>
                                                                                                                      <x:String>3</x:String>
                                                                                                                      <x:String>division</x:String>
                                                                                                                      <x:String>vat</x:String>
                                                                                                                    </scg:List>
                                                                                                                  </scg:List>
                                                                                                                </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                              </iai:IONAPIRequestWizard>
                                                                                                            </If.Then>
                                                                                                            <If.Else>
                                                                                                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_26" Response="[resp]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                                                                                <iai:IONAPIRequestWizard.Headers>
                                                                                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                      <x:String>Accept</x:String>
                                                                                                                    </scg:List>
                                                                                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                      <x:String>application/json</x:String>
                                                                                                                    </scg:List>
                                                                                                                  </scg:List>
                                                                                                                </iai:IONAPIRequestWizard.Headers>
                                                                                                                <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                    <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                                                      <x:String>INBN</x:String>
                                                                                                                      <x:String>RDTP</x:String>
                                                                                                                      <x:String>DIVI</x:String>
                                                                                                                      <x:String>VTA1</x:String>
                                                                                                                      <x:String>VTCD</x:String>
                                                                                                                    </scg:List>
                                                                                                                    <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                                                      <x:String>inbnValue</x:String>
                                                                                                                      <x:String>3</x:String>
                                                                                                                      <x:String>division</x:String>
                                                                                                                      <x:String>vat</x:String>
                                                                                                                      <x:String>vatCode</x:String>
                                                                                                                    </scg:List>
                                                                                                                  </scg:List>
                                                                                                                </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                              </iai:IONAPIRequestWizard>
                                                                                                            </If.Else>
                                                                                                          </If>
                                                                                                        </Sequence>
                                                                                                      </iad:CommentOut.Activities>
                                                                                                    </iad:CommentOut>
                                                                                                  </If.Else>
                                                                                                </If>
                                                                                              </iad:CommentOut.Activities>
                                                                                            </iad:CommentOut>
                                                                                            <If Condition="[AddLineOcrWorkflowStatus= 200]" sap2010:WorkflowViewState.IdRef="If_402">
                                                                                              <If.Then>
                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_505">
                                                                                                  <Sequence.Variables>
                                                                                                    <Variable x:TypeArguments="iru:ResponseObject" Name="resp1" />
                                                                                                  </Sequence.Variables>
                                                                                                  <If Condition="[Math.abs(Convert.ToDecimal(diffamt)*Convert.ToDecimal(qty) + Convert.ToDecimal(chargeDiff)) &gt; 0]" sap2010:WorkflowViewState.IdRef="If_439">
                                                                                                    <If.Then>
                                                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_511">
                                                                                                        <Sequence.Variables>
                                                                                                          <Variable x:TypeArguments="x:String" Name="diffAmt1" />
                                                                                                        </Sequence.Variables>
                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_892">
                                                                                                          <Assign.To>
                                                                                                            <OutArgument x:TypeArguments="x:String">[diffAmt1]</OutArgument>
                                                                                                          </Assign.To>
                                                                                                          <Assign.Value>
                                                                                                            <InArgument x:TypeArguments="x:String">[Math.abs(Convert.ToDecimal(diffamt)*Convert.ToDecimal(qty) + Convert.ToDecimal(chargeDiff)).ToString]</InArgument>
                                                                                                          </Assign.Value>
                                                                                                        </Assign>
                                                                                                        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for adding AddInfo" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_28" Response="[resp1]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddAddInfo&quot;]">
                                                                                                          <iai:IONAPIRequestWizard.Headers>
                                                                                                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                <x:String>Accept</x:String>
                                                                                                              </scg:List>
                                                                                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                <x:String>application/json</x:String>
                                                                                                              </scg:List>
                                                                                                            </scg:List>
                                                                                                          </iai:IONAPIRequestWizard.Headers>
                                                                                                          <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                <x:String>INBN</x:String>
                                                                                                                <x:String>PEXN</x:String>
                                                                                                                <x:String>PEXI</x:String>
                                                                                                                <x:String>DIVI</x:String>
                                                                                                              </scg:List>
                                                                                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                <x:String>inbnValue</x:String>
                                                                                                                <x:String>414</x:String>
                                                                                                                <x:String>diffAmt1</x:String>
                                                                                                                <x:String>division</x:String>
                                                                                                              </scg:List>
                                                                                                            </scg:List>
                                                                                                          </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                        </iai:IONAPIRequestWizard>
                                                                                                      </Sequence>
                                                                                                    </If.Then>
                                                                                                  </If>
                                                                                                  <If Condition="[miscValues(&quot;AutomateValidation&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_437">
                                                                                                    <If.Then>
                                                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_512">
                                                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_449">
                                                                                                          <Sequence.Variables>
                                                                                                            <Variable x:TypeArguments="x:Int32" Name="loopBreak" />
                                                                                                            <Variable x:TypeArguments="x:String" Name="supa" />
                                                                                                            <Variable x:TypeArguments="x:String" Name="inyr" />
                                                                                                          </Sequence.Variables>
                                                                                                          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" Response="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_16" StatusCode="[validateStatus]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS455MI/ValidByBatchNo&quot;]">
                                                                                                            <iai:IONAPIRequestWizard.Headers>
                                                                                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                  <x:String>Accept</x:String>
                                                                                                                </scg:List>
                                                                                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                  <x:String>application/json</x:String>
                                                                                                                </scg:List>
                                                                                                              </scg:List>
                                                                                                            </iai:IONAPIRequestWizard.Headers>
                                                                                                            <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                  <x:String>INBN</x:String>
                                                                                                                  <x:String>DIVI</x:String>
                                                                                                                </scg:List>
                                                                                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                  <x:String>inbnValue</x:String>
                                                                                                                  <x:String>division</x:String>
                                                                                                                </scg:List>
                                                                                                              </scg:List>
                                                                                                            </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                          </iai:IONAPIRequestWizard>
                                                                                                          <Delay Duration="00:00:02" sap2010:WorkflowViewState.IdRef="Delay_4" />
                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_776">
                                                                                                            <Assign.To>
                                                                                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                            </Assign.To>
                                                                                                            <Assign.Value>
                                                                                                              <InArgument x:TypeArguments="x:String">Invoice is successfully created and validated</InArgument>
                                                                                                            </Assign.Value>
                                                                                                          </Assign>
                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_778">
                                                                                                            <Assign.To>
                                                                                                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                                            </Assign.To>
                                                                                                            <Assign.Value>
                                                                                                              <InArgument x:TypeArguments="x:String">SUCCESS</InArgument>
                                                                                                            </Assign.Value>
                                                                                                          </Assign>
                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_910">
                                                                                                            <Assign.To>
                                                                                                              <OutArgument x:TypeArguments="x:String">[sino]</OutArgument>
                                                                                                            </Assign.To>
                                                                                                            <Assign.Value>
                                                                                                              <InArgument x:TypeArguments="x:String">[DictOcrValues("INVOICE_RECEIPT_ID").ToString]</InArgument>
                                                                                                            </Assign.Value>
                                                                                                          </Assign>
                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_911">
                                                                                                            <Assign.To>
                                                                                                              <OutArgument x:TypeArguments="x:String">[inyr]</OutArgument>
                                                                                                            </Assign.To>
                                                                                                            <Assign.Value>
                                                                                                              <InArgument x:TypeArguments="x:String">[ivdate.Substring(0,4)]</InArgument>
                                                                                                            </Assign.Value>
                                                                                                          </Assign>
                                                                                                          <If Condition="[validateStatus = 200 AND NOT httpOut.ToString.Contains(&quot;Not Allocated&quot;)]" sap2010:WorkflowViewState.IdRef="If_401">
                                                                                                            <If.Then>
                                                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_513">
                                                                                                                <If Condition="[miscValues(&quot;AutomateApproval&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_443">
                                                                                                                  <If.Then>
                                                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_514">
                                                                                                                      <InvokeMethod DisplayName="inbn value InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_49" MethodName="Add">
                                                                                                                        <InvokeMethod.TargetObject>
                                                                                                                          <InArgument x:TypeArguments="scg:List(x:String)">[approvalList]</InArgument>
                                                                                                                        </InvokeMethod.TargetObject>
                                                                                                                        <InArgument x:TypeArguments="x:String">[InbnValue]</InArgument>
                                                                                                                      </InvokeMethod>
                                                                                                                      <InvokeMethod DisplayName="sino InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_50" MethodName="Add">
                                                                                                                        <InvokeMethod.TargetObject>
                                                                                                                          <InArgument x:TypeArguments="scg:List(x:String)">[approvalList]</InArgument>
                                                                                                                        </InvokeMethod.TargetObject>
                                                                                                                        <InArgument x:TypeArguments="x:String">[sino]</InArgument>
                                                                                                                      </InvokeMethod>
                                                                                                                      <InvokeMethod DisplayName="vendorID InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_51" MethodName="Add">
                                                                                                                        <InvokeMethod.TargetObject>
                                                                                                                          <InArgument x:TypeArguments="scg:List(x:String)">[approvalList]</InArgument>
                                                                                                                        </InvokeMethod.TargetObject>
                                                                                                                        <InArgument x:TypeArguments="x:String">[vendorID]</InArgument>
                                                                                                                      </InvokeMethod>
                                                                                                                      <InvokeMethod DisplayName="year InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_52" MethodName="Add">
                                                                                                                        <InvokeMethod.TargetObject>
                                                                                                                          <InArgument x:TypeArguments="scg:List(x:String)">[approvalList]</InArgument>
                                                                                                                        </InvokeMethod.TargetObject>
                                                                                                                        <InArgument x:TypeArguments="x:String">[inyr]</InArgument>
                                                                                                                      </InvokeMethod>
                                                                                                                      <InvokeMethod DisplayName="divi InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_53" MethodName="Add">
                                                                                                                        <InvokeMethod.TargetObject>
                                                                                                                          <InArgument x:TypeArguments="scg:List(x:String)">[approvalList]</InArgument>
                                                                                                                        </InvokeMethod.TargetObject>
                                                                                                                        <InArgument x:TypeArguments="x:String">[division]</InArgument>
                                                                                                                      </InvokeMethod>
                                                                                                                    </Sequence>
                                                                                                                  </If.Then>
                                                                                                                </If>
                                                                                                              </Sequence>
                                                                                                            </If.Then>
                                                                                                            <If.Else>
                                                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_448">
                                                                                                                <If Condition="[httpOut.ToString.Contains(&quot;Not Allocated&quot;)]" sap2010:WorkflowViewState.IdRef="If_429">
                                                                                                                  <If.Then>
                                                                                                                    <If Condition="[validateStatus = 200]" sap2010:WorkflowViewState.IdRef="If_428">
                                                                                                                      <If.Then>
                                                                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_485">
                                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_837">
                                                                                                                            <Assign.To>
                                                                                                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                                            </Assign.To>
                                                                                                                            <Assign.Value>
                                                                                                                              <InArgument x:TypeArguments="x:String">["Invoice created and validated in the M3 but amount in invoice is not matched with M3. please verify the amounts."]</InArgument>
                                                                                                                            </Assign.Value>
                                                                                                                          </Assign>
                                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_838">
                                                                                                                            <Assign.To>
                                                                                                                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                                                            </Assign.To>
                                                                                                                            <Assign.Value>
                                                                                                                              <InArgument x:TypeArguments="x:String">SUCCESS</InArgument>
                                                                                                                            </Assign.Value>
                                                                                                                          </Assign>
                                                                                                                        </Sequence>
                                                                                                                      </If.Then>
                                                                                                                      <If.Else>
                                                                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_491">
                                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_849">
                                                                                                                            <Assign.To>
                                                                                                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                                            </Assign.To>
                                                                                                                            <Assign.Value>
                                                                                                                              <InArgument x:TypeArguments="x:String">Invoice created in the M3 but amount in invoice is not matched with M3. please verify the amounts. Error occured while validating the invoice.</InArgument>
                                                                                                                            </Assign.Value>
                                                                                                                          </Assign>
                                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_850">
                                                                                                                            <Assign.To>
                                                                                                                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                                                            </Assign.To>
                                                                                                                            <Assign.Value>
                                                                                                                              <InArgument x:TypeArguments="x:String">SUCCESS</InArgument>
                                                                                                                            </Assign.Value>
                                                                                                                          </Assign>
                                                                                                                        </Sequence>
                                                                                                                      </If.Else>
                                                                                                                    </If>
                                                                                                                  </If.Then>
                                                                                                                  <If.Else>
                                                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_492">
                                                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_851">
                                                                                                                        <Assign.To>
                                                                                                                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                                        </Assign.To>
                                                                                                                        <Assign.Value>
                                                                                                                          <InArgument x:TypeArguments="x:String">Invoice Header and lines are created. Error occured while validating the invoice.</InArgument>
                                                                                                                        </Assign.Value>
                                                                                                                      </Assign>
                                                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_852">
                                                                                                                        <Assign.To>
                                                                                                                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                                                        </Assign.To>
                                                                                                                        <Assign.Value>
                                                                                                                          <InArgument x:TypeArguments="x:String">SUCCESS</InArgument>
                                                                                                                        </Assign.Value>
                                                                                                                      </Assign>
                                                                                                                    </Sequence>
                                                                                                                  </If.Else>
                                                                                                                </If>
                                                                                                              </Sequence>
                                                                                                            </If.Else>
                                                                                                          </If>
                                                                                                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_138" Line="[commentStatus]" Source="[logfile]" />
                                                                                                        </Sequence>
                                                                                                      </Sequence>
                                                                                                    </If.Then>
                                                                                                    <If.Else>
                                                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_506">
                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_880">
                                                                                                          <Assign.To>
                                                                                                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                          </Assign.To>
                                                                                                          <Assign.Value>
                                                                                                            <InArgument x:TypeArguments="x:String">Invoice and Lines created</InArgument>
                                                                                                          </Assign.Value>
                                                                                                        </Assign>
                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_881">
                                                                                                          <Assign.To>
                                                                                                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                                          </Assign.To>
                                                                                                          <Assign.Value>
                                                                                                            <InArgument x:TypeArguments="x:String">SUCCESS</InArgument>
                                                                                                          </Assign.Value>
                                                                                                        </Assign>
                                                                                                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_153" Line="[commentStatus]" Source="[logfile]" />
                                                                                                      </Sequence>
                                                                                                    </If.Else>
                                                                                                  </If>
                                                                                                </Sequence>
                                                                                              </If.Then>
                                                                                              <If.Else>
                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_450">
                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_781">
                                                                                                    <Assign.To>
                                                                                                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                    </Assign.To>
                                                                                                    <Assign.Value>
                                                                                                      <InArgument x:TypeArguments="x:String">Error while adding the lines</InArgument>
                                                                                                    </Assign.Value>
                                                                                                  </Assign>
                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_782">
                                                                                                    <Assign.To>
                                                                                                      <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                                    </Assign.To>
                                                                                                    <Assign.Value>
                                                                                                      <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                                                    </Assign.Value>
                                                                                                  </Assign>
                                                                                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_139" Line="[commentStatus]" Source="[logfile]" />
                                                                                                </Sequence>
                                                                                              </If.Else>
                                                                                            </If>
                                                                                            <If Condition="[approvalRequired]" sap2010:WorkflowViewState.IdRef="If_405">
                                                                                              <If.Then>
                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_453">
                                                                                                  <Sequence.Variables>
                                                                                                    <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="ApprovalOcrWorkflowOutput" />
                                                                                                    <Variable x:TypeArguments="x:Int32" Name="ApprovalOcrWorkflowStatus" />
                                                                                                    <Variable x:TypeArguments="x:String" Name="GUID" />
                                                                                                  </Sequence.Variables>
                                                                                                  <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;VendorID&quot;,vendorId},{&quot;division&quot;,division},{&quot;businessRule&quot;,miscValues(&quot;businessRuleForGUID&quot;).ToString}}]" ContinueOnError="True" DisplayName="Approval Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_43" OutputArguments="[ApprovalOcrWorkflowOutput]" ResponseCode="[ApprovalOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\ApprovalGUID.xaml&quot;]" />
                                                                                                  <If Condition="[ApprovalOcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_404">
                                                                                                    <If.Then>
                                                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_452">
                                                                                                        <Sequence.Variables>
                                                                                                          <Variable x:TypeArguments="x:Int32" Name="sendApprovalResponseCode" />
                                                                                                          <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="sendApprovalResponseDictionary" />
                                                                                                        </Sequence.Variables>
                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_783">
                                                                                                          <Assign.To>
                                                                                                            <OutArgument x:TypeArguments="x:String">[GUID]</OutArgument>
                                                                                                          </Assign.To>
                                                                                                          <Assign.Value>
                                                                                                            <InArgument x:TypeArguments="x:String">[CType(ApprovalOcrWorkflowOutput("GUID"), String)]</InArgument>
                                                                                                          </Assign.Value>
                                                                                                        </Assign>
                                                                                                        <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;amount&quot;,DictOcrValues(&quot;TOTAL&quot;).ToString},{&quot;tenantID&quot;,tenantID},{&quot;division&quot;,DIVISION},{&quot;inbn&quot;,inbnValue},{&quot;invoiceNumber&quot;,DictOcrValues(&quot;INVOICE_RECEIPT_ID&quot;).ToString},{&quot;poNumber&quot;,DictOcrValues(&quot;PO_NUMBER&quot;).ToString},{&quot;vendorName&quot;,vendorId},{&quot;approvalWorkflow&quot;,approvalWorkflow}}]" ContinueOnError="True" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_44" OutputArguments="[sendApprovalResponseDictionary]" ResponseCode="[sendApprovalResponseCode]" WorkflowFile="[projectPath+&quot;\SendtoApprovalWorkflow.xaml&quot;]" />
                                                                                                        <If Condition="[sendApprovalResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_403">
                                                                                                          <If.Then>
                                                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_451">
                                                                                                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_140" Line="Supllier invoice is sent for approval since the invoice amount is large" Source="[logfile]" />
                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_784">
                                                                                                                <Assign.To>
                                                                                                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                                </Assign.To>
                                                                                                                <Assign.Value>
                                                                                                                  <InArgument x:TypeArguments="x:String">["Invoice created and Sent for approval"]</InArgument>
                                                                                                                </Assign.Value>
                                                                                                              </Assign>
                                                                                                            </Sequence>
                                                                                                          </If.Then>
                                                                                                        </If>
                                                                                                      </Sequence>
                                                                                                    </If.Then>
                                                                                                  </If>
                                                                                                </Sequence>
                                                                                              </If.Then>
                                                                                            </If>
                                                                                          </Sequence>
                                                                                        </If.Then>
                                                                                      </If>
                                                                                    </Sequence>
                                                                                  </If.Then>
                                                                                </If>
                                                                              </Sequence>
                                                                            </If.Then>
                                                                            <If.Else>
                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_461">
                                                                                <If Condition="[miscValues(&quot;createInvoiceIrrespectiveTolerance&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_410">
                                                                                  <If.Then>
                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_459">
                                                                                      <Assign DisplayName="Assign ivdate" sap2010:WorkflowViewState.IdRef="Assign_785">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:String">[ivdate]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:String">[DateTime.ParseExact(DictOcrValues("INVOICE_RECEIPT_DATE").ToString,"dd/MM/yyyy",Nothing).ToString("yyyyMMdd")]</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                      <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_786">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:String">[System.DateTime.Now.ToString("yyyyMMddHHmmssfff").tostring + " -"+ DictOcrValues("INVOICE_RECEIPT_ID").ToString + "-" + division + "-" + vendorID]</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                      <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_2">
                                                                                        <TryCatch.Try>
                                                                                          <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_816">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:String">[correlationID.Substring(0,35)]</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                        </TryCatch.Try>
                                                                                        <TryCatch.Catches>
                                                                                          <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_2">
                                                                                            <ActivityAction x:TypeArguments="s:Exception">
                                                                                              <ActivityAction.Argument>
                                                                                                <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                              </ActivityAction.Argument>
                                                                                              <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_817">
                                                                                                <Assign.To>
                                                                                                  <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                                                                                                </Assign.To>
                                                                                                <Assign.Value>
                                                                                                  <InArgument x:TypeArguments="x:String">[correlationID]</InArgument>
                                                                                                </Assign.Value>
                                                                                              </Assign>
                                                                                            </ActivityAction>
                                                                                          </Catch>
                                                                                        </TryCatch.Catches>
                                                                                      </TryCatch>
                                                                                      <If Condition="[authUser.ToUpper() = &quot;SUPPLIERAUTHUSER&quot;]" sap2010:WorkflowViewState.IdRef="If_447">
                                                                                        <If.Then>
                                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_517">
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_915">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="x:String">[authUser]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="x:String">[vendorResult(1)]</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                          </Sequence>
                                                                                        </If.Then>
                                                                                      </If>
                                                                                      <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;bkid&quot;,bkid},{&quot;authUser&quot;,authUser},{&quot;cuam&quot;,DictOcrValues(&quot;TOTAL&quot;).ToString.Replace(&quot;,&quot;,&quot;&quot;)},{&quot;pyme&quot;,pyme},{&quot;tepy&quot;,tepy},{&quot;cucd&quot;,cucd},{&quot;sino&quot;,DictOcrValues(&quot;INVOICE_RECEIPT_ID&quot;).ToString},{&quot;division&quot;,division},{&quot;ivdate&quot;,ivdate},{&quot;SupplierNo&quot;,VendorID},{&quot;correlationID&quot;,correlationID},{&quot;discountTerms&quot;,discountTerms},{&quot;miscValues&quot;,miscValues}}]" ContinueOnError="True" DisplayName="Add Head Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_45" OutputArguments="[AddHeadOcrWorkflowOutput]" ResponseCode="[AddHeadOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\AddHead.xaml&quot;]" />
                                                                                      <If Condition="[AddHeadOcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_409">
                                                                                        <If.Then>
                                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_457">
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_788">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="x:String">[inbnValue]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="x:String">[CType(AddHeadOcrWorkflowOutput("inbnValue"), String)]</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_789">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="x:String">[CType(AddHeadOcrWorkflowOutput("Status"), String)]</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_790">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="x:String">[CType(AddHeadOcrWorkflowOutput("commentStatus"), String)]</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                            <If Condition="[inbnValue &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_408">
                                                                                              <If.Then>
                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_465">
                                                                                                  <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;M3TotalTableRows&quot;,M3TotalTableRows},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;division&quot;,division},{&quot;inbnValue&quot;,inbnValue}}]" ContinueOnError="True" DisplayName="Add Lines Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_46" OutputArguments="[AddLineOcrWorkflowOutput]" ResponseCode="[AddLineOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\AddLine.xaml&quot;]" />
                                                                                                  <If Condition="[AddLineOcrWorkflowStatus= 200]" sap2010:WorkflowViewState.IdRef="If_415">
                                                                                                    <If.Then>
                                                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_467">
                                                                                                        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" Response="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_18" StatusCode="[validateStatus]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS455MI/ValidByBatchNo&quot;]">
                                                                                                          <iai:IONAPIRequestWizard.Headers>
                                                                                                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                <x:String>Accept</x:String>
                                                                                                              </scg:List>
                                                                                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                <x:String>application/json</x:String>
                                                                                                              </scg:List>
                                                                                                            </scg:List>
                                                                                                          </iai:IONAPIRequestWizard.Headers>
                                                                                                          <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                <x:String>INBN</x:String>
                                                                                                                <x:String>DIVI</x:String>
                                                                                                              </scg:List>
                                                                                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                <x:String>inbnValue</x:String>
                                                                                                                <x:String>division</x:String>
                                                                                                              </scg:List>
                                                                                                            </scg:List>
                                                                                                          </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                        </iai:IONAPIRequestWizard>
                                                                                                        <If Condition="[validateStatus = 200]" sap2010:WorkflowViewState.IdRef="If_416">
                                                                                                          <If.Then>
                                                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_468">
                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_801">
                                                                                                                <Assign.To>
                                                                                                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                                </Assign.To>
                                                                                                                <Assign.Value>
                                                                                                                  <InArgument x:TypeArguments="x:String">Invoice created in the M3 but amount in invoice is not matched with M3. please verify the amounts.</InArgument>
                                                                                                                </Assign.Value>
                                                                                                              </Assign>
                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_802">
                                                                                                                <Assign.To>
                                                                                                                  <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                                                </Assign.To>
                                                                                                                <Assign.Value>
                                                                                                                  <InArgument x:TypeArguments="x:String">SUCCESS</InArgument>
                                                                                                                </Assign.Value>
                                                                                                              </Assign>
                                                                                                            </Sequence>
                                                                                                          </If.Then>
                                                                                                          <If.Else>
                                                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_469">
                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_803">
                                                                                                                <Assign.To>
                                                                                                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                                </Assign.To>
                                                                                                                <Assign.Value>
                                                                                                                  <InArgument x:TypeArguments="x:String">["Invoice Header and lines are created. Error occured while validating the invoice."]</InArgument>
                                                                                                                </Assign.Value>
                                                                                                              </Assign>
                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_804">
                                                                                                                <Assign.To>
                                                                                                                  <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                                                </Assign.To>
                                                                                                                <Assign.Value>
                                                                                                                  <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                                                                </Assign.Value>
                                                                                                              </Assign>
                                                                                                            </Sequence>
                                                                                                          </If.Else>
                                                                                                        </If>
                                                                                                      </Sequence>
                                                                                                    </If.Then>
                                                                                                    <If.Else>
                                                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_466">
                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_799">
                                                                                                          <Assign.To>
                                                                                                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                          </Assign.To>
                                                                                                          <Assign.Value>
                                                                                                            <InArgument x:TypeArguments="x:String">["Invoice header added and error occured while adding the lines."]</InArgument>
                                                                                                          </Assign.Value>
                                                                                                        </Assign>
                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_800">
                                                                                                          <Assign.To>
                                                                                                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                                          </Assign.To>
                                                                                                          <Assign.Value>
                                                                                                            <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                                                          </Assign.Value>
                                                                                                        </Assign>
                                                                                                      </Sequence>
                                                                                                    </If.Else>
                                                                                                  </If>
                                                                                                </Sequence>
                                                                                              </If.Then>
                                                                                            </If>
                                                                                          </Sequence>
                                                                                        </If.Then>
                                                                                        <If.Else>
                                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_458">
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_791">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_792">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="x:String">Error occured while adding the lines</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                          </Sequence>
                                                                                        </If.Else>
                                                                                      </If>
                                                                                    </Sequence>
                                                                                  </If.Then>
                                                                                  <If.Else>
                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_460">
                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_793">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:String">["Amount is not allocated with the amount in M3. Please check the delivery note numbers."]</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_794">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:String">NEEDSVERIFICATION</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                    </Sequence>
                                                                                  </If.Else>
                                                                                </If>
                                                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_141" Line="[commentStatus]" Source="[logfile]" />
                                                                              </Sequence>
                                                                            </If.Else>
                                                                          </If>
                                                                        </Sequence>
                                                                      </If.Then>
                                                                    </If>
                                                                  </Sequence>
                                                                </If.Then>
                                                                <If.Else>
                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_464">
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_795">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">An error occurred with the AI Optimizer.</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_796">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_142" Line="[commentStatus]" Source="[logfile]" />
                                                                  </Sequence>
                                                                </If.Else>
                                                              </If>
                                                            </Sequence>
                                                          </If.Then>
                                                        </If>
                                                      </Sequence>
                                                    </ActivityAction>
                                                  </ForEach>
                                                </Sequence>
                                              </If.Then>
                                            </If>
                                          </Sequence>
                                        </If.Then>
                                      </If>
                                    </Sequence>
                                  </Sequence>
                                </If.Else>
                              </If>
                            </Sequence>
                          </Sequence>
                        </If.Then>
                      </If>
                    </Sequence>
                  </If.Then>
                  <If.Else>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_317">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_589">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_590">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">["Invoice number " + DictOcrValues("INVOICE_RECEIPT_ID").ToString+" already exists."]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                  </If.Else>
                </If>
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_292">
                <Sequence.Variables>
                  <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="vendorResponseDictionary" />
                  <Variable x:TypeArguments="x:Int32" Name="vendorResponseCode" />
                </Sequence.Variables>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_541">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[CType(POocrWorkflowOutput("Status"), String)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_542">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[CType(POocrWorkflowOutput("commentStatus"), String)]</InArgument>
                  </Assign.Value>
                </Assign>
                <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;VendorName&quot;,DictOcrValues(&quot;VENDOR_NAME&quot;).ToString},{&quot;VendorAddress&quot;,DictOcrValues(&quot;VENDOR_ADDRESS&quot;).ToString},{&quot;VendorPhone&quot;,DictOcrValues(&quot;VENDOR_PHONE&quot;).ToString}}]" ContinueOnError="True" DisplayName="VendorAddress Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_47" OutputArguments="[vendorResponseDictionary]" ResponseCode="[vendorResponseCode]" WorkflowFile="[projectPath+&quot;\vendorIDAddressMatch.xaml&quot;]" />
                <If Condition="[vendorResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_414">
                  <If.Then>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_798">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("vendorID"), String)]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </If.Then>
                </If>
              </Sequence>
            </If.Else>
          </If>
        </Sequence>
      </If.Then>
      <If.Else>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_293">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_543">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching company number and division for the PO " + pono +"."]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_544">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
            </Assign.Value>
          </Assign>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_105" Line="[commentStatus]" Source="[logfile]" />
        </Sequence>
      </If.Else>
    </If>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_893" sap:VirtualizedContainerService.HintSize="3460,62" />
      <sap2010:ViewStateData Id="Assign_855" sap:VirtualizedContainerService.HintSize="680,62" />
      <sap2010:ViewStateData Id="Assign_856" sap:VirtualizedContainerService.HintSize="680,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_23" sap:VirtualizedContainerService.HintSize="680,22" />
      <sap2010:ViewStateData Id="Assign_857" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_495" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_858" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_496" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_433" sap:VirtualizedContainerService.HintSize="554,340" />
      <sap2010:ViewStateData Id="If_434" sap:VirtualizedContainerService.HintSize="680,494" />
      <sap2010:ViewStateData Id="Sequence_497" sap:VirtualizedContainerService.HintSize="3460,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="3460,62" />
      <sap2010:ViewStateData Id="Assign_630" sap:VirtualizedContainerService.HintSize="3460,62" />
      <sap2010:ViewStateData Id="Assign_631" sap:VirtualizedContainerService.HintSize="3460,62" />
      <sap2010:ViewStateData Id="Assign_685" sap:VirtualizedContainerService.HintSize="3460,62" />
      <sap2010:ViewStateData Id="Assign_136" sap:VirtualizedContainerService.HintSize="3460,62" />
      <sap2010:ViewStateData Id="Assign_686" sap:VirtualizedContainerService.HintSize="3460,62" />
      <sap2010:ViewStateData Id="Assign_687" sap:VirtualizedContainerService.HintSize="3460,62" />
      <sap2010:ViewStateData Id="Assign_137" sap:VirtualizedContainerService.HintSize="3460,62" />
      <sap2010:ViewStateData Id="Assign_602" sap:VirtualizedContainerService.HintSize="3460,62" />
      <sap2010:ViewStateData Id="Assign_603" sap:VirtualizedContainerService.HintSize="3460,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_10" sap:VirtualizedContainerService.HintSize="3460,22" />
      <sap2010:ViewStateData Id="Assign_528" sap:VirtualizedContainerService.HintSize="3212,62" />
      <sap2010:ViewStateData Id="Assign_859" sap:VirtualizedContainerService.HintSize="2964,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_17" sap:VirtualizedContainerService.HintSize="2964,22" />
      <sap2010:ViewStateData Id="Assign_579" sap:VirtualizedContainerService.HintSize="2964,62" />
      <sap2010:ViewStateData Id="Assign_529" sap:VirtualizedContainerService.HintSize="2652,62" />
      <sap2010:ViewStateData Id="Assign_530" sap:VirtualizedContainerService.HintSize="2652,62" />
      <sap2010:ViewStateData Id="Assign_531" sap:VirtualizedContainerService.HintSize="2652,62" />
      <sap2010:ViewStateData Id="Assign_532" sap:VirtualizedContainerService.HintSize="2652,62" />
      <sap2010:ViewStateData Id="Assign_533" sap:VirtualizedContainerService.HintSize="2652,62" />
      <sap2010:ViewStateData Id="Assign_534" sap:VirtualizedContainerService.HintSize="2652,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_11" sap:VirtualizedContainerService.HintSize="2652,22" />
      <sap2010:ViewStateData Id="Assign_546" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_547" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_294" sap:VirtualizedContainerService.HintSize="2504,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_540" sap:VirtualizedContainerService.HintSize="2504,62" />
      <sap2010:ViewStateData Id="Assign_538" sap:VirtualizedContainerService.HintSize="2504,62" />
      <sap2010:ViewStateData Id="Assign_545" sap:VirtualizedContainerService.HintSize="2504,62" />
      <sap2010:ViewStateData Id="Assign_931" sap:VirtualizedContainerService.HintSize="2504,62" />
      <sap2010:ViewStateData Id="Assign_1056" sap:VirtualizedContainerService.HintSize="2504,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_13" sap:VirtualizedContainerService.HintSize="554,22" />
      <sap2010:ViewStateData Id="Assign_548" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_549" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_296" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_550" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_551" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_106" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_297" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_274" sap:VirtualizedContainerService.HintSize="554,504" />
      <sap2010:ViewStateData Id="Sequence_328" sap:VirtualizedContainerService.HintSize="576,690">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_616" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_617" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_329" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_303" sap:VirtualizedContainerService.HintSize="2482,844">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_552" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_553" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_107" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_298" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_878" sap:VirtualizedContainerService.HintSize="2148,62" />
      <sap2010:ViewStateData Id="Assign_556" sap:VirtualizedContainerService.HintSize="1761,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_12" sap:VirtualizedContainerService.HintSize="1761,22" />
      <sap2010:ViewStateData Id="Assign_632" sap:VirtualizedContainerService.HintSize="1514,60" />
      <sap2010:ViewStateData Id="Assign_633" sap:VirtualizedContainerService.HintSize="1514,60" />
      <sap2010:ViewStateData Id="Append_Line_118" sap:VirtualizedContainerService.HintSize="287,22" />
      <sap2010:ViewStateData Id="InvokeMethod_27" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="Sequence_353" sap:VirtualizedContainerService.HintSize="240,252">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_53" sap:VirtualizedContainerService.HintSize="287,400" />
      <sap2010:ViewStateData Id="Sequence_354" sap:VirtualizedContainerService.HintSize="309,586">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_863" sap:VirtualizedContainerService.HintSize="1158,60" />
      <sap2010:ViewStateData Id="Assign_864" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_865" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_150" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_866" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_499" sap:VirtualizedContainerService.HintSize="264,446">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_885" sap:VirtualizedContainerService.HintSize="825,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_48" sap:VirtualizedContainerService.HintSize="825,22" />
      <sap2010:ViewStateData Id="Assign_886" sap:VirtualizedContainerService.HintSize="678,60" />
      <sap2010:ViewStateData Id="Assign_887" sap:VirtualizedContainerService.HintSize="678,60" />
      <sap2010:ViewStateData Id="Assign_888" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_889" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_154" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_890" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_509" sap:VirtualizedContainerService.HintSize="264,446">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_875" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_876" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_152" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_877" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_510" sap:VirtualizedContainerService.HintSize="264,446">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_442" sap:VirtualizedContainerService.HintSize="553,594" />
      <sap2010:ViewStateData Id="If_441" sap:VirtualizedContainerService.HintSize="678,742" />
      <sap2010:ViewStateData Id="Sequence_508" sap:VirtualizedContainerService.HintSize="700,1066">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_440" sap:VirtualizedContainerService.HintSize="825,1214" />
      <sap2010:ViewStateData Id="Sequence_504" sap:VirtualizedContainerService.HintSize="847,1500">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_502" sap:VirtualizedContainerService.HintSize="869,1624">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_436" sap:VirtualizedContainerService.HintSize="1158,1772" />
      <sap2010:ViewStateData Id="Sequence_503" sap:VirtualizedContainerService.HintSize="1180,1996">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_315" sap:VirtualizedContainerService.HintSize="1514,2144" />
      <sap2010:ViewStateData Id="Sequence_348" sap:VirtualizedContainerService.HintSize="1536,2468">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_127" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_314" sap:VirtualizedContainerService.HintSize="1761,2616" />
      <sap2010:ViewStateData Id="Sequence_347" sap:VirtualizedContainerService.HintSize="1783,2902">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_51" sap:VirtualizedContainerService.HintSize="2148,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_688" sap:VirtualizedContainerService.HintSize="2000,62" />
      <sap2010:ViewStateData Id="Assign_879" sap:VirtualizedContainerService.HintSize="2000,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_11" sap:VirtualizedContainerService.HintSize="612.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_924" sap:VirtualizedContainerService.HintSize="529.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_925" sap:VirtualizedContainerService.HintSize="529.333333333333,62" />
      <sap2010:ViewStateData Id="InvokeMethod_58" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="Assign_926" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_59" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Sequence_524" sap:VirtualizedContainerService.HintSize="264,360">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_452" sap:VirtualizedContainerService.HintSize="217.333333333333,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_525" sap:VirtualizedContainerService.HintSize="239.333333333333,350.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_927" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_928" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_60" sap:VirtualizedContainerService.HintSize="464,134" />
      <sap2010:ViewStateData Id="Assign_929" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_61" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Sequence_526" sap:VirtualizedContainerService.HintSize="264,360">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_453" sap:VirtualizedContainerService.HintSize="464,514" />
      <sap2010:ViewStateData Id="Sequence_527" sap:VirtualizedContainerService.HintSize="486,812">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_454" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_528" sap:VirtualizedContainerService.HintSize="264,380.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_455" sap:VirtualizedContainerService.HintSize="529.333333333333,534.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_529" sap:VirtualizedContainerService.HintSize="551.333333333333,862.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_82" sap:VirtualizedContainerService.HintSize="582,1015.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_62" sap:VirtualizedContainerService.HintSize="612.666666666667,1168">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_693" sap:VirtualizedContainerService.HintSize="612.666666666667,62" />
      <sap2010:ViewStateData Id="InvokeMethod_39" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="Sequence_400" sap:VirtualizedContainerService.HintSize="240,252">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_401" sap:VirtualizedContainerService.HintSize="262,376">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_353" sap:VirtualizedContainerService.HintSize="464,524" />
      <sap2010:ViewStateData Id="InvokeMethod_40" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="Sequence_402" sap:VirtualizedContainerService.HintSize="240,252">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_354" sap:VirtualizedContainerService.HintSize="729,672" />
      <sap2010:ViewStateData Id="ForEach`1_63" sap:VirtualizedContainerService.HintSize="612.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_694" sap:VirtualizedContainerService.HintSize="284.666666666667,62" />
      <sap2010:ViewStateData Id="Sequence_403" sap:VirtualizedContainerService.HintSize="634.666666666667,1650.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_355" sap:VirtualizedContainerService.HintSize="2000,1804.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_749" sap:VirtualizedContainerService.HintSize="2000,62" />
      <sap2010:ViewStateData Id="Assign_750" sap:VirtualizedContainerService.HintSize="2000,62" />
      <sap2010:ViewStateData Id="InvokeMethod_62" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="If_522" sap:VirtualizedContainerService.HintSize="464,276" />
      <sap2010:ViewStateData Id="InvokeMethod_63" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="If_523" sap:VirtualizedContainerService.HintSize="707,424">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_68" sap:VirtualizedContainerService.HintSize="737,572" />
      <sap2010:ViewStateData Id="InvokeMethod_44" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="If_386" sap:VirtualizedContainerService.HintSize="2000,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_751" sap:VirtualizedContainerService.HintSize="1852,62" />
      <sap2010:ViewStateData Id="Assign_752" sap:VirtualizedContainerService.HintSize="1799,60" />
      <sap2010:ViewStateData Id="Assign_695" sap:VirtualizedContainerService.HintSize="1652,60" />
      <sap2010:ViewStateData Id="Assign_696" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_697" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_356" sap:VirtualizedContainerService.HintSize="509,208" />
      <sap2010:ViewStateData Id="Assign_698" sap:VirtualizedContainerService.HintSize="509,60" />
      <sap2010:ViewStateData Id="Sequence_404" sap:VirtualizedContainerService.HintSize="531,432">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_64" sap:VirtualizedContainerService.HintSize="1652,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_753" sap:VirtualizedContainerService.HintSize="1652,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_38" sap:VirtualizedContainerService.HintSize="1652,22" />
      <sap2010:ViewStateData Id="Assign_754" sap:VirtualizedContainerService.HintSize="1341,60" />
      <sap2010:ViewStateData Id="Assign_755" sap:VirtualizedContainerService.HintSize="1341,60" />
      <sap2010:ViewStateData Id="Assign_756" sap:VirtualizedContainerService.HintSize="1341,60" />
      <sap2010:ViewStateData Id="Assign_757" sap:VirtualizedContainerService.HintSize="736,60" />
      <sap2010:ViewStateData Id="Assign_758" sap:VirtualizedContainerService.HintSize="736,60" />
      <sap2010:ViewStateData Id="Assign_759" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_760" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_439" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_761" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_440" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_389" sap:VirtualizedContainerService.HintSize="553,332">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_762" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Sequence_441" sap:VirtualizedContainerService.HintSize="575,656">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_71" sap:VirtualizedContainerService.HintSize="736,804" />
      <sap2010:ViewStateData Id="InvokeWorkflow_39" sap:VirtualizedContainerService.HintSize="736,22" />
      <sap2010:ViewStateData Id="Assign_763" sap:VirtualizedContainerService.HintSize="589,60" />
      <sap2010:ViewStateData Id="Assign_764" sap:VirtualizedContainerService.HintSize="589,60" />
      <sap2010:ViewStateData Id="Assign_765" sap:VirtualizedContainerService.HintSize="589,60" />
      <sap2010:ViewStateData Id="Assign_766" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_812" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_473" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_390" sap:VirtualizedContainerService.HintSize="464,432" />
      <sap2010:ViewStateData Id="If_391" sap:VirtualizedContainerService.HintSize="589,580" />
      <sap2010:ViewStateData Id="Sequence_442" sap:VirtualizedContainerService.HintSize="611,1004">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_392" sap:VirtualizedContainerService.HintSize="736,1152" />
      <sap2010:ViewStateData Id="Sequence_443" sap:VirtualizedContainerService.HintSize="758,2382">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_393" sap:VirtualizedContainerService.HintSize="883,2530" />
      <sap2010:ViewStateData Id="Sequence_444" sap:VirtualizedContainerService.HintSize="905,2654">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_813" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_445" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_394" sap:VirtualizedContainerService.HintSize="1194,2802">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_767" sap:VirtualizedContainerService.HintSize="1194,60" />
      <sap2010:ViewStateData Id="Assign_768" sap:VirtualizedContainerService.HintSize="1137,60" />
      <sap2010:ViewStateData Id="Assign_769" sap:VirtualizedContainerService.HintSize="1137,60" />
      <sap2010:ViewStateData Id="Assign_770" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_814" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Catch`1_1" sap:VirtualizedContainerService.HintSize="404,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_1" sap:VirtualizedContainerService.HintSize="1137,287" />
      <sap2010:ViewStateData Id="Assign_797" sap:VirtualizedContainerService.HintSize="1137,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_49" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_914" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_446" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Assign_913" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Sequence_516" sap:VirtualizedContainerService.HintSize="486,494">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_912" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_515" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_444" sap:VirtualizedContainerService.HintSize="464,332" />
      <sap2010:ViewStateData Id="If_445" sap:VirtualizedContainerService.HintSize="1137,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_40" sap:VirtualizedContainerService.HintSize="1137,22" />
      <sap2010:ViewStateData Id="Assign_771" sap:VirtualizedContainerService.HintSize="990,60" />
      <sap2010:ViewStateData Id="Assign_772" sap:VirtualizedContainerService.HintSize="990,60" />
      <sap2010:ViewStateData Id="Assign_773" sap:VirtualizedContainerService.HintSize="990,60" />
      <sap2010:ViewStateData Id="Assign_774" sap:VirtualizedContainerService.HintSize="843,60" />
      <sap2010:ViewStateData Id="Assign_829" sap:VirtualizedContainerService.HintSize="843,60" />
      <sap2010:ViewStateData Id="Assign_830" sap:VirtualizedContainerService.HintSize="843,60" />
      <sap2010:ViewStateData Id="Assign_823" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_824" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="InvokeMethod_46" sap:VirtualizedContainerService.HintSize="242,128" />
      <sap2010:ViewStateData Id="Sequence_480" sap:VirtualizedContainerService.HintSize="264,452">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_75" sap:VirtualizedContainerService.HintSize="294,600" />
      <sap2010:ViewStateData Id="Assign_827" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_828" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="InvokeMethod_48" sap:VirtualizedContainerService.HintSize="242,128" />
      <sap2010:ViewStateData Id="Sequence_482" sap:VirtualizedContainerService.HintSize="264,452">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_425" sap:VirtualizedContainerService.HintSize="464,600" />
      <sap2010:ViewStateData Id="ForEach`1_78" sap:VirtualizedContainerService.HintSize="494,748" />
      <sap2010:ViewStateData Id="ForEach`1_79" sap:VirtualizedContainerService.HintSize="524,896" />
      <sap2010:ViewStateData Id="If_424" sap:VirtualizedContainerService.HintSize="843,1044">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_805" sap:VirtualizedContainerService.HintSize="843,60" />
      <sap2010:ViewStateData Id="Assign_819" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_476" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_419" sap:VirtualizedContainerService.HintSize="464,340" />
      <sap2010:ViewStateData Id="Sequence_477" sap:VirtualizedContainerService.HintSize="486,464">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_820" sap:VirtualizedContainerService.HintSize="590,62" />
      <sap2010:ViewStateData Id="Assign_821" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_822" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_478" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_420" sap:VirtualizedContainerService.HintSize="464,442" />
      <sap2010:ViewStateData Id="If_421" sap:VirtualizedContainerService.HintSize="590,596" />
      <sap2010:ViewStateData Id="Sequence_479" sap:VirtualizedContainerService.HintSize="612,822">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_422" sap:VirtualizedContainerService.HintSize="843,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_806" sap:VirtualizedContainerService.HintSize="843,60" />
      <sap2010:ViewStateData Id="Assign_831" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_832" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_426" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Sequence_483" sap:VirtualizedContainerService.HintSize="486,432">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_427" sap:VirtualizedContainerService.HintSize="843,580" />
      <sap2010:ViewStateData Id="Assign_1054" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="ForEach`1_74" sap:VirtualizedContainerService.HintSize="287,208" />
      <sap2010:ViewStateData Id="If_418" sap:VirtualizedContainerService.HintSize="843,356">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_41" sap:VirtualizedContainerService.HintSize="843,22" />
      <sap2010:ViewStateData Id="Assign_883" sap:VirtualizedContainerService.HintSize="843,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_42" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_884" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_507" sap:VirtualizedContainerService.HintSize="264,246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_396" sap:VirtualizedContainerService.HintSize="464,394" />
      <sap2010:ViewStateData Id="If_397" sap:VirtualizedContainerService.HintSize="843,542" />
      <sap2010:ViewStateData Id="Assign_1052" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_1053" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_35" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_36" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_520" sap:VirtualizedContainerService.HintSize="464,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_2" sap:VirtualizedContainerService.HintSize="242,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_62" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_610" sap:VirtualizedContainerService.HintSize="264,344">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_1055" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_63" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_611" sap:VirtualizedContainerService.HintSize="264,246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_524" sap:VirtualizedContainerService.HintSize="464,394">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_521" sap:VirtualizedContainerService.HintSize="843,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_775" sap:VirtualizedContainerService.HintSize="464,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_882" sap:VirtualizedContainerService.HintSize="464,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_25" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_26" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_438" sap:VirtualizedContainerService.HintSize="464,212" />
      <sap2010:ViewStateData Id="Sequence_446" sap:VirtualizedContainerService.HintSize="200,52">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_1" sap:VirtualizedContainerService.HintSize="192.666666666667,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_398" sap:VirtualizedContainerService.HintSize="464,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_3" sap:VirtualizedContainerService.HintSize="843,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_892" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_28" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_511" sap:VirtualizedContainerService.HintSize="264,246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_439" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_16" sap:VirtualizedContainerService.HintSize="1378,22" />
      <sap2010:ViewStateData Id="Delay_4" sap:VirtualizedContainerService.HintSize="1378,22" />
      <sap2010:ViewStateData Id="Assign_776" sap:VirtualizedContainerService.HintSize="1378,60" />
      <sap2010:ViewStateData Id="Assign_778" sap:VirtualizedContainerService.HintSize="1378,60" />
      <sap2010:ViewStateData Id="Assign_910" sap:VirtualizedContainerService.HintSize="1378,60" />
      <sap2010:ViewStateData Id="Assign_911" sap:VirtualizedContainerService.HintSize="1378,61.3333333333333" />
      <sap2010:ViewStateData Id="InvokeMethod_49" sap:VirtualizedContainerService.HintSize="218,132" />
      <sap2010:ViewStateData Id="InvokeMethod_50" sap:VirtualizedContainerService.HintSize="218,134" />
      <sap2010:ViewStateData Id="InvokeMethod_51" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="InvokeMethod_52" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="InvokeMethod_53" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="Sequence_514" sap:VirtualizedContainerService.HintSize="240,934">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_443" sap:VirtualizedContainerService.HintSize="464,1086" />
      <sap2010:ViewStateData Id="Sequence_513" sap:VirtualizedContainerService.HintSize="486,1210">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_837" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_838" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_485" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_849" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_850" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_491" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_428" sap:VirtualizedContainerService.HintSize="554,438" />
      <sap2010:ViewStateData Id="Assign_851" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_852" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_492" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_429" sap:VirtualizedContainerService.HintSize="844,592" />
      <sap2010:ViewStateData Id="Sequence_448" sap:VirtualizedContainerService.HintSize="866,716">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_401" sap:VirtualizedContainerService.HintSize="1378,1362" />
      <sap2010:ViewStateData Id="Append_Line_138" sap:VirtualizedContainerService.HintSize="1375,22" />
      <sap2010:ViewStateData Id="Sequence_449" sap:VirtualizedContainerService.HintSize="1400,2073">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_512" sap:VirtualizedContainerService.HintSize="1422,2197">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_880" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_881" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_153" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_506" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_437" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_505" sap:VirtualizedContainerService.HintSize="222,266">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_781" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_782" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_139" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_450" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_402" sap:VirtualizedContainerService.HintSize="843,494">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_43" sap:VirtualizedContainerService.HintSize="611,22" />
      <sap2010:ViewStateData Id="Assign_783" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_44" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Append_Line_140" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_784" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_451" sap:VirtualizedContainerService.HintSize="264,246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_403" sap:VirtualizedContainerService.HintSize="464,394" />
      <sap2010:ViewStateData Id="Sequence_452" sap:VirtualizedContainerService.HintSize="486,680">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_404" sap:VirtualizedContainerService.HintSize="611,828" />
      <sap2010:ViewStateData Id="Sequence_453" sap:VirtualizedContainerService.HintSize="633,1014">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_405" sap:VirtualizedContainerService.HintSize="612,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_454" sap:VirtualizedContainerService.HintSize="865,4335">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_406" sap:VirtualizedContainerService.HintSize="990,4483">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_455" sap:VirtualizedContainerService.HintSize="1012,4907">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_407" sap:VirtualizedContainerService.HintSize="1137,5055" />
      <sap2010:ViewStateData Id="Sequence_456" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_785" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_786" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_816" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_817" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Catch`1_2" sap:VirtualizedContainerService.HintSize="404,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_2" sap:VirtualizedContainerService.HintSize="464,287" />
      <sap2010:ViewStateData Id="Assign_915" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_517" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_447" sap:VirtualizedContainerService.HintSize="464,332" />
      <sap2010:ViewStateData Id="InvokeWorkflow_45" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_788" sap:VirtualizedContainerService.HintSize="1011,60" />
      <sap2010:ViewStateData Id="Assign_789" sap:VirtualizedContainerService.HintSize="1011,60" />
      <sap2010:ViewStateData Id="Assign_790" sap:VirtualizedContainerService.HintSize="1011,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_46" sap:VirtualizedContainerService.HintSize="864,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_18" sap:VirtualizedContainerService.HintSize="553,22" />
      <sap2010:ViewStateData Id="Assign_801" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_802" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_468" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_803" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_804" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_469" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_416" sap:VirtualizedContainerService.HintSize="553,432" />
      <sap2010:ViewStateData Id="Sequence_467" sap:VirtualizedContainerService.HintSize="575,618">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_799" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_800" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_466" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_415" sap:VirtualizedContainerService.HintSize="864,766" />
      <sap2010:ViewStateData Id="Sequence_465" sap:VirtualizedContainerService.HintSize="886,952">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_408" sap:VirtualizedContainerService.HintSize="1011,1100">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_457" sap:VirtualizedContainerService.HintSize="1033,1524">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_791" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_792" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_458" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_409" sap:VirtualizedContainerService.HintSize="464,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_459" sap:VirtualizedContainerService.HintSize="486,1136">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_793" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_794" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_460" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_410" sap:VirtualizedContainerService.HintSize="775,1284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_141" sap:VirtualizedContainerService.HintSize="775,22" />
      <sap2010:ViewStateData Id="Sequence_461" sap:VirtualizedContainerService.HintSize="797,1470">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_411" sap:VirtualizedContainerService.HintSize="1194,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_462" sap:VirtualizedContainerService.HintSize="1216,3117">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_412" sap:VirtualizedContainerService.HintSize="1341,3265" />
      <sap2010:ViewStateData Id="Sequence_463" sap:VirtualizedContainerService.HintSize="1363,3689">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_795" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_796" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_142" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_464" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_413" sap:VirtualizedContainerService.HintSize="1652,3837">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_438" sap:VirtualizedContainerService.HintSize="1674,4314">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_388" sap:VirtualizedContainerService.HintSize="1799,4462">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_437" sap:VirtualizedContainerService.HintSize="1821,4686">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_70" sap:VirtualizedContainerService.HintSize="1852,4838.66666666667" />
      <sap2010:ViewStateData Id="Sequence_436" sap:VirtualizedContainerService.HintSize="1874,5064.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_387" sap:VirtualizedContainerService.HintSize="2000,5218.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_427" sap:VirtualizedContainerService.HintSize="2022,7688">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_380" sap:VirtualizedContainerService.HintSize="2148,7842">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_299" sap:VirtualizedContainerService.HintSize="2170,8160.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_346" sap:VirtualizedContainerService.HintSize="2192,8284.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_275" sap:VirtualizedContainerService.HintSize="2482,8438.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_295" sap:VirtualizedContainerService.HintSize="2504,9446.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_290" sap:VirtualizedContainerService.HintSize="2526,10408.6666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_271" sap:VirtualizedContainerService.HintSize="2652,10562.6666666667" />
      <sap2010:ViewStateData Id="Sequence_291" sap:VirtualizedContainerService.HintSize="2674,11360.6666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_589" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_590" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_317" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_286" sap:VirtualizedContainerService.HintSize="2964,11514.6666666667" />
      <sap2010:ViewStateData Id="Sequence_310" sap:VirtualizedContainerService.HintSize="2986,11904.6666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_541" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_542" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_47" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_798" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_414" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Sequence_292" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_273" sap:VirtualizedContainerService.HintSize="3212,12058.6666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_287" sap:VirtualizedContainerService.HintSize="3234,12284.6666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_543" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_544" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_105" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_293" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_270" sap:VirtualizedContainerService.HintSize="3460,12438.6666666667" />
      <sap2010:ViewStateData Id="Sequence_17" sap:VirtualizedContainerService.HintSize="3482,13839.3333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="3522,14359.3333333333" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>