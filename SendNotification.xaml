﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="invoiceNumber" Type="InArgument(x:String)" />
    <x:Property Name="poNumber" Type="InArgument(x:String)" />
    <x:Property Name="invoiceDate" Type="InArgument(x:String)" />
    <x:Property Name="total" Type="InArgument(x:String)" />
    <x:Property Name="tax" Type="InArgument(x:String)" />
    <x:Property Name="drillbackLink" Type="InArgument(x:String)" />
    <x:Property Name="status" Type="InArgument(x:String)" />
    <x:Property Name="statusComments" Type="InArgument(x:String)" />
    <x:Property Name="userIdentifier" Type="InArgument(x:String)" />
    <x:Property Name="distributionType" Type="InArgument(x:String)" />
    <x:Property Name="emailSubject" Type="InArgument(x:String)" />
    <x:Property Name="emailReceivedTime" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="subTotal" Type="InArgument(x:String)" />
    <x:Property Name="charges" Type="InArgument(x:String)" />
    <x:Property Name="discount" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="APResp" Type="InArgument(x:String)" />
    <x:Property Name="deliveryNote" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>System.Text.RegularExpressions</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_1">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="message" />
      <Variable x:TypeArguments="x:String" Name="drillbackUrl" />
      <Variable x:TypeArguments="x:String" Name="notificationRequestStr" />
      <Variable x:TypeArguments="njl:JToken" Name="notificationToken" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="notificationResponse" />
      <Variable x:TypeArguments="x:Int32" Name="notificationResponseCode" />
    </Sequence.Variables>
    <If Condition="[emailSubject.contains(&quot;'&quot;)]" sap2010:WorkflowViewState.IdRef="If_2">
      <If.Then>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_3">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[emailSubject]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[emailSubject.Replace("'","")]</InArgument>
          </Assign.Value>
        </Assign>
      </If.Then>
    </If>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_1">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[message]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">["RPA M3 IP" +" - " + status + "( "+statusComments+")"]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_2">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[drillbackUrl]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[tenantId+ "?" + drillbackLink]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_5">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[deliveryNote]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[deliveryNote.Replace(Environment.NewLine, "").Replace(" ","")]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_7">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[deliveryNote]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[Regex.Replace(deliveryNote, "[^a-zA-Z0-9,]", "").ToString]</InArgument>
      </Assign.Value>
    </Assign>
    <If Condition="[deliveryNote &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_3">
      <If.Then>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_4">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[deliveryNote]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[deliveryNote.Replace("""","").Replace("\r\n","").Replace("vbLf", "")]</InArgument>
          </Assign.Value>
        </Assign>
      </If.Then>
    </If>
    <If Condition="[status = &quot;SUCCESS&quot;]" sap2010:WorkflowViewState.IdRef="If_1">
      <If.Then>
        <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_1" Template="{}{ 'message': '{{%msg%}}',  'parameters': [{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_STATUS', 'value':  '{{%status%}}', 'label': 'Status', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_Subject', 'value':  '{{%Subject%}}', 'label': 'Subject', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_RcvdTime', 'value':  '{{%RcvdTime%}}', 'label': 'Recieved Time', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_INVNO', 'value':  '{{%InvNumber%}}', 'label': 'Invoice Number', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_PONO', 'value':  '{{%PONumber%}}', 'label': 'PO Number', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_INVDate', 'value':  '{{%InvDate%}}', 'label': 'Invoice Date', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_SubTotal', 'value':  '{{%subTotal%}}', 'label': 'Sub Total', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_Tax', 'value':  '{{%Tax%}}', 'label': 'Tax', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_Charges', 'value':  '{{%Charges%}}', 'label': 'Shipping Charges', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_Discount', 'value':  '{{%Discount%}}', 'label': 'Discount', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_Total', 'value':  '{{%Total%}}', 'label': 'Total', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_DeliveryNote', 'value':  '{{%DeliveryNote%}}', 'label': 'Delivery Note Numbers', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_Remarks', 'value':  '{{%Remarks%}}', 'label': 'Remarks', 'readOnly': true }],'views': [{'name':'LinkToDashboard','label': 'Invoice','properties': [{'name': '{{%drill%}}','value': '{{%totalDrill%}}'}]}], 'category': 'RPA', 'distribution': [{ 'identifier': '{{%userIdentifier%}}','type': '{{%distributionType%}}','sendMail': false }] }" Text="[notificationRequestStr]">
          <ias:Template_Apply.Values>
            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
              <scg:List x:TypeArguments="x:String" Capacity="32">
                <x:String>status</x:String>
                <x:String>userIdentifier</x:String>
                <x:String>distributionType</x:String>
                <x:String>InvNumber</x:String>
                <x:String>PONumber</x:String>
                <x:String>InvDate</x:String>
                <x:String>subTotal</x:String>
                <x:String>Tax</x:String>
                <x:String>Total</x:String>
                <x:String>Remarks</x:String>
                <x:String>RcvdTime</x:String>
                <x:String>Subject</x:String>
                <x:String>msg</x:String>
                <x:String>drill</x:String>
                <x:String>totalDrill</x:String>
                <x:String>Charges</x:String>
                <x:String>Discount</x:String>
                <x:String>DeliveryNote</x:String>
              </scg:List>
              <scg:List x:TypeArguments="x:String" Capacity="32">
                <x:String>status</x:String>
                <x:String>userIdentifier</x:String>
                <x:String>distributionType</x:String>
                <x:String>invoiceNumber</x:String>
                <x:String>poNumber</x:String>
                <x:String>invoiceDate</x:String>
                <x:String>subTotal</x:String>
                <x:String>tax</x:String>
                <x:String>total</x:String>
                <x:String>statusComments</x:String>
                <x:String>emailReceivedTime</x:String>
                <x:String>emailSubject</x:String>
                <x:String>message</x:String>
                <x:String>drillbackLink</x:String>
                <x:String>drillbackUrl</x:String>
                <x:String>charges</x:String>
                <x:String>discount</x:String>
                <x:String>deliveryNote</x:String>
              </scg:List>
            </scg:List>
          </ias:Template_Apply.Values>
        </ias:Template_Apply>
      </If.Then>
      <If.Else>
        <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_2" Template="{}{ 'message': '{{%msg%}}',  'parameters': [{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_STATUS', 'value':  '{{%status%}}', 'label': 'Status', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_Subject', 'value':  '{{%Subject%}}', 'label': 'Subject', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_RcvdTime', 'value':  '{{%RcvdTime%}}', 'label': 'Recieved Time', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_INVNO', 'value':  '{{%InvNumber%}}', 'label': 'Invoice Number', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_PONO', 'value':  '{{%PONumber%}}', 'label': 'PO Number', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_INVDate', 'value':  '{{%InvDate%}}', 'label': 'Invoice Date', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_SubTotal', 'value':  '{{%subTotal%}}', 'label': 'Sub Total', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_Tax', 'value':  '{{%Tax%}}', 'label': 'Tax', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_Charges', 'value':  '{{%Charges%}}', 'label': 'Shipping Charges', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_Discount', 'value':  '{{%Discount%}}', 'label': 'Discount', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_Total', 'value':  '{{%Total%}}', 'label': 'Total', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_DeliveryNote', 'value':  '{{%DeliveryNote%}}', 'label': 'Delivery Note Numbers', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_Remarks', 'value':  '{{%Remarks%}}', 'label': 'Remarks', 'readOnly': true }], 'category': 'RPA', 'distribution': [{ 'identifier': '{{%userIdentifier%}}','type': '{{%distributionType%}}','sendMail': false }] }" Text="[notificationRequestStr]">
          <ias:Template_Apply.Values>
            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
              <scg:List x:TypeArguments="x:String" Capacity="32">
                <x:String>status</x:String>
                <x:String>userIdentifier</x:String>
                <x:String>distributionType</x:String>
                <x:String>InvNumber</x:String>
                <x:String>PONumber</x:String>
                <x:String>InvDate</x:String>
                <x:String>subTotal</x:String>
                <x:String>Tax</x:String>
                <x:String>Total</x:String>
                <x:String>Remarks</x:String>
                <x:String>RcvdTime</x:String>
                <x:String>Subject</x:String>
                <x:String>msg</x:String>
                <x:String>drill</x:String>
                <x:String>totalDrill</x:String>
                <x:String>Charges</x:String>
                <x:String>Discount</x:String>
                <x:String>DeliveryNote</x:String>
              </scg:List>
              <scg:List x:TypeArguments="x:String" Capacity="32">
                <x:String>status</x:String>
                <x:String>userIdentifier</x:String>
                <x:String>distributionType</x:String>
                <x:String>invoiceNumber</x:String>
                <x:String>poNumber</x:String>
                <x:String>invoiceDate</x:String>
                <x:String>subTotal</x:String>
                <x:String>tax</x:String>
                <x:String>total</x:String>
                <x:String>statusComments</x:String>
                <x:String>emailReceivedTime</x:String>
                <x:String>emailSubject</x:String>
                <x:String>message</x:String>
                <x:String>drillbackLink</x:String>
                <x:String>drillbackUrl</x:String>
                <x:String>charges</x:String>
                <x:String>discount</x:String>
                <x:String>deliveryNote</x:String>
              </scg:List>
            </scg:List>
          </ias:Template_Apply.Values>
        </ias:Template_Apply>
      </If.Else>
    </If>
    <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_1" JTokenObject="[notificationToken]" JTokenString="[notificationRequestStr]" />
    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_1" PostData="[notificationToken.tostring]" Response="[notificationResponse]" ResponseCode="[notificationResponseCode]" Url="[tenantID+ &quot;IONSERVICES/process/application/v1/pulse/notification/create&quot;]">
      <iai:IONAPIRequestWizard.Headers>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>Accept</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>application/json</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.Headers>
      <iai:IONAPIRequestWizard.QueryParameters>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>logicalId</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>lid://infor.rpa.1</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.QueryParameters>
    </iai:IONAPIRequestWizard>
    <sads:DebugSymbol.Symbol>d1xDOlxVc2Vyc1xza2F2aXJheWFuaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYzXFNlbmROb3RpZmljYXRpb24ueGFtbCdTA44CDgIBAVwFZwoCATZoBW8OAgEwcAV3DgIBKngFfw4CASaAAQWHAQ4CASKIAQWTAQoCARqUAQX1AQoCAQ/2AQX2AeYBAgEK9wEFjAIfAgECXBNcOwIBN14JZRICATltMG1nAgEzajFqOgIBMXUwdU8CAS1yMXI/AgErfTB9bwIBKXoxej8CASeFATCFAWsCASWCATGCAT8CASOIAROIATkCARuKAQmRARICAR2UAROUATMCARCWAQnDAR4CARbGAQnzAR4CARL2AckB9gHjAQIBDfYBpgH2AbsBAgEL9wH1AfcBkwICAQn3AcMC9wHfAgIBB/cBnQL3AbUCAgEF9wHkAvcBugMCAQNjNGNSAgE8YDVgQwIBOo8BNI8BegIBIIwBNYwBQwIBHpYBnRGWAbcRAgEYlgGnAZYBlxECARfGAaAQxgG6EAIBFMYBpwHGAZoQAgET</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_7" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Template_Apply_1" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Template_Apply_2" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="DeserializeJSON_1" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_1" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="486,1352">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="526,1552" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>