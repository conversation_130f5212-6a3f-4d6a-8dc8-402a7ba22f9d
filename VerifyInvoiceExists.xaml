﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow" this:Workflow.tenantID="https://mingle-ionapi.inforcloudsuite.com/MANDALA_DEM/" this:Workflow.SupInvoiceNo="INVGD001" this:Workflow.division="BBC" this:Workflow.AYear="2025" this:Workflow.vendorID="20FGCASE"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:this="clr-namespace:RehostedWorkflowDesigner"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="logfile" Type="InArgument(x:String)" />
    <x:Property Name="invoiceExist" Type="OutArgument(x:Boolean)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="SupInvoiceNo" Type="InArgument(x:String)" />
    <x:Property Name="division" Type="InArgument(x:String)" />
    <x:Property Name="AYear" Type="InArgument(x:String)" />
    <x:Property Name="vendorID" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>System.Windows.Controls</x:String>
      <x:String>System.Windows.Documents</x:String>
      <x:String>System.Windows.Shapes</x:String>
      <x:String>System.Windows.Shell</x:String>
      <x:String>System.Windows.Navigation</x:String>
      <x:String>System.Windows.Data</x:String>
      <x:String>System.Windows</x:String>
      <x:String>System.Windows.Controls.Primitives</x:String>
      <x:String>System.Windows.Media.Animation</x:String>
      <x:String>System.Windows.Input</x:String>
      <x:String>System.Windows.Media</x:String>
      <x:String>System.Diagnostics</x:String>
      <x:String>System.Windows.Automation</x:String>
      <x:String>System.Windows.Media.TextFormatting</x:String>
      <x:String>System.Windows.Ink</x:String>
      <x:String>System.Windows.Media.Effects</x:String>
      <x:String>System.Windows.Media.Imaging</x:String>
      <x:String>System.Windows.Media.Media3D</x:String>
      <x:String>System.Windows.Controls.Ribbon</x:String>
      <x:String>System.Windows.Forms.Integration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.HTTPRequests</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.Activities.Web</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Windows.Controls.Ribbon</AssemblyReference>
      <AssemblyReference>WindowsFormsIntegration</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.HTTPRequests</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.Activities.Web</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_7">
    <Assign sap2010:WorkflowViewState.IdRef="Assign_1">
      <Assign.To>
        <OutArgument x:TypeArguments="x:Boolean">[invoiceExist]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:Boolean">False</InArgument>
      </Assign.Value>
    </Assign>
    <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_1">
      <TryCatch.Try>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_6">
          <Sequence.Variables>
            <Variable x:TypeArguments="x:String" Name="sinoReq" />
            <Variable x:TypeArguments="x:Int32" Name="invoiceCheckResponseCode" />
            <Variable x:TypeArguments="iru:ResponseObject" Name="invoiceResponse" />
          </Sequence.Variables>
          <Assign DisplayName="SINO Assign" sap2010:WorkflowViewState.IdRef="Assign_2">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[sinoReq]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["SINO : '"+SupInvoiceNo+"' AND DIVI : '" +division + "'" + " AND YEA4 : " + AYear + " AND SPYN : '" + vendorID + "'"]</InArgument>
            </Assign.Value>
          </Assign>
          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Invoice exists in APS200 IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_1" Response="[invoiceResponse]" StatusCode="[invoiceCheckResponseCode]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS200MI/SearchSupInvoic?dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
            <iai:IONAPIRequestWizard.Headers>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>Accept</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>application/json</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.Headers>
            <iai:IONAPIRequestWizard.QueryParameters>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>SQRY</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>sinoReq</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.QueryParameters>
          </iai:IONAPIRequestWizard>
          <If Condition="[invoiceCheckResponseCode = 200]" DisplayName="If Invoice available in APS200" sap2010:WorkflowViewState.IdRef="If_2">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_3">
                <If Condition="[not invoiceResponse.readasjson(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" DisplayName="If Invoice available in APS200" sap2010:WorkflowViewState.IdRef="If_1">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_2">
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_1">
                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Invoice available in APS200 Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="[&quot;Invoice number &quot;+SupInvoiceNo+&quot; available in APS200&quot;]" Source="[logfile]" />
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_3">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Boolean">[invoiceExist]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                          </Assign.Value>
                        </Assign>
                      </Sequence>
                    </Sequence>
                  </If.Then>
                </If>
              </Sequence>
            </If.Then>
          </If>
          <If Condition="[not invoiceExist]" sap2010:WorkflowViewState.IdRef="If_5">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_5">
                <Sequence.Variables>
                  <Variable x:TypeArguments="x:String" Name="checkInvoiceSqry" />
                  <Variable x:TypeArguments="iru:ResponseObject" Name="checkInvoiceExportMIResponse" />
                  <Variable x:TypeArguments="x:Int32" Name="checkInvoiceExportMIResponseCode" />
                  <Variable x:TypeArguments="x:String" Name="ivdate" />
                  <Variable x:TypeArguments="x:String" Name="AYear1" />
                </Sequence.Variables>
                <Assign DisplayName="SINO queryAssign" sap2010:WorkflowViewState.IdRef="Assign_4">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[checkInvoiceSqry]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["E5IVDT,E5INBN from FAPIBH where E5SINO = '"+SupInvoiceNo+"'" + " and E5DIVI = '" + division + "'" + " and E5SUNO = '" + vendorID + "'"]</InArgument>
                  </Assign.Value>
                </Assign>
                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Invoice available in APS450 IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_2" Response="[checkInvoiceExportMIResponse]" StatusCode="[checkInvoiceExportMIResponseCode]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/EXPORTMI/Select?SEPC=~&amp;HDRS=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
                  <iai:IONAPIRequestWizard.QueryParameters>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>QERY</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>checkInvoiceSqry</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.QueryParameters>
                </iai:IONAPIRequestWizard>
                <If Condition="[checkInvoiceExportMIResponseCode=200]" DisplayName="Invoice available in APS450 If" sap2010:WorkflowViewState.IdRef="If_4">
                  <If.Then>
                    <If Condition="[not checkInvoiceExportMIResponse.readasjson(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_3">
                      <If.Then>
                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_4">
                          <Sequence.Variables>
                            <Variable x:TypeArguments="x:String" Name="InYear" />
                            <Variable x:TypeArguments="iru:ResponseObject" Name="respForFiscalYear" />
                            <Variable x:TypeArguments="x:Int32" Name="respForFiscalYearCode" />
                          </Sequence.Variables>
                          <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_1">
                            <iad:CommentOut.Activities>
                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_11">
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_6">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[ivdate]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[checkInvoiceExportMIResponse.readasjson("results")(0)("records")(0)("REPL").ToString().split("~"C)(0).ToString]</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_7">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[InYear]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[ivdate.SubString(0,4)]</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Get Fiscal year IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_3" Response="[respForFiscalYear]" StatusCode="[respForFiscalYearCode]" Url="[tenantID+&quot;M3/m3api-rest/v2/execute/CRS900MI/GetSysCalDate&quot;]">
                                  <iai:IONAPIRequestWizard.Headers>
                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                        <x:String>Accept</x:String>
                                      </scg:List>
                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                        <x:String>application/json</x:String>
                                      </scg:List>
                                    </scg:List>
                                  </iai:IONAPIRequestWizard.Headers>
                                  <iai:IONAPIRequestWizard.QueryParameters>
                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                        <x:String>YMD8</x:String>
                                        <x:String>DIVI</x:String>
                                      </scg:List>
                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                        <x:String>ivdate</x:String>
                                        <x:String>division</x:String>
                                      </scg:List>
                                    </scg:List>
                                  </iai:IONAPIRequestWizard.QueryParameters>
                                </iai:IONAPIRequestWizard>
                                <If Condition="[respForFiscalYearCode = 200]" sap2010:WorkflowViewState.IdRef="If_7">
                                  <If.Then>
                                    <If Condition="[respForFiscalYear.ReadAsJson(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_6">
                                      <If.Then>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_8">
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_8">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[AYear1]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[ivdate.SubString(0,4)]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                        </Sequence>
                                      </If.Then>
                                      <If.Else>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_9">
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_9">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[AYear1]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[respForFiscalYear.ReadAsJson("results")(0)("records")(0)("CYP1").ToString.Substring(0,4)]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                        </Sequence>
                                      </If.Else>
                                    </If>
                                  </If.Then>
                                </If>
                                <If Condition="[AYear1 = AYear]" sap2010:WorkflowViewState.IdRef="If_8">
                                  <If.Then>
                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_10">
                                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Invoice available in APS450 Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_2" Line="[&quot;Invoice number &quot;+SupInvoiceNo+&quot; available in APS450&quot;]" Source="[logfile]" />
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_5">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:Boolean">[invoiceExist]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                    </Sequence>
                                  </If.Then>
                                </If>
                              </Sequence>
                            </iad:CommentOut.Activities>
                          </iad:CommentOut>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_12">
                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Invoice available in APS450 Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_3" Line="[&quot;Invoice number &quot;+SupInvoiceNo+&quot; available in APS450&quot;]" Source="[logfile]" />
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_10">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Boolean">[invoiceExist]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                              </Assign.Value>
                            </Assign>
                          </Sequence>
                        </Sequence>
                      </If.Then>
                    </If>
                  </If.Then>
                </If>
              </Sequence>
            </If.Then>
          </If>
        </Sequence>
      </TryCatch.Try>
      <TryCatch.Catches>
        <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_1">
          <ActivityAction x:TypeArguments="s:Exception">
            <ActivityAction.Argument>
              <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
            </ActivityAction.Argument>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_11">
              <Assign.To>
                <OutArgument x:TypeArguments="x:Boolean">[invoiceExist]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:Boolean">False</InArgument>
              </Assign.Value>
            </Assign>
          </ActivityAction>
        </Catch>
      </TryCatch.Catches>
    </TryCatch>
    <sads:DebugSymbol.Symbol>d19DOlxVc2Vyc1xkbWl0dGFwYWxsaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYyXFZlcmlmeUludm9pY2VFeGlzdHMueGFtbDkB5AEB6AEBBgH+AQGDAgEFAcIBAcsBAQQBnAIBpQIBAwFuAaUBAQJjA9QCDgIBAWQFaw4CAUtsBdICEAIBAmkxaTYCAU5mMmZAAgFMbgm/AhQCAQfHAg3OAhYCAQN0C3sUAgFDfAuRASUCATySAQuoARACASupAQu+AhACAQjMAjnMAj4CAQbJAjrJAkgCAQR5NnmsAQIBRnY3dkACAUR8qQJ8vAICAUF86QJ8mgQCAT98yAJ85AICAT2SARmSATsCASyUAQ+mARoCAS6pARmpAS0CAQmrAQ+8AhoCAQuVARGlARYCAS+zARG6ARoCASS7ARHGASsCAR3HARG7AhYCAQyVAR+VAYgBAgEwlwEVowEgAgExuAE8uAHFAQIBJ7UBPbUBTwIBJbsBxQK7AeUCAgEiuwGaA7sB2AQCASC7AfECuwGVAwIBHscBH8cBRwIBDckBFbkCGgIBD5gBF6IBIgIBMskBI8kBmQECARDLARm3AiQCARGZARmZAaQCAgE3mgEZoQEiAgEz0QEbqwIsAgEcrAIbtgImAgESmQHBAZkBjgICATqZAZYCmQGhAgIBOJ8BRZ8BSQIBNpwBRpwBVAIBNK0CHa0CqAICAReuAh21AiYCAROtAsUBrQKSAgIBGq0CmgKtAqUCAgEYswJJswJNAgEWsAJKsAJYAgEU</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="779,60" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="737,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_1" sap:VirtualizedContainerService.HintSize="737,22" />
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="264,246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="286,370">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="464,518" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="486,642">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="737,790" />
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="590,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_2" sap:VirtualizedContainerService.HintSize="590,22" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="678,60" />
      <sap2010:ViewStateData Id="Assign_7" sap:VirtualizedContainerService.HintSize="678,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_3" sap:VirtualizedContainerService.HintSize="678,22" />
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_8" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_9" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_6" sap:VirtualizedContainerService.HintSize="553,334" />
      <sap2010:ViewStateData Id="If_7" sap:VirtualizedContainerService.HintSize="678,484" />
      <sap2010:ViewStateData Id="Append_Line_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_10" sap:VirtualizedContainerService.HintSize="264,246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_8" sap:VirtualizedContainerService.HintSize="678,396" />
      <sap2010:ViewStateData Id="Sequence_11" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_1" sap:VirtualizedContainerService.HintSize="264,148.666666666667" />
      <sap2010:ViewStateData Id="Append_Line_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_12" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_4" sap:VirtualizedContainerService.HintSize="286,560.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="464,714.666666666667" />
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="590,868.666666666667" />
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="612,1155">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_5" sap:VirtualizedContainerService.HintSize="737,1303" />
      <sap2010:ViewStateData Id="Sequence_6" sap:VirtualizedContainerService.HintSize="759,2419">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_11" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_1" sap:VirtualizedContainerService.HintSize="764.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_1" sap:VirtualizedContainerService.HintSize="779,2647">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="801,2871">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="841,3031" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>